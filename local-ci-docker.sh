#!/bin/bash

# 完整的本地 GitLab CI 执行脚本
# 包含测试、构建和 Docker 镜像构建

set -e

echo "🚀 开始完整的本地 GitLab CI 流程..."

# 设置环境变量
export BROWSER_OUTPUT_FOLDER="./public"
export CI_COMMIT_REF_NAME=$(git branch --show-current)
export CI_COMMIT_SHA=$(git rev-parse HEAD)
export CI_PROJECT_NAME="fevideo"
export CI_PROJECT_PATH="fe/fevideo"

# 设置 Docker 相关变量（如果需要）
export REGISTRY_NAME="docker-reg.devops.xiaohongshu.com"
export REGISTRY_USERNAME="${REGISTRY_USERNAME:-test}"
export REGISTRY_PASSWORD="${REGISTRY_PASSWORD:-test}"

echo "📋 当前分支: $CI_COMMIT_REF_NAME"
echo "📋 当前提交: $CI_COMMIT_SHA"
echo "📋 项目名称: $CI_PROJECT_NAME"

# 检查必要工具
echo "🔧 检查必要工具..."
node -v
formula -V
npm -v

# 检查 Docker 是否可用
if command -v docker &> /dev/null; then
    echo "🐳 Docker 已安装"
    docker -v
    DOCKER_AVAILABLE=true
else
    echo "⚠️  Docker 未安装，将跳过 Docker 构建步骤"
    echo "💡 如需 Docker 构建，请安装 Docker Desktop: https://www.docker.com/products/docker-desktop"
    DOCKER_AVAILABLE=false
fi

# 安装依赖
echo "📦 安装依赖..."
npm install --no-optional --legacy-peer-deps

# 执行测试阶段
echo "🧪 执行测试阶段..."
if [[ "$CI_COMMIT_REF_NAME" != "master" ]]; then
    echo "执行测试..."
    formula test
else
    echo "跳过测试（master 分支）"
fi

# 执行构建阶段
echo "🏗️ 执行构建阶段..."

if [[ "$CI_COMMIT_REF_NAME" == "develop" ]]; then
    echo "构建测试环境版本..."
    formula build -e test
elif [[ "$CI_COMMIT_REF_NAME" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "构建生产环境版本..."
    formula build
else
    echo "构建特性分支版本..."
    formula build -e test
fi

# 执行 Docker 构建阶段
echo "🐳 执行 Docker 构建阶段..."

if [[ "$DOCKER_AVAILABLE" == "true" ]] && ([[ -n "$CI_COMMIT_TAG" ]] || [[ "$CI_COMMIT_REF_NAME" != "master" ]]); then
    echo "构建 Docker 镜像..."
    
    # 构建镜像
    docker build -t $REGISTRY_NAME/$CI_PROJECT_PATH:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8} .
    
    echo "✅ Docker 镜像构建成功！"
    echo "📦 镜像标签: $REGISTRY_NAME/$CI_PROJECT_PATH:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8}"
    
    # 清理镜像（可选）
    # docker rmi $REGISTRY_NAME/$CI_PROJECT_PATH:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8}
else
    if [[ "$DOCKER_AVAILABLE" == "false" ]]; then
        echo "跳过 Docker 构建（Docker 未安装）"
    else
        echo "跳过 Docker 构建（master 分支且无标签）"
    fi
fi

echo "✅ 完整的本地 CI 执行完成！"
echo "📁 构建输出目录: $BROWSER_OUTPUT_FOLDER"
if [[ "$DOCKER_AVAILABLE" == "true" ]]; then
    echo "🐳 Docker 镜像已构建完成"
fi 