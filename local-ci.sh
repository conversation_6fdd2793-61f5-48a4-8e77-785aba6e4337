#!/bin/bash

# 本地执行 GitLab CI 脚本
# 模拟 .gitlab-ci.yml 中的构建流程

set -e

echo "🚀 开始本地执行 GitLab CI 流程..."

# 设置环境变量
export BROWSER_OUTPUT_FOLDER="./public"
export CI_COMMIT_REF_NAME=$(git branch --show-current)
export CI_COMMIT_SHA=$(git rev-parse HEAD)
export CI_PROJECT_NAME="fevideo"

echo "📋 当前分支: $CI_COMMIT_REF_NAME"
echo "📋 当前提交: $CI_COMMIT_SHA"

# 检查必要工具
echo "🔧 检查必要工具..."
node -v
formula -V
npm -v

# 安装依赖
echo "📦 安装依赖..."
npm install --no-optional --legacy-peer-deps

# 执行测试阶段
echo "🧪 执行测试阶段..."
if [[ "$CI_COMMIT_REF_NAME" != "master" ]]; then
    echo "执行测试..."
    formula test
else
    echo "跳过测试（master 分支）"
fi

# 执行构建阶段
echo "🏗️ 执行构建阶段..."

if [[ "$CI_COMMIT_REF_NAME" == "develop" ]]; then
    echo "构建测试环境版本..."
    formula build -e test
elif [[ "$CI_COMMIT_REF_NAME" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "构建生产环境版本..."
    formula build
else
    echo "构建特性分支版本..."
    formula build -e test
fi

echo "✅ 本地 CI 执行完成！"
echo "📁 构建输出目录: $BROWSER_OUTPUT_FOLDER" 