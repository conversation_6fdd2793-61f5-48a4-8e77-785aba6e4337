image: docker-reg.devops.xiaohongshu.com/fe/fe-ci:$FE_CI_IMAGE_LATEST

variables:
  BROWSER_OUTPUT_FOLDER: "./public"
  REMOTES_FOLDER: "/data/formula-static/$CI_PROJECT_NAME"
  NODE_VERSION: "14.21.3"

before_script:
  - node -v
  - formula -V
  - npm i

.cache: &common_cache
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/
  policy: pull-push

.artifacts: &common_artifacts
  expire_in: 10 mins
  paths:
    - $BROWSER_OUTPUT_FOLDER

.job_template: &build_definition
  stage: build
  cache:
    <<: *common_cache
    policy: pull
  artifacts:
    <<: *common_artifacts
  allow_failure: false

.job_template: &dockwalloper_definition
  stage: dockwalloper
  image: docker-reg.devops.xiaohongshu.com/fe/dockwalloper:latest
  before_script:
    - node -v
    - dockwalloper -v
  script:
    - dockwalloper carry -p ./ -d $CI_PROJECT_URL -r $CI_COMMIT_REF_NAME -u $FE_CI_WIKI_GROUP -w $FE_CI_WIKI_PASSWORD

stages:
  - test
  - build
  - deploy
  - dockwalloper

test:
  stage: test
  script:
    # - formula lint -p
    - formula test
  cache:
    <<: *common_cache
  except:
    - master

build:feature:
  <<: *build_definition
  script:
    - formula build -e test
  only:
    - branches
  except:
    - develop
    - master
  # when: manual

build:test:
  <<: *build_definition
  script:
    - formula build -e test
  only:
    - develop

build:production:
  <<: *build_definition
  script:
    - formula build
  only:
    - /^v\d+\.\d+\.\d+$/
  except:
    - branches

deploy:static-and-image:
  stage: deploy
  cache:
    <<: *common_cache
    policy: pull
  before_script:
    - node -v
    - formula -V
    - docker -v
    - npm i
  script:
    - "if [[ $CI_COMMIT_TAG != '' ]]; then formula deploy -s $BROWSER_OUTPUT_FOLDER -d $REMOTES_FOLDER; fi" # deploy static
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY_NAME # deploy image
    - docker build -t $REGISTRY_NAME/$CI_PROJECT_PATH:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8} .
    - docker push $REGISTRY_NAME/$CI_PROJECT_PATH:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8}
    - docker rmi $REGISTRY_NAME/$CI_PROJECT_PATH:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8}
  only:
    - branches
    - /^v\d+\.\d+\.\d+$/
  except:
    - master

dockwalloper:tag:
  <<: *dockwalloper_definition
  only:
    - /^v\d+\.\d+\.\d+$/
    - /^v\d+\.\d+\.\d+-\d+$/

dockwalloper:manual:
  <<: *dockwalloper_definition
  when:
      manual
  only:
    - branches
