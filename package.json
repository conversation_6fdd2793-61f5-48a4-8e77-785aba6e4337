{"name": "fevideo", "version": "1.0.0", "description": "音视频测试平台", "main": "src/index.js", "engines": {"node": ">=14.21.3", "npm": ">=6.14.18"}, "repository": {"type": "git", "url": "*******************************:fe/fevideo.git"}, "scripts": {"preinstall": "[ -d ./.hooks ] && (git config --add core.hooksPath ./.hooks) || ([ -d ./.git ] && formula githooks || echo 'Please run git init')"}, "author": "宁焕君(阿翡) <<EMAIL>>", "license": "ISC", "dependencies": {"@types/xlsx": "0.0.36", "@xhs/delight": "1.1.1", "@xhs/delight-charts": "^0.1.94", "@xhs/launcher": "latest", "@xhs/launcher-plugin-auth": "^2.2.1-6", "@xhs/launcher-plugin-eaglet": "latest", "@xhs/launcher-plugin-store": "latest", "@xhs/uploader": "^3.0.2", "@xhs/yam-beer": "^5.9.4", "@xhs/yam-layout": "^0.9.13", "cos-js-sdk-v5": "^1.8.7", "downloadjs": "^1.4.7", "echarts": "^5.4.0", "echarts-wordcloud": "^2.1.0", "file-saver": "^2.0.5", "json-editor-vue3": "^1.0.6", "jszip": "^3.10.1", "vue-json-viewer": "^3.0.4", "vue-simple-uploader": "^1.0.0-beta.5", "vue3-ace-editor": "^2.2.2", "vue3-video-play": "^1.3.1-beta.6", "xlsx": "^0.18.5"}, "private": true, "devDependencies": {"vue-picture-cropper": "^0.5.1"}}