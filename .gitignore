############################
# NPM files
############################
**/node_modules
**/bower_components
node_modules
bower_components
npm-debug.log*
.node_repl_history
.node_history
.lock-wscript
.npm
package-lock.json


############################
# Temp files
############################
tramp
temp
tmp
.tmp
*.swo
*.swp
*.swn
*.swm
*.diff
*.log
*.patch
*.bak
*.log
*.iml
*.ipr
*.iws
*.out
*.gz
*#
*~
~*
.\#*
.cache
**/.cache
.hooks


############################
# Editor & OS files
############################
.idea
.DS_STORE
.DS_Store
.Trashes
.project
.rebooted
.*proj
Thumbs.db
ehthumbs.db
Icon?
nbproject
.cask/
.pydevproject
.settings/
.vscode

/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
/eshell/history
/eshell/lastdir
/elpa/
/auto/
*_flymake.*
.org-id-locations
*_archive
.imdone


############################
# Report files
############################
coverages
coverage
reports
report
lib-cov
html-report
JSCover
nohup.out
out
logs
log
*.log
*.log.*
.nyc_output


############################
# Other files
############################
pids
*.pid
*.rel
*.seed
.grunt
.svn
__pycache__/
*.py[cod]
*$py.class
.picasso


############################
# Build
############################
dist
*_dist
public
public_server


############################
# By Project
############################
