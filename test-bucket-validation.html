<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bucket 校验测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 300px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .test-button {
            padding: 8px 16px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 600px;
            border-radius: 5px;
        }
        .modal-header {
            color: #ff4d4f;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .modal-body {
            margin-bottom: 16px;
        }
        .error-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #d9d9d9;
            padding: 12px;
            border-radius: 4px;
            background-color: #fafafa;
        }
        .error-item {
            margin-bottom: 8px;
            padding: 8px;
            background-color: #fff2f0;
            border-left: 3px solid #ff4d4f;
            color: #cf1322;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <h1>Bucket 字段校验测试</h1>
    
    <div class="test-section">
        <h3>单个 Bucket 校验测试</h3>
        <input type="text" id="singleBucket" class="test-input" placeholder="输入 bucket 值进行测试">
        <button class="test-button" onclick="testSingleBucket()">测试单个 Bucket</button>
        <div id="singleResult"></div>
    </div>

    <div class="test-section">
        <h3>批量 Bucket 校验测试</h3>
        <p>测试数据（每行一个 bucket 值）：</p>
        <textarea id="batchBuckets" rows="6" style="width: 100%; padding: 8px;">
valid-bucket-123
invalid bucket with spaces
another-valid-bucket
invalid@bucket#with$symbols
valid123
empty-line-below

invalid.bucket.with.dots
valid-bucket-name
</textarea>
        <button class="test-button" onclick="testBatchBuckets()">测试批量 Bucket</button>
        <div id="batchResult"></div>
    </div>

    <!-- 错误弹窗 -->
    <div id="errorModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div class="modal-header" id="modalTitle">Bucket格式错误</div>
            <div class="modal-body">
                <div id="modalMessage"></div>
                <div><strong>格式要求：</strong>bucket字段只能包含数字(0-9)、字母(a-z, A-Z)、连字符(-)</div>
                <div class="error-list" id="errorList"></div>
            </div>
        </div>
    </div>

    <script>
        // Bucket 校验函数（从 Vue 组件中提取）
        function validateBucket(value) {
            if (!value || value.trim() === '') {
                return { status: 'error', message: 'bucket is required', type: 'empty' };
            }
            // 只能包含数字(0-9)、字母(a-z, A-Z)、连字符(-)
            const bucketRegex = /^[a-zA-Z0-9-]+$/;
            if (!bucketRegex.test(value)) {
                return { status: 'error', message: 'bucket格式错误：只能包含数字(0-9)、字母(a-z, A-Z)、连字符(-)', type: 'format' };
            }
            return { status: 'success', message: 'bucket格式正确' };
        }

        // 测试单个 bucket
        function testSingleBucket() {
            const input = document.getElementById('singleBucket');
            const result = document.getElementById('singleResult');
            const bucketValue = input.value;
            
            const validation = validateBucket(bucketValue);
            
            if (validation.status === 'success') {
                result.innerHTML = `<div class="result success">${validation.message}</div>`;
            } else {
                result.innerHTML = `<div class="result error">${validation.message}</div>`;
            }
        }

        // 测试批量 bucket
        function testBatchBuckets() {
            const textarea = document.getElementById('batchBuckets');
            const result = document.getElementById('batchResult');
            const bucketValues = textarea.value.split('\n').filter(line => line.trim() !== '');
            
            const invalidBuckets = [];
            const emptyBuckets = [];
            const validBuckets = [];
            
            bucketValues.forEach((bucket, index) => {
                const validation = validateBucket(bucket);
                if (validation.status === 'error') {
                    if (validation.type === 'empty') {
                        emptyBuckets.push({ bucket, index: index + 1 });
                    } else {
                        invalidBuckets.push({ bucket, index: index + 1 });
                    }
                } else {
                    validBuckets.push({ bucket, index: index + 1 });
                }
            });
            
            let resultHtml = `<div class="result success">有效的 bucket: ${validBuckets.length} 个</div>`;
            
            if (emptyBuckets.length > 0) {
                resultHtml += `<div class="result error">空值 bucket: ${emptyBuckets.length} 个</div>`;
                showErrorModal(emptyBuckets, '批量测试', 'empty');
                return;
            }
            
            if (invalidBuckets.length > 0) {
                resultHtml += `<div class="result error">格式错误的 bucket: ${invalidBuckets.length} 个</div>`;
                showErrorModal(invalidBuckets, '批量测试', 'format');
                return;
            }
            
            result.innerHTML = resultHtml;
        }

        // 显示错误弹窗
        function showErrorModal(errorItems, context, type) {
            const modal = document.getElementById('errorModal');
            const title = document.getElementById('modalTitle');
            const message = document.getElementById('modalMessage');
            const errorList = document.getElementById('errorList');
            
            if (type === 'empty') {
                title.textContent = 'Bucket字段缺失';
                message.innerHTML = `在${context}过程中发现${errorItems.length}条数据的bucket字段为空！<br><strong>要求：</strong>bucket字段是必填项，不能为空`;
                
                errorList.innerHTML = '<div style="font-weight: bold; margin-bottom: 8px;">缺失数据列表：</div>' +
                    errorItems.map(item => 
                        `<div class="error-item">第${item.index}条：bucket字段为空</div>`
                    ).join('');
            } else {
                title.textContent = 'Bucket格式错误';
                message.innerHTML = `在${context}过程中发现${errorItems.length}条数据的bucket格式错误！`;
                
                errorList.innerHTML = '<div style="font-weight: bold; margin-bottom: 8px;">错误数据列表：</div>' +
                    errorItems.map(item => 
                        `<div class="error-item">第${item.index}条：bucket = "${item.bucket}"</div>`
                    ).join('');
            }
            
            modal.style.display = 'block';
        }

        // 关闭弹窗
        function closeModal() {
            document.getElementById('errorModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('errorModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 回车键测试
        document.getElementById('singleBucket').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testSingleBucket();
            }
        });
    </script>
</body>
</html>
