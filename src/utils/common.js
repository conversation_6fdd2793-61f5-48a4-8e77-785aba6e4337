export const videoDynamicList = ['8', '10']
export const audioSampleRateList = ['32', '44.1', '48']
export const commonTypeList = [
  {
    label: '正常',
    value: '0',
  },
  {
    label: '高',
    value: '1',
  },
  {
    label: '低',
    value: '2',
  },
]
export const isMovePictureList = [
  {
    label: '否',
    value: '0',
  },
  {
    label: '是',
    value: '1',
  },
]

export const colorTypeList = [
  {
    label: '正常',
    value: '0',
  },
  {
    label: '颜色丰富',
    value: '1',
  },
  {
    label: '颜色不丰富',
    value: '2',
  },
]

export const isVolumeList = ['case1', 'case2', 'case3', 'case4', 'case5', 'case6', 'case7', 'case8', 'case9']

export const cvsceneTypeList = ['宝宝', '自拍', '穿搭', '健身', '人-其他']
export const shotsTypeList = ['特写', '近景', '中景', '全景', '远景']
export const postureTypeList = ['站', '卧', '坐']
export const peopleNumList = ['无', '1人', '2人', '多人(3-5)', '密集']

export const lightTypeList = [
  {
    label: '正常',
    value: '0',
  },
  {
    label: '强光',
    value: '1',
  },
  {
    label: '暗光',
    value: '2',
  },
  {
    label: '逆光',
    value: '3',
  },
]

export const taskTypeList = [
  {
    label: '任务编排',
    value: '1',
  },
  // {
  //   label: 'capa',
  //   value: '2',
  // },
  // {
  //   label: '人体关键点',
  //   value: '3',
  // },
  // {
  //   label: '3dphoto',
  //   value: '4',
  // },
  // {
  //   label: '主体分割',
  //   value: '5',
  // },
  // {
  //   label: '路人分割',
  //   value: '6',
  // },
  {
    label: '子工作流',
    value: '7',
  },
  {
    label: '质检工作流',
    value: '8',
  },
]

export const autoEnableOption = [
  {
    label: 'NO',
    value: '0',
  },
  {
    label: 'YES',
    value: '1',
  },
  {
    label: 'PAUSE',
    value: '2',
  },
]

export const setTypeList = [
  {
    label: '音视频',
    value: '1',
  },
  {
    label: '智能算法',
    value: '2',
  },
  {
    label: '视频线上产物',
    value: '3',
  },
]

export const avstatusOptions = [
  {
    label: '成功',
    value: 'COMPLETED',
  },
  {
    label: '失败',
    value: 'FAILED',
  },
  {
    label: '运行中',
    value: 'RUNNING',
  },
  {
    label: '超时',
    value: 'TIMED_OUT',
  },
]

export const cvstatusOptions = [
  {
    label: '成功',
    value: '1',
  },
  {
    label: '未知错误',
    value: '0',
  },
  {
    label: '未检测到主体',
    value: '2',
  },
  {
    label: '下载图片失败',
    value: '3',
  },
  {
    label: '上传图片失败',
    value: '4',
  },
  {
    label: '算法处理失败',
    value: '5',
  },
  {
    label: '其他错误',
    value: '6',
  },
]

export const AvsetType = [
  '1', // 音视频
  '3', // 视频线上产物
]
export const AvtaskType = [
  '1', // 任务编排
  '7', // 子工作流
  '8', // 质检工作流
]
export const CvsetType = [
  '2', // 智能算法
]
export const CvtaskType = [
  '3', // 人体关键点
  '4', // 3dphoto
  '5', // 主体分割
  '6', // 路人分割
]

export const taskRecordStatusOptions = [
  {
    label: '执行成功',
    value: '成功',
    color: 'green',
  },
  {
    label: '执行失败',
    value: '失败',
    color: 'orange',
  },
  {
    label: '执行失败',
    value: '-1',
    color: 'orange',
  },
  {
    label: '执行失败',
    value: '-2',
    color: 'orange',
  },
  {
    label: '执行失败',
    value: '-3',
    color: 'orange',
  },
  {
    label: '执行失败',
    value: '-10',
    color: 'orange',
  },
  {
    label: '执行中',
    value: '1',
    color: 'blue',
  },
  {
    label: '等待',
    value: '等待',
    color: 'blue',
  },
  {
    label: '等待',
    value: '0',
    color: 'blue',
  },
  {
    label: '查询中',
    value: '2',
    color: 'blue',
  },
  {
    label: '执行成功',
    value: '3',
    color: 'green',
  },
  {
    label: '收数中',
    value: '4',
    color: 'blue',
  },
  {
    label: '收数成功',
    value: '5',
    color: 'green',
  },
  {
    label: '收数失败',
    value: '6',
    color: 'orange',
  },
]

export const mergestatusOptions = [
  {
    label: '成功',
    value: '成功',
  },
  {
    label: '失败',
    value: '失败',
  },
  {
    label: '运行中',
    value: '运行中',
  },
]

export const pageSizeOptions = [10, 20, 50, 100, 200]

export function isIndexOfArray(arr, value) {
  for (let i = 0; i < arr.length; i++) {
    if (value.indexOf(arr[i]) !== -1) {
      return true
    }
  }
  return false
}

// 根据map value值获取label值
export function getMapLable(objArr, v) {
  const newObj = {}
  objArr.forEach(item => {
    newObj[item.value] = item.label
  })
  return newObj[v]
}

// 获取当前时间
export function getCurrentDate() {
  const date = new Date()
  const seperator1 = '-'
  const year = date.getFullYear()
  let month = date.getMonth() + 1
  let strDate = date.getDate()
  if (month >= 1 && month <= 9) {
    month = `0${month}`
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = `0${strDate}`
  }
  const currentdate = year + seperator1 + month + seperator1 + strDate
  return currentdate
}

// 获取N天前的时间
export function beforeDays(num) {
  // 设置日期，当前日期的前num天
  const myDate = new Date() // 获取今天日期
  myDate.setDate(myDate.getDate() - (num - 1))
  const dateArray = []
  let myMonth = myDate.getMonth() + 1
  if (myMonth >= 1 && myMonth <= 9) {
    myMonth = `0${myMonth}`
  }
  let myDates = myDate.getDate()
  if (myDates >= 0 && myDates <= 9) {
    myDates = `0${myDates}`
  }
  let dateTemp
  const flag = 1
  for (let i = 0; i < num; i++) {
    dateTemp = `${myDate.getFullYear()}-${myMonth}-${myDates}`
    dateArray.push(dateTemp)
    myDate.setDate(myDate.getDate() + flag)
  }
  return dateArray[0]
}
