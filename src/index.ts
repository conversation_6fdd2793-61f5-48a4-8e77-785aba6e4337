import Launcher from '@xhs/launcher'
import authPlugin from '@xhs/launcher-plugin-auth'

import storePlugin from '@xhs/launcher-plugin-store'
import eagletPlugin from '@xhs/launcher-plugin-eaglet'
import httpConfig from './config/http.config'
import routes from './config/routes.config'

import storeConfig from './store/index'

import '@xhs/delight/style/index.styl'

const app = new Launcher('#app', {
  routes,
  http: httpConfig,
})

app.use(authPlugin, {
  scheme: {
    type: 'internalSSO',
    subsystemAlias: 'va', // 请使用自己子系统别名，不重复即可，可联系SSO服务号确认是否重复
  },
})

app.use(storePlugin, storeConfig)
app.use(eagletPlugin, {
  // tracker: () => import('@xhs/protobuf-fevideo-tracker'),
})
app.start()

export default app
