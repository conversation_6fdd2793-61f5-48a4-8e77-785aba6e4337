<!-- ignore eslint -->
<template>
  <BeerPanel :bs="{ boxShadow: 'none', flex: 1, marginTop: 20, pt: 0 }">
    <BeerTitle :bs="{ mb: 16 }"> 转码测试 </BeerTitle>
    <BeerDivider />
    <br>
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center', flexWrap: 'wrap', gap: '10px' }">
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">笔记ID:</Text>
        <Input
          v-model="state.noteId"
          :style="{ width: '200px' }"
          clearable
          placeholder="请输入笔记ID"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">状态:</Text>
        <Select
          v-model="state.status"
          :options="statusOptions"
          :style="{ width: '130px' }"
          clearable
          placeholder="请选择状态"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">视频ID:</Text>
        <Input
          v-model="state.videoId"
          :style="{ width: '200px' }"
          clearable
          placeholder="请输入视频ID"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">文件ID:</Text>
        <Input
          v-model="state.file_id"
          :style="{ width: '300px' }"
          clearable
          placeholder="请输入文件ID"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">环境:</Text>
        <Select
          v-model="state.env"
          :options="envOptions"
          :style="{ width: '120px' }"
          clearable
          placeholder="请选择环境"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">场景类型:</Text>
        <Select
          v-model="state.sceneType"
          :options="sceneTypeOptions"
          :style="{ width: '120px' }"
          clearable
          placeholder="请选择场景类型"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">云类型:</Text>
        <Select
          v-model="state.cloudType"
          :options="cloudTypeOptions"
          :style="{ width: '120px' }"
          clearable
          placeholder="请选择云类型"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">媒体类型:</Text>
        <Select
          v-model="state.mediaType"
          :options="mediaTypeOptions"
          :style="{ width: '120px' }"
          clearable
          placeholder="请选择媒体类型"
        />
      </div>
      <div style="display: flex; align-items: center; margin-right: 10px;">
        <Text bold style="margin-right: 10px;">视频类型:</Text>
        <Select
          v-model="state.videoType"
          :options="videoTypeOptions"
          :style="{ width: '120px' }"
          clearable
          placeholder="请选择视频类型"
        />
      </div>
      <div style="display: flex; align-items: center;">
        <Button
          style="margin-right: 10px;"
          type="primary"
          :icon="{ icon: Search }"
          @click="searchTranscodeList()"
        >查询</Button>
        <Button
          style="margin-right: 10px;"
          type="secondary"
          :icon="{ icon: Clear }"
          @click="clearParam"
        >重置</Button>
        <Button
          style=" margin-left: 10px; margin-right: 10px;"
          type="primary"
          :style="{ backgroundColor: '#52c41a', borderColor: '#52c41a', color: '#fff' }"
          :icon="{ icon: Plus }"
          @click="handleAdd"
        >新增</Button>
      </div>
    </NewBsBox>
    <br>

    <Spinner
      :spinning="loading"
      tip="查询中"
      size="large"
    >
      <Table
        :columns="columns"
        :data-source="transcodeList"
        :pagination="false"
        :loading="loading"
        row-key="id"
      >
        <template #status="{ rowData }">
          <Tag :color="getStatusColor(rowData.status)">
            {{ rowData.status }}
          </Tag>
        </template>
        <template #checkResult="{ rowData }">
          <div style="max-width: 300px; word-break: break-all;">
            <div v-for="(status, key) in parseCheckResultStatus(rowData.checkResult)" :key="key" style="margin-bottom: 4px;">
              <Tag :color="getCheckStatusColor(status)">{{ key }}: {{ status }}</Tag>
            </div>
          </div>
        </template>
        <template #crateTime="{ rowData }">
          <Text size="small">{{ formatDateTime(rowData.crateTime) }}</Text>
        </template>
        <template #updateTime="{ rowData }">
          <Text size="small">{{ formatDateTime(rowData.updateTime) }}</Text>
        </template>
        <template #operation="{ rowData }">
          <Space>
            <Button
              size="small"
              type="primary"
              :icon="{ icon: Eyes }"
              @click="handleViewDetail(rowData)"
            >查看详情</Button>
            <Button
              size="small"
              type="primary"
              :style="{ backgroundColor: '#52c41a', borderColor: '#52c41a' }"
              :icon="{ icon: Refresh }"
              @click="handleCheck(rowData)"
            >检查</Button>
          </Space>
        </template>
      </Table>
    </Spinner>

    <NewBsBox :bs="{ display: 'flex', margin: 20, alignItems: 'center', justifyContent: 'flex-end' }">
      <Pagination
        v-model="state.page_num"
        :total="state.total"
        :page-size-options="pageSizeOptions"
        @update:modelValue="handlePagination"
        @update:pageSize="handlepageSize"
      />
    </NewBsBox>
  </BeerPanel>

  <!-- 详情弹窗 -->
  <Modal
    v-model:visible="detailModalVisible"
    title="转码测试详情"
    :with-footer="false"
    :style="{ width: '800px' }"
  >
    <div v-if="detailData" style="max-height: 600px; overflow-y: auto;">
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">ID</Text>
          <div>{{ detailData.id }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">笔记ID</Text>
          <div>{{ detailData.noteId }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">状态</Text>
          <div><Tag :color="getStatusColor(detailData.status)">{{ detailData.status }}</Tag></div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">视频ID</Text>
          <div>{{ detailData.videoId }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">文件ID</Text>
          <div style="word-break: break-all;">{{ detailData.file_id }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">环境</Text>
          <div>{{ detailData.env }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">场景类型</Text>
          <div>{{ detailData.sceneType }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">云类型</Text>
          <div>{{ detailData.cloudType }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">媒体类型</Text>
          <div>{{ detailData.mediaType }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">视频类型</Text>
          <div>{{ detailData.videoType }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">缩略图类型</Text>
          <div>{{ detailData.thumbTypes }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">流类型</Text>
          <div>{{ detailData.streamTypes }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">音频流类型</Text>
          <div>{{ detailData.audioStreamTypes }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">需要检查类型</Text>
          <div>{{ detailData.needCheckTypeList }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">创建时间</Text>
          <div>{{ formatDateTime(detailData.crateTime) }}</div>
        </div>
        <div style="background: #fafafa; padding: 12px; border-radius: 4px;">
          <Text bold size="small" style="color: #666;">更新时间</Text>
          <div>{{ formatDateTime(detailData.updateTime) }}</div>
        </div>
      </div>
      
      <Divider />
      <Text bold>检查结果详情:</Text>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">{{ formatJson(detailData.checkResult) }}</pre>
      
      <Divider />
      <Text bold>媒体详情:</Text>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">{{ formatJson(detailData.mediaDeatil) }}</pre>
    </div>
  </Modal>

  <!-- 新增弹窗 -->
  <Modal
    v-model:visible="addModalVisible"
    title="新增转码测试"
    :with-footer="false"
    :style="{ width: '900px' }"
  >
    <div style="max-height: 600px; overflow-y: auto;">
      <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
        <div>
          <Text bold size="small" style="color: #666;">笔记ID *</Text>
          <Input
            v-model="addFormData.noteId"
            placeholder="请输入笔记ID"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">视频ID *</Text>
          <Input
            v-model="addFormData.videoId"
            placeholder="请输入视频ID"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">视频类型</Text>
          <Select
            v-model="addFormData.videoType"
            :options="videoTypeOptions"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">环境</Text>
          <Select
            v-model="addFormData.env"
            :options="envOptions"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">场景类型</Text>
          <Select
            v-model="addFormData.sceneType"
            :options="sceneTypeOptions"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">云类型</Text>
          <Select
            v-model="addFormData.cloudType"
            :options="cloudTypeOptions"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">媒体类型</Text>
          <Select
            v-model="addFormData.mediaType"
            :options="mediaTypeOptions"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">抽帧类型</Text>
          <Input
            v-model="addFormData.thumbTypesListStr"
            placeholder="请输入抽帧类型，多个值用逗号分隔，如: 3,4,5"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">视频码流</Text>
          <Input
            v-model="addFormData.streamTypesStr"
            placeholder="请输入视频码流，多个值用逗号分隔，如: 258,259,260"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">音频档位</Text>
          <Input
            v-model="addFormData.audioStreamTypesStr"
            placeholder="请输入音频档位，多个值用逗号分隔，如: 0,1,2"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
        <div>
          <Text bold size="small" style="color: #666;">检查项目</Text>
          <Input
            v-model="addFormData.needCheckTypeListStr"
            placeholder="请输入检查项目，多个值用逗号分隔，如: video,audio,thumb,frame"
            style="margin-top: 4px; width: 100%;"
          />
        </div>
      </div>
      
      <Divider />
      <div style="display: flex; justify-content: flex-end; gap: 8px;">
        <Button @click="addModalVisible = false">取消</Button>
        <Button type="primary" @click="handleSubmitAdd">确定</Button>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import {
    useStore,
  } from 'vuex'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  import {
    Search, Clear, Eyes, Refresh, Plus,
  } from '@xhs/delight/icons'
  import {
    Select, Button, Text, Spinner, toast, Pagination, Input, Table, Space, Tag, Modal, Divider,
  } from '@xhs/delight'
  import {
    queryNoteMediaCheckRecord,
    checkNoteMediaCheckRecord,
    publishSingleNote,
  } from '../../services/task'

  const pageSizeOptions = [10, 20, 50, 100, 200]

  const store = useStore()

  const loading = ref(false)
  const transcodeList = ref([] as any[])
  const detailModalVisible = ref(false)
  const detailData = ref(null as any)
  const addModalVisible = ref(false)
  const addFormData = reactive({
    noteId: '',
    videoId: '',
    videoType: 'long',
    env: 'sit',
    sceneType: 'cn',
    cloudType: 'qc',
    mediaType: 'video', // livephoto,video
    thumbTypesList: [3],
    streamTypes: [258],
    audioStreamTypes: [0],
    need_check_type_list: ['video', 'audio', 'thumb', 'frame'],
    thumbTypesListStr: '3',
    streamTypesStr: '258',
    audioStreamTypesStr: '0',
    needCheckTypeListStr: 'video,audio,thumb,frame'
  })

  const statusOptions = [
    { label: 'PENDING', value: 'PENDING' },
    { label: 'RUNNING', value: 'RUNNING' },
    { label: 'COMPLETE', value: 'COMPLETE' },
    { label: 'FAILED', value: 'FAILED' },
  ]

  const envOptions = [
    { label: 'sit', value: 'sit' },
    { label: 'beta', value: 'beta' },
    { label: 'prod', value: 'prod' },
  ]

  const sceneTypeOptions = [
    { label: 'cn', value: 'cn' },
    { label: 'sg', value: 'sg' },
  ]

  const cloudTypeOptions = [
    { label: 'qc', value: 'qc' },
    { label: 'al', value: 'al' },
    { label: 'ros', value: 'ros' },
    { label: 'alsg', value: 'alsg' },
  ]

  const mediaTypeOptions = [
    { label: 'video', value: 'video' },
    { label: 'livephoto', value: 'livephoto' }
  ]

  const videoTypeOptions = [
    { label: '4khdrdobly', value: '4khdrdobly' },
    { label: 'long', value: 'long' },
  ]

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '笔记ID',
      dataIndex: 'noteId',
      key: 'noteId',
      width: 200,
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      width: 100,
    },
    {
      title: '视频ID',
      dataIndex: 'videoId',
      key: 'videoId',
      width: 150,
    },
    {
      title: '文件ID',
      dataIndex: 'file_id',
      key: 'file_id',
      width: 300,
    },
    {
      title: '环境',
      dataIndex: 'env',
      key: 'env',
      width: 80,
    },
    {
      title: '场景类型',
      dataIndex: 'sceneType',
      key: 'sceneType',
      width: 100,
    },
    {
      title: '云类型',
      dataIndex: 'cloudType',
      key: 'cloudType',
      width: 100,
    },
    {
      title: '媒体类型',
      dataIndex: 'mediaType',
      key: 'mediaType',
      width: 100,
    },
    {
      title: '视频类型',
      dataIndex: 'videoType',
      key: 'videoType',
      width: 100,
    },
    {
      title: '检查结果',
      key: 'checkResult',
      dataIndex: 'checkResult',
      width: 300,
    },
    {
      title: '创建时间',
      dataIndex: 'crateTime',
      key: 'crateTime',
      width: 180,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'operation',
      dataIndex: 'operation',
      width: 200,
      fixed: 'right' as const,
    },
  ] as any

  const state = reactive({
    noteId: '',
    status: '',
    videoId: '',
    file_id: '',
    env: '',
    sceneType: '',
    cloudType: '',
    mediaType: '',
    videoType: '',
    total: 0,
    page_num: 1,
    page_size: 10,
  } as any)

  async function searchTranscodeList() {
    loading.value = true
    try {
      const payload = {
        request: {
          page_num: state.page_num,
          page_size: state.page_size,
        } as any,
      }
      if (state.noteId) payload.request.noteId = state.noteId
      if (state.status) payload.request.status = state.status
      if (state.videoId) payload.request.videoId = state.videoId
      if (state.file_id) payload.request.file_id = state.file_id
      if (state.env) payload.request.env = state.env
      if (state.sceneType) payload.request.sceneType = state.sceneType
      if (state.cloudType) payload.request.cloudType = state.cloudType
      if (state.mediaType) payload.request.mediaType = state.mediaType
      if (state.videoType) payload.request.videoType = state.videoType
      
      const res = await queryNoteMediaCheckRecord(payload)
      console.log('转码测试结果:', res)
      
      if (res.code === 200) {
        transcodeList.value = res.data || []
        state.total = res.dataLength || 0
        console.log('设置的数据:', transcodeList.value)
      } else {
        toast.danger(res.message || '查询失败')
      }
    } catch (error) {
      toast.danger('获取转码测试数据失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  function handleViewDetail(record: any) {
    console.log('查看详情:', record)
    detailData.value = record
    detailModalVisible.value = true
  }

  function handleAdd() {
    console.log('新增记录')
    addModalVisible.value = true
  }

  async function handleSubmitAdd() {
    try {
      // 转换字符串为数组
      const thumbTypesList = addFormData.thumbTypesListStr.split(',').map(item => parseInt(item.trim())).filter(item => !isNaN(item))
      const streamTypes = addFormData.streamTypesStr.split(',').map(item => parseInt(item.trim())).filter(item => !isNaN(item))
      const audioStreamTypes = addFormData.audioStreamTypesStr.split(',').map(item => parseInt(item.trim())).filter(item => !isNaN(item))
      const need_check_type_list = addFormData.needCheckTypeListStr.split(',').map(item => item.trim()).filter(item => item !== '')
      
      const payload = {
        request: {
          noteId: addFormData.noteId,
          videoId: addFormData.videoId,
          videoType: addFormData.videoType,
          env: addFormData.env,
          sceneType: addFormData.sceneType,
          cloudType: addFormData.cloudType,
          mediaType: addFormData.mediaType,
          thumbTypesList: thumbTypesList,
          streamTypes: streamTypes,
          audioStreamTypes: audioStreamTypes,
          need_check_type_list: need_check_type_list
        }
      }
      
      const res = await publishSingleNote(payload)
      console.log('新增结果:', res)
      
      if (res.code === 200) {
        toast.success('新增成功')
        addModalVisible.value = false
        // 重置表单
        Object.assign(addFormData, {
          noteId: '',
          videoId: '',
          videoType: 'long',
          env: 'sit',
          sceneType: 'cn',
          cloudType: 'qc',
          mediaType: 'video',
          thumbTypesList: [3],
          streamTypes: [258],
          audioStreamTypes: [0],
          need_check_type_list: ['video', 'audio', 'thumb', 'frame'],
          thumbTypesListStr: '3',
          streamTypesStr: '258',
          audioStreamTypesStr: '0',
          needCheckTypeListStr: 'video,audio,thumb,frame'
        })
        // 刷新列表
        await searchTranscodeList()
      } else {
        toast.danger(res.message || '新增失败')
      }
    } catch (error) {
      toast.danger('新增记录失败')
      console.error(error)
    }
  }

  async function handleCheck(record: any) {
    console.log('检查转码:', record)
    loading.value = true
    try {
      const payload = {
        request: {
          id: record.id.toString()
        }
      }
      const res = await checkNoteMediaCheckRecord(payload)
      console.log('检查结果:', res)
      if (res.code === 200) {
        toast.success('检查成功')
        await searchTranscodeList()
      } else {
        toast.danger(res.message || '检查失败')
      }
    } catch (error) {
      toast.danger('检查转码失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'PENDING':
        return 'orange'
      case 'RUNNING':
        return 'blue'
      case 'COMPLETE':
        return 'green'
      case 'FAILED':
        return 'red'
      default:
        return undefined
    }
  }

  function getCheckStatusColor(status: string) {
    switch (status) {
      case 'PENDING':
        return 'orange'
      case 'RUNNING':
        return 'blue'
      case 'COMPLETE':
        return 'green'
      case 'FAILED':
        return 'red'
      case 'COMPLETE':
        return 'green'
      default:
        return undefined
    }
  }

  function parseCheckResultStatus(checkResult: string) {
    try {
      const parsed = JSON.parse(checkResult)
      const result: Record<string, string> = {}
      for (const [key, value] of Object.entries(parsed)) {
        if (typeof value === 'object' && value !== null) {
          const objValue = value as any
          result[key] = objValue.status || 'N/A'
        }
      }
      return result
    } catch (error) {
      return {}
    }
  }

  function formatCheckResult(checkResult: string) {
    try {
      const parsed = JSON.parse(checkResult)
      const result = []
      for (const [key, value] of Object.entries(parsed)) {
        if (typeof value === 'object' && value !== null) {
          const objValue = value as any
          result.push(`${key}: ${objValue.status || 'N/A'}`)
        }
      }
      return result.join(', ')
    } catch (error) {
      return checkResult
    }
  }

  function formatJson(jsonString: string) {
    try {
      const parsed = JSON.parse(jsonString)
      return JSON.stringify(parsed, null, 2)
    } catch (error) {
      return jsonString
    }
  }

  function formatDateTime(dateTimeStr: string) {
    if (!dateTimeStr) return ''
    try {
      const date = new Date(dateTimeStr)
      if (isNaN(date.getTime())) return dateTimeStr
      
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
      return dateTimeStr
    }
  }

  function handlePagination() {
    searchTranscodeList()
  }

  function handlepageSize(e: any) {
    state.page_size = e
    state.page_num = 1
    searchTranscodeList()
  }

  function clearParam() {
    state.noteId = ''
    state.status = ''
    state.videoId = ''
    state.file_id = ''
    state.env = ''
    state.sceneType = ''
    state.cloudType = ''
    state.mediaType = ''
    state.videoType = ''
    state.page_num = 1
  }

  onMounted(() => {
    searchTranscodeList()
  })
</script>

<style>
/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 详情弹窗样式 */
.ant-descriptions-item-label {
  font-weight: 600;
  background-color: #fafafa;
}

pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style> 