<template>
  <BeerPanel :bs="{ boxShadow: 'none', flex: 1, margin: 20, pt: 0 }">
    <BeerTitle :bs="{ mb: 16 }"> 视频查询 </BeerTitle>
    <BeerDivider />
    <br>
    <NewBsBox>
      <Form2
        label-width="auto"
        label-position="left"
      >
        <Spinner
          :spinning="loading"
          tip="查询中"
          size="large"
        >
          <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 10, flex: 1, alignItems: 'center' }">
            <Text
              bold
              :style="{ fontSize: '16px', marginRight: '20px' }"
            >环境:</Text>
            <FormItem2
              label=""
              name="env"
            >
              <Select
                v-model="state.env"
                :style="{ width: '120px', marginRight: '20px' }"
                type="secondary"
                :options="[{ label: 'sit', value: 'sit' }, { label: 'prod', value: 'prod' }]"
                placeholder="默认为sit"
              />
            </FormItem2>
            <Text
              bold
              :style="{ fontSize: '16px' }"
            >查询范围:</Text>
            <Tooltip>
              <template #content>
                <div>示例1:x-y(英文-)，查询[x,y);</div>
                <div>示例2:x，查询[x,x+10)</div>
              </template>
              <Icon
                :icon="Help"
                :style="{ marginRight: '10px' }"
              />
            </Tooltip>
            <FormItem2
              label=""
              name="dataRange"
            >
              <Input
                v-model="state.dataRange"
                :style="{ width: '120px', marginRight: '20px' }"
                placeholder="默认为0-10"
              />
            </FormItem2>

            <Text
              bold
              :style="{ fontSize: '16px' }"
            >有效数据数量:</Text>
            <FormItem2
              label=""
              name="dataLength"
            >
              <Card
                :style="{ width: 'auto', height: 'auto' }"
                :bordered="false"
              >
                {{ state.dataLength }}
              </Card>
            </FormItem2>

          </NewBsBox>
          <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 10, flex: 1, alignItems: 'center' }">
            <Text
              bold
              :style="{ fontSize: '16px' }"
            >宽度:</Text>
            <Tooltip>
              <template #content>
                <div>示例：x-y(英文-)，查询[x,y]内的数据</div>
              </template>
              <Icon
                :icon="Help"
                :style="{ marginRight: '10px' }"
              />
            </Tooltip>
            <FormItem2
              label=""
              name="videoWidth"
            >
              <Input
                v-model="state.videoWidth"
                :style="{ width: '120px', marginRight: '20px' }"
                placeholder="默认为无限制"
              />
            </FormItem2>
            <Text
              bold
              :style="{ fontSize: '16px' }"
            >高度:</Text>
            <Tooltip>
              <template #content>
                <div>示例：x-y(英文-)，查询[x,y]内的数据</div>
              </template>
              <Icon
                :icon="Help"
                :style="{ marginRight: '10px' }"
              />
            </Tooltip>
            <FormItem2
              label=""
              name="videoHeight"
            >
              <Input
                v-model="state.videoHeight"
                :style="{ width: '120px', marginRight: '20px' }"
                placeholder="默认为无限制"
              />
            </FormItem2>
            <Text
              bold
              :style="{ fontSize: '16px' }"
            >流类型:</Text>
            <Tooltip>
              <template #content>
                <div>示例：x,y,z(英文,)，查询含有x,y,z流类型的数据</div>
              </template>
              <Icon
                :icon="Help"
                :style="{ marginRight: '10px' }"
              />
            </Tooltip>
            <FormItem2
              label=""
              name="streamTypes"
            >
              <Input
                v-model="state.streamTypes"
                :style="{ width: '230px', marginRight: '20px' }"
                placeholder="默认包含0"
              />
            </FormItem2>
          </NewBsBox>

          <NewBsBox :bs="{ boxShadow: 'none', mr: 20, flex: 1, }">
            <Button
              style="float:right;margin:10px"
              type="secondary"
              :icon="{ icon: Clear }"
              :disabled="loading"
              @click="clearParam()"
            >重置</Button>
            <Button
              style="float:right;margin:10px"
              type="primary"
              :icon="{ icon: Search }"
              :disabled="loading"
              @click="videoFetch()"
            >查询</Button>
          </NewBsBox>
        </Spinner>
      </Form2>
    </NewBsBox>
    <br>
    <NewBsBox
      :bs="{ margin: 10, }"
      :style="{ textAlign: 'center' }"
    >
      <div style="margin-bottom: 10px; text-align: left">
        <Button
          type="primary"
          :disabled="!state.videoSelected.length"
          @click="exportSelectedData"
        >
          数据导出
        </Button>
      </div>
      <Table
        v-model:selected="state.videoSelected"
        :style="{ border: '1px solid #ccc' }"
        :columns="videoColumns"
        :data-source="state.videoSource"
        :row-selection="rowSelection"
        :loading="loading"
        size="small"
      >
        <template #empty>
          <Result
            status="empty"
            title="暂无内容"
          />
        </template>
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
        <template #isMusic="{ data }">
          <Text>{{ data === 1 ? "是" : "否" }}</Text>
        </template>
        <template #filekey="{ data }">
          <Tooltip>
            <Tag
              color="green"
              size="small"
              @click="copyUrl(data)"
            >filekey</Tag>
            <template #content>
              {{ data }}
            </template>
          </Tooltip>
        </template>
        <template #fileurl="{ data }">
          <Tooltip>
            <Tag
              color="blue"
              size="small"
              @click="copyUrl(data)"
            >地址</Tag>
            <template #content>
              {{ data }}
            </template>
          </Tooltip>
        </template>
        <template #lightType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #colorType="{ data }">
          <Text>{{ getMapLable(colorTypeList, data) }}</Text>
        </template>
        <template #spaceType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #travelType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #contrastType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #isMovePicture="{ data }">
          <Text>{{ getMapLable(isMovePictureList, data) }}</Text>
        </template>
        <template #operation="{ rowData }">
          <Button
            size="small"
            style="background-color:#3366FF;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
            @click="jsonDetail(rowData)"
          >查看</Button>
        </template>
      </Table>
    </NewBsBox>

    <Modal
      v-model:visible="showjsonDataModal"
      :mask-closeable="maskCloseable"
      title="数据"
      :with-footer="false"
      :style="{ width: '800px', height: '800px' }"
    >
      <json-viewer
        :value="jsonData"
        copyable
      />
    </Modal>
  </BeerPanel>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  import {
    Form2, FormItem2, Select, Input, Button, Text, Spinner, toast, Table, Tooltip, Tag, Modal, Card, Icon, Result,
  } from '@xhs/delight'
  import {
    Search, Clear, Help,
  } from '@xhs/delight/icons'
  import JsonViewer from 'vue-json-viewer'
  import * as XLSX from 'xlsx'
  import { getAvsetVideoQuery } from '../../services/material'
  import {
    commonTypeList, colorTypeList, getMapLable, isMovePictureList,
  } from '../../utils/common'

  const loading = ref(false)
  const state = ref({
    env: '',
    videoWidth: '',
    videoHeight: '',
    streamTypes: '',
    dataRange: '',
    dataLength: 0,
    videoSource: [],
    videoSelected: [],
  })

  const videoColumns = ref([
    {
      title: 'id',
      dataIndex: 'id',
      minWidth: 50,
    },
    {
      title: 'videoId',
      dataIndex: 'videoId',
      minWidth: 70,
    },

    {
      title: 'filekey',
      dataIndex: 'filekey',
      minWidth: 70,
    },
    {
      title: 'fileurl',
      dataIndex: 'fileurl',
      minWidth: 70,
    },
    {
      title: '详情',
      dataIndex: 'videodetails',
      minWidth: 60,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      minWidth: 80,
    },
    {
      title: '宽高比',
      dataIndex: 'videoDAR',
      minWidth: 70,
    },
    {
      title: 'width',
      dataIndex: 'videoWidth',
      minWidth: 70,
    },
    {
      title: 'height',
      dataIndex: 'videoHeight',
      minWidth: 70,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },
  ])

  const showjsonDataModal = ref(false)
  const jsonData = ref({})
  const maskCloseable = ref(false)

  const rowSelection = {
    getCheckboxProps: v => ({
      selectable: v.id !== 0,
    }),
    onSelect: v => console.log(v),
  }

  function videoFetch() {
    // console.log('videoFetch')
    loading.value = true

    const queryparam = {
      streamType: 0,
    }
    if (state.value.videoWidth) queryparam.videoWidth = state.value.videoWidth
    if (state.value.videoHeight) queryparam.videoHeight = state.value.videoHeight

    const payload = {
      request: {
        queryparam,
      },
    }

    payload.request.env = state.value.env !== '' ? state.value.env : 'sit'
    payload.request.streamTypes = state.value.streamTypes !== '' ? state.value.streamTypes : '0'
    payload.request.dataRange = state.value.dataRange

    getAvsetVideoQuery(payload).then(res => {
      // console.log('res', res)
      loading.value = false
      state.value.dataLength = res.dataLength
      state.value.videoSource = res.data.map(item => {
        item.key = item.id
        return item
      })
      toast.success('查询成功')
    }).catch(err => {
      loading.value = false
      toast.danger(`查询失败: ${err}`)
    })
  }

  function clearParam() {
    state.value = {
      env: '',
      videoWidth: '',
      videoHeight: '',
      streamTypes: '',
      dataRange: '',
    }
  }

  function copyUrl(data: any) {
    const cInput = document.createElement('input')
    cInput.value = data
    document.body.appendChild(cInput)
    cInput.select() // 选取文本框内容
    document.execCommand('copy')
    toast.success('复制成功')
  }

  function jsonDetail(data: any) {
    showjsonDataModal.value = true
    jsonData.value = data
    maskCloseable.value = true
  }

  function exportSelectedData() {
    const data = state.value.videoSource.filter(item => state.value.videoSelected.includes(item.id)).map(item => ({
      id: item.id,
      bucket: item.bucket,
      videoId: item.videoId,
      filekey: item.filekey,
      fileurl: item.fileurl,
      details: item.videodetails,
      updateTime: item.updateTime,
      videoDAR: item.videoDAR,
      videoWidth: item.videoWidth,
      videoHeight: item.videoHeight,
    }))
    // console.log(data)

    // 定义 Excel 数据
    const worksheet = XLSX.utils.json_to_sheet(data)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Selected Data')

    // 生成 Excel 文件的二进制数据
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })

    // 创建 Blob 对象
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'selected_data.xlsx'
    document.body.appendChild(a)
    a.click()

    // 移除链接
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  onMounted(videoFetch)
</script>
