<template>
  <NewBsBox :bs="{ display: 'flex' }">
    <BeerPanel :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">
      <!-- <BeerTitle :bs="{ mb: 16 }">图片尺寸修改
      </BeerTitle>
      <BeerDivider /> -->
      <Tabs v-model="state.tabValue">
        <TabPane label="修改尺寸">
          <NewBsBox :bs="{ mt: 30,ml: 30 }">
            <div>
              <div
                style="display: table-cell;
vertical-align: middle;"
              >
                <span
                  v-if="state.fileStatus==='uploading'"
                  style="margin-right: 20px;display: inline-block"
                >正在上传</span>
                <span
                  v-else-if="state.fileStatus==='success'"
                  style="margin-right: 20px;display: inline-block"
                >上传成功</span>
                <span
                  v-else-if="state.fileStatus==='error'"
                  style="margin-right: 20px;display: inline-block"
                >上传失败</span>
                <span
                  v-else
                  style="margin-right: 20px;display: inline-block"
                >选取图片</span>

                <button
                  style="margin-right: 30px;"
                  class="button-css"
                  type="button"
                >上传图片
                  <input
                    class="input-css"
                    type="file"
                    accept="image/png,image/gif,image/jpg,image/jpeg,image/bmp,image/webp"
                    name=""
                    @change="handleFileChange"
                  >
                </button>

              </div>
              <div
                style="display: table-cell;
vertical-align: middle;"
              >

                <img
                  v-if="state.srcUrl!=='' && state.srcUrl!=='undefined'"
                  :src="state.srcUrl"
                  width="80"
                  height="80"
                >

              </div>
            </div>
          </NewBsBox>
          <NewBsBox :bs="{ mt: 30,ml: 30 }">
            <Text> 格式转换：</Text>
            <RadioGroup v-model="state.destFormat">
              <Radio
                value="jpg"
                label="jpg"
              />
              <Radio
                value="gif"
                label="gif"
              />
              <Radio
                value="png"
                label="png"
              />
              <Radio
                value="webp"
                label="webp"
              />
              <Radio
                value="jpeg"
                label="jpeg"
              />
            </RadioGroup>
          </NewBsBox>

          <NewBsBox :bs="{ mt: 30,ml: 30 }">
            <Text> 缩放模式：</Text>
            <RadioGroup v-model="state.transMode">
              <Radio
                value="1"
                label="等比例缩放"
              />
              <Radio
                value="2"
                label="目标宽高缩放"
              />
              <Radio
                value="0"
                label="不缩放"
                checked
              />    </RadioGroup>

          </NewBsBox>

          <NewBsBox
            v-if="state.transMode=='1'"
            :bs="{ mt: 30,ml: 30 }"
          >
            <Text>比例：</Text>
            <Input
              v-model="state.equalValue"
              placeholder="请输入比例，例如：0.9"
            />
          </NewBsBox>
          <NewBsBox
            v-if="state.transMode=='2'"
            :bs="{ mt: 30,ml: 30 }"
          >
            <Text>比例：</Text>
            <Input
              v-model="state.transwidth"
              placeholder="宽度"
            />
            <Text>*</Text><Input
              v-model="state.transheight"
              placeholder="高度"
            />
          </NewBsBox>

          <NewBsBox :bs="{ mt: 30,ml: 30 }">
            <Button
              type="primary"
              :disabled="state.isDisabled"
              @click="ModifyImg"
            >修改尺寸</Button>
            <Button
              :style="{ backgroundColor: '#999933', color: 'white',marginLeft:'30px'}"
              :disabled="state.isDisabled"
              @click="saveImg"
            >保存新图</Button>

          </NewBsBox>

          <Divider style="margin-top: 30px;" />
          <!-- <Text style="margin-top: 30px;margin-left: 30px;">修改尺寸后图片预览</Text> -->
          <Banner
            :closeable="false"
            type="success"
            description="修改尺寸后图片预览"
          >
            <template #icon>
              <Icon :icon="Airplay" />
            </template>
          </Banner>

          <div style="margin-top: 10px;">
            <img :src="state.imageUrl">
          </div>

        </TabPane>
        <TabPane label="图片裁切">
          <NewBsBox :bs="{ mt: 30,ml: 30 }">
            <button
              style="margin-right: 30px;"
              class="button-css"
              type="button"
            >选取图片
              <input
                class="input-css"
                type="file"
                accept="image/png,image/gif,image/jpg,image/jpeg,image/bmp,image/webp"
                name=""
                @change="selectFile"
              >
            </button>

            <Button
              :style="{ backgroundColor: '#999933', color: 'white',marginLeft:'30px'}"
              @click="savecutImg"
            >保存新图</Button>

          </NewBsBox>
          <NewBsBox
            v-if="result.dataURL "
            :bs="{ mt: 30,ml: 30 }"
          >

            <Banner
              :closeable="false"
              type="success"
              description="裁切后的图片预览"
            >
              <template #icon>
                <Icon :icon="Airplay" />
              </template>
            </Banner>
            <div style="margin-top:10px">
              <img :src="result.dataURL">
            </div>
          </NewBsBox>

          <Modal
            v-model:visible="isShowDialog"
            style="width:800px;height:600px"
            title="图片裁切"
            :mask-closeable="maskCloseable"
          >
            <template #footer>
              <Button
                style="margin:10px;"
                @click="cancel"
              >取消</Button>
              <Button
                style="margin:10px;"
                @click="clear"
              >清除</Button>
              <Button
                style="margin:10px;"
                @click="reset"
              >重置</Button>
              <Button
                style="margin:10px;"
                type="primary"
                @click="getResult"
              >裁切</Button>
            </template>

            <!-- 图片裁切插件 -->
            <VuePictureCropper
              :box-style="{
                width: '100%',
                height: '100%',
                backgroundColor: '#f8f8f8',
                margin: 'auto',
              }"
              :img="pic"
              :options="{
                viewMode: 1,
                dragMode: 'crop',

              }"
            />
            <!-- 图片裁切插件 -->
          </Modal>
        </TabPane>

      </Tabs>

    </BeerPanel>
  </NewBsBox>

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import {
    Text, toast, Input, RadioGroup, Radio, Button, Modal, Tabs, TabPane, Divider, Banner, Icon,
  } from '@xhs/delight'
  import { useRoute } from 'vue-router'
  import {
    Airplay,
  } from '@xhs/delight/icons'
  import {
    NewBsBox, BeerPanel,
  } from '@xhs/yam-beer'
  import VuePictureCropper, { cropper } from 'vue-picture-cropper'
  import { UploadFile, Modify } from '../../services/testtool'

  interface Result {
    dataURL: string
    blobURL: string
  }

  const result: Result = reactive({
    dataURL: '',
    blobURL: '',
  })

  const route = useRoute()

  const isShowDialog = ref(false)
  const maskCloseable = ref(false)
  const pic = ref<string>('')
  const uploadInput = ref<HTMLInputElement | null>(null)

  const state = reactive({
    tabValue: '修改尺寸',
    fileStatus: 'ready',
    srcUrl: '',
    transMode: '',
    isDisabled: true,
    destFormat: 'jpg',
    equalValue: '',
    transwidth: '',
    transheight: '',
    imageUrl: '',
  })

  const handleFileChange = (e: Event) => {
    state.isDisabled = true
    // 断言为HTMLInputElement
    const target = e.target as HTMLInputElement
    const files = target.files
    console.error('ninghuanjun', files)
    if (files) {
      const uploadedFile = files[0]
      const formData = new FormData()
      formData.append('file', uploadedFile)

      e.target.value = ''
      state.fileStatus = 'uploading'
      console.log(formData.get('file'))

      UploadFile(formData, { headers: { 'Content-Type': 'multipart/form-data' } })
        .then(res => {
          console.error('ninghuanjun---res---', res)
          // loading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
            state.fileStatus = 'error'
          } else {
            state.fileStatus = 'success'
            state.srcUrl = res.data.imageUrl
            state.isDisabled = false
          }

          formData.delete('file')
        })
        .catch(() => {
          toast.danger('上传失败！请重试')
          state.fileStatus = 'ready'
        })
    }
  }

  function saveImg() {
    window.open(state.imageUrl)
  }

  function savecutImg() {
    const aLink = document.createElement('a')
    const evt = document.createEvent('HTMLEvents')
    evt.initEvent('click', true, true)// initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
    const Timestamp = new Date().getTime()
    aLink.download = String(Timestamp)
    aLink.href = result.blobURL
    aLink.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true, view: window }))// 兼容火狐
  }

  const selectFile = (e: Event): void => {
    maskCloseable.value = true

    // 重置上一次的结果
    result.dataURL = ''
    result.blobURL = ''
    // 如果有多个裁剪框，也需要重置掉裁剪目标的值，避免使用同一张图片无法触发watch
    pic.value = ''
    // 获取选取的文件
    const target = e.target as HTMLInputElement
    const { files } = target
    if (!files) return
    const file: File = files[0]
    // 转换为base64传给裁切组件
    const reader: FileReader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (): void => {
      // 更新裁切弹窗的图片源
      pic.value = String(reader.result)
      // 显示裁切弹窗
      isShowDialog.value = true
      // 清空已选择的文件
      if (!uploadInput.value) return
      uploadInput.value.value = ''
    }
  }

  const cancel = (): void => {
    isShowDialog.value = false
  }
  const clear = (): void => {
    cropper.clear()
  }

  const reset = (): void => {
    cropper.reset()
  }

  const getResult = async (): Promise<void> => {
    console.log(cropper)
    // 获取生成的base64图片地址
    const base64: string = cropper.getDataURL()
    // 获取生成的blob文件信息
    const blob: Blob = await cropper.getBlob()
    // 获取生成的file文件信息
    const file = await cropper.getFile({
      fileName: '测试文件名，可不传',
    })
    console.log({ base64, blob, file })
    // 把base64赋给结果展示区
    result.dataURL = base64
    try {
      result.blobURL = URL.createObjectURL(blob)
    } catch (e) {
      result.blobURL = ''
    }
    // 隐藏裁切弹窗
    isShowDialog.value = false
  }

  //   ModifyImg
  function ModifyImg() {
    const payload = {
      request: {
        destFormat: state.destFormat,
        imageCrop: {},
        transPara: {},
        srcUrl: state.srcUrl,
        transMode: state.transMode,

      },
    }
    if (state.transMode === '1') payload.request.transPara.equalValue = state.equalValue
    if (state.transMode === '2') {
      payload.request.transPara.width = state.transwidth
      payload.request.transPara.height = state.transheight
    }
    if (state.transMode === '0') payload.request.transPara = {}

    console.error('ninghuanjun---res---', payload)

    Modify(payload)
      .then(res => {
        // loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
          state.fileStatus = 'error'
        } else {
          state.fileStatus = 'success'
          state.imageUrl = res.data.imageUrl
        //   state.isDisabled = false
        }

        // formData.delete('file')
      })
      .catch(() => {
        toast.danger('上传失败！请重试')
        state.fileStatus = 'ready'
      })
  }

  onMounted(() => {
    // console.error('ninghuanjun---00000-', route.params)
    if (route.params !== {} && route.params.srcUrl !== '') {
      state.srcUrl = String(route.params.srcUrl)
      state.isDisabled = false
    }
    console.error('ninghuanjun---00000-', state.srcUrl)

    //  state.theRequest = route.params
  })

</script>
<style scoped lang="stylus">
.button-css {
	background-color: #28BC77;
	border: 1px solid #28BC77;
  color: #fff;
	border-radius: 4px;
	position: relative;
  width: 100px;
  height: 30px;
  font-weight: 800;
  cursor: pointer;
	overflow: hidden;
		}
.input-css {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
  cursor: pointer;
  width: 100px;
  height: 30px;
	bottom: 0;
	opacity: 0;
}

 </style>
