<template>
  <NewBsBox :bs="{ display: 'flex' }">
    <BeerPanel :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">
      <BeerTitle :bs="{ mb: 16 }">图片信息
      </BeerTitle>
      <BeerDivider />
      <NewBsBox :bs="{ mt: 30,ml: 30 }">
        <div>
          <div
            style="display: table-cell;
vertical-align: middle;"
          >
            <span
              v-if="state.fileStatus==='uploading'"
              style="margin-right: 20px;display: inline-block"
            >正在上传</span>
            <span
              v-else-if="state.fileStatus==='success'"
              style="margin-right: 20px;display: inline-block"
            >上传成功</span>
            <span
              v-else-if="state.fileStatus==='error'"
              style="margin-right: 20px;display: inline-block"
            >上传失败</span>
            <span
              v-else
              style="margin-right: 20px;display: inline-block"
            >选取图片</span>

            <button
              style="margin-right: 30px;"
              class="button-css"
              type="button"
            >上传图片
              <input
                class="input-css"
                type="file"
                accept="image/png,image/gif,image/jpg,image/jpeg,image/bmp,image/webp"
                name=""
                @change="handleFileChange"
              >
            </button>

          </div>
          <div
            style="display: table-cell;
vertical-align: middle;"
          >

            <img
              v-if="state.imageUrl!==''"
              :src="state.imageUrl"
              style="width:80px;height:80px;cursor: pointer;"
              @click="showdetail()"
            >

          </div>
          <div
            style="display: table-cell;
vertical-align: middle;"
          >
            <Button
              v-if="state.imageUrl!==''"
              style="margin-left: 30px;"
              type="primary"
              @click="ModifyImg"
            >执行裁剪</Button>
          </div>

        </div>
      </NewBsBox>
      <NewBsBox :bs="{ mt: 30,ml: 30 }">
        <Table
          :key="state.isFlag"
          :columns="imgColumns"
          :data-source="imgDataSource"
          size="small"
        >
          <template #name="{ data }">
            <Text style="font-weight:bold;">{{ data }}</Text>
          </template>
        </Table>

      </NewBsBox>

    </BeerPanel>

  </NewBsBox>
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Table, Text, toast, Button, viewImgs,
  } from '@xhs/delight'

  import {
    // Add,
  } from '@xhs/delight/icons'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  import { UploadFile } from '../../services/testtool'

  const router = useRouter()
  const state = reactive({
    fileStatus: 'ready',
    isFlag: false,
    imageUrl: '',

  })

  const imgColumns = [
    {
      title: '字段名称',
      dataIndex: 'name',
      minWidth: 200,
    },
    {
      title: '值',
      dataIndex: 'value',
    },
    {
      title: '描述',
      dataIndex: 'description',
      minWidth: 500,
    },

  ]

  const imgDataSource = [
    { name: '图片大小', value: '--', description: '文件大小，单位：KB' },
    { name: '图片宽度', value: '--', description: '图片宽度' },
    { name: '图片高度', value: '--', description: '图片高度' },
    { name: '图片类型', value: '--', description: '图片类型，如png、jpeg、gif、bmp等' },
    { name: '图片像素', value: '--', description: '单位像素（px）' },

  ]

  const handleFileChange = (e: Event) => {
    console.error('ninghuanjun--111--', state.fileStatus)
    // 断言为HTMLInputElement
    const target = e.target as HTMLInputElement
    const files = target.files
    console.error('ninghuanjun', files)
    if (files) {
      const uploadedFile = files[0]
      const formData = new FormData()
      formData.append('file', uploadedFile)

      e.target.value = ''
      state.fileStatus = 'uploading'
      console.log(formData.get('file'))

      UploadFile(formData, { headers: { 'Content-Type': 'multipart/form-data' } })
        .then(res => {
          console.error('ninghuanjun---res---', res)
          // loading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
            state.fileStatus = 'error'
          } else {
            state.fileStatus = 'success'
            state.isFlag = !state.isFlag
            state.imageUrl = res.data.imageUrl
            imgDataSource[0].value = res.data.imageSize
            imgDataSource[1].value = res.data.imageWidth
            imgDataSource[2].value = res.data.imageHeight
            imgDataSource[3].value = res.data.imageFormat
            imgDataSource[4].value = res.data.imagePx
          }

          formData.delete('file')
        })
        .catch(() => {
          toast.danger('上传失败！请重试')
          state.fileStatus = 'ready'
        })
    }
  }
  function ModifyImg() {
    // 跳转到任务页面
    router.push({
      name: 'ImgSizeModify',
      params: {
        srcUrl: state.imageUrl,
      },
    })
  }

  const showdetail = () => {
    viewImgs([state.imageUrl])
  }

  onMounted(() => {
    console.error('ninghuanjun---00000-', state.fileStatus)
  })

</script>
<style scoped lang="stylus">
.button-css {
	background-color: #28BC77;
	border: 1px solid #28BC77;
  color: #fff;
	border-radius: 4px;
	position: relative;
  width: 100px;
  height: 30px;
  font-weight: 800;
  cursor: pointer;
	overflow: hidden;
		}
.input-css {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
  cursor: pointer;
  width: 100px;
  height: 30px;
	bottom: 0;
	opacity: 0;
}

 </style>
