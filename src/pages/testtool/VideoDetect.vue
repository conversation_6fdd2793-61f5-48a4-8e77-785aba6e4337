<!-- ignore eslint -->
<template>
  <BeerPanel :bs="{ boxShadow: 'none', flex: 1, marginTop: 20, pt: 0 }">
    <BeerTitle :bs="{ mb: 16 }"> 视频标注 </BeerTitle>
    <BeerDivider />
    <br>
    <!-- 搜索组件 -->
    <div class="search-container">
        <!-- 第一行：时间和类型 -->
        <div class="search-row">
          <div class="search-item">
            <div class="search-label">
              <Text bold>创建时间</Text>
            </div>
            <div class="search-control">
              <DateRangePicker
                v-model="state.create_time"
                unit="second"
                class="search-field"
                :placeholder="{ start: '开始日期', end: '结束日期' }"
              />
            </div>
          </div>

          <div class="search-item">
            <div class="search-label">
              <Text bold>检测类型</Text>
              <Tooltip theme="light">
                <template #content>
                  <div style="color: #333;">
                    检测类型说明：<br>
                    1 - 普通检测<br>
                    2 - 增强检测
                  </div>
                </template>
                <Icon :icon="Help" class="help-icon" />
              </Tooltip>
            </div>
            <div class="search-control">
              <Select
                v-model="state.detect_type"
                :options="detectTypes"
                class="search-field"
                clearable
                placeholder="请选择检测类型"
              />
            </div>
          </div>

          <div class="search-item">
            <div class="search-label">
              <Text bold>关联ID</Text>
              <Tooltip theme="light">
                <template #content>
                  <div style="color: #333;">请输入 correlationId 进行精确查找</div>
                </template>
                <Icon :icon="Help" class="help-icon" />
              </Tooltip>
            </div>
            <div class="search-control">
              <Input
                v-model="state.correctlationId"
                class="search-field"
                clearable
                placeholder="请输入关联ID"
              />
            </div>
          </div>
        </div>

        <!-- 第二行：状态相关 -->
        <div class="search-row">
          <div class="search-item">
            <div class="search-label">
              <Text bold>检测状态</Text>
            </div>
            <div class="search-control">
              <Select
                v-model="state.status"
                :options="[{ label: 'PASS', value: 'pass' }, { label: 'FAIL', value: 'fail' }, { label: '无', value: 'N/T' }, { label: 'RUNNING', value: 'running' }]"
                class="search-field"
                clearable
                placeholder="请选择检测状态"
              />
            </div>
          </div>

          <div class="search-item">
            <div class="search-label">
              <Text bold>标注状态</Text>
            </div>
            <div class="search-control">
              <Select
                v-model="state.double_check"
                :options="[{ label: 'PASS', value: 'pass' }, { label: 'FAIL', value: 'fail' }, { label: '未标注', value: 'uncheck' }]"
                class="search-field"
                clearable
                placeholder="请选择标注状态"
              />
            </div>
          </div>

          <div class="search-item">
            <div class="search-label">
              <Text bold>检测信息</Text>
            </div>
            <div class="search-control">
              <Select
                v-model="state.message"
                :options="messageType"
                class="search-field"
                clearable
                placeholder="请选择检测信息类型"
              />
            </div>
          </div>
        </div>

        <!-- 第三行：其他条件和操作 -->
        <div class="search-row">
          <div class="search-item">
            <div class="search-label">
              <Text bold>来源</Text>
            </div>
            <div class="search-control">
              <Select
                v-model="state.source"
                :options="sourceType"
                class="search-field"
                clearable
                placeholder="请选择来源"
              />
            </div>
          </div>

          <div class="search-item">
            <div class="search-label">
              <Text bold>视频名</Text>
              <Tooltip theme="light">
                <template #content>
                  <div style="color: #333;">请输入视频文件名进行模糊搜索</div>
                </template>
                <Icon :icon="Help" class="help-icon" />
              </Tooltip>
            </div>
            <div class="search-control">
              <Input
                v-model="state.filekey"
                class="search-field"
                clearable
                placeholder="请输入视频名"
              />
            </div>
          </div>

          <div class="search-item search-actions-item">
            <div class="search-actions">
              <Button
                type="primary"
                size="medium"
                :icon="{ icon: Search }"
                @click="searchVideoDetectResult()"
              >
                查询
              </Button>
              <Button
                type="secondary"
                size="medium"
                :icon="{ icon: Clear }"
                @click="clearParam"
              >
                重置
              </Button>
            </div>
          </div>
        </div>
    </div>
    <Tabs
      v-model="state.tabValue"
      style="margin-top: 10px;"
    >
      <TabPane label="视频标注">
        <br>
        <NewBsBox :bs="{ display: 'flex', justifyContent: 'space-around', width: '100%' }">
          <Banner
            align="center"
            type="success"
            :closeable="false"
            :style="{ width: '45%', borderRadius: '8px' }"
          >
            <template
              v-if="state.detect_type !== '2'"
              #description
            >
              准确率: {{ (((state.TP + state.TN) / (state.TP + state.TN + state.FP + state.FN)) * 100).toFixed(2) }}%
            </template>
            <template
              v-else
              #description
            >
              准确率: 100%
            </template>
            <template #icon>
              <Icon :icon="CheckCorrect" />
            </template>
          </Banner>
          <Banner
            align="center"
            :closeable="false"
            :style="{ width: '45%', borderRadius: '8px' }"
          >
            <template
              v-if="state.detect_type !== '2'"
              #description
            >
              召回率: {{ ((state.TP / (state.TP + state.FN)) * 100).toFixed(2) }}%
            </template>
            <template
              v-else
              #description
            >
              召回率: 100%
            </template>
            <template #icon>
              <Icon
                :icon="LoopOnce"
                :rotate="180"
              />
            </template>
          </Banner>
        </NewBsBox>
        <br>

        <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
          <Checkbox
            :checked="state.selected_count === videoDetectResult.length"
            :indeterminate="state.selected_count > 0 && state.selected_count < videoDetectResult.length"
            :disabled="state.total === 0"
            style="margin-left:20px;margin-right:20px"
            label="全部查询结果"
            description="点击全选/取消全选"
            @click="handleAllCheckbox"
          />
          <Button
            size="small"
            style="margin-right: 10px; background-color:#28BC77; color:white;"
            @click="markSelectedVideoDetectResult('pass')"
          >批量通过</Button>
          <Button
            size="small"
            style="margin-right: 10px; background-color:#f03860; color:white;"
            @click="markSelectedVideoDetectResult('fail')"
          >批量失败</Button>
          <Button
            size="small"
            style="margin-right: 10px;background-color:#0038a0; color:white;"
            @click="submitPingCode2"
          >一键PingCode2</Button>
          <Tooltip placement="right">
            <template #content>
              选择需要提交Pingcode2的卡片后<br>
              点击左侧按钮提交<br>
              所有卡片生成一个需求<br>
              查看链接：https://pingcode2.devops.xiaohongshu.com/my-space/84/0000001425/tasks?viewId=3&tab=detail
            </template>
            <Icon
              :icon="Help"
              :style="{ marginRight: '10px' }"
            />
          </Tooltip>
        </NewBsBox>
        <br>

        <Spinner
          :spinning="loading"
          tip="查询中"
          size="large"
        >
          <NewBsBox :bs="{
            display: 'flex',
            flexWrap: 'wrap', // 关键：允许换行
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: 10, // 替代 margin，统一间距
          }">
            <template
              v-for="item in videoDetectResult"
              :key="item.id"
            >
              <!-- 每个卡片 -->
              <NewBsBox
                class="result_card"
                :bs="{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                  padding: 10,
                  flexShrink: 0 // 禁止压缩
                }"
              >
                <!-- 图片固定尺寸 + 保持比例 -->
                <video
                  :src="item.videos[0]"
                  :style="{
                    maxHeight: '90%',
                    objectFit: 'cover',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    width: '200px'
                  }"
                  controls
                  preload="metadata"
                  @error="handleVideoError($event, item, 0)"
                  @loadeddata="handleVideoLoaded($event, item, 0)"
                >
                  <source
                    :src="item.videos[0]"
                    type="video/mp4"
                  >
                  您的浏览器不支持视频播放
                </video>
                <video
                  v-if="item.detect_type === '2'"
                  :src="item.videos[1]"
                  :style="{
                    marginLeft: '5px',
                    maxHeight: '90%',
                    objectFit: 'cover',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    width: '200px'
                  }"
                  controls
                  preload="metadata"
                  @error="handleVideoError($event, item, 1)"
                  @loadeddata="handleVideoLoaded($event, item, 1)"
                >
                  <source
                    :src="item.videos[1]"
                    type="video/mp4"
                  >
                  您的浏览器不支持视频播放
                </video>
                <!-- 文字信息 -->
                <Card
                  :bordered="false"
                  :style="{
                    width: '225px',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                  }"
                  :title="`数据Id:${item.id}`"
                >
                  <template #extra>
                    <Checkbox
                      :checked="item.selected"
                      @click="handleCheckbox(item)"
                    />
                  </template>
                  <Tooltip placement="left">
                    <template #content>
                      {{ item.filekey }}
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >视频名: {{ item.filekey
                    }}</Text><br>
                  </Tooltip>
                  <Tooltip placement="left">
                    <template #content>
                      {{ item.create_time }}
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >创建时间: {{ item.create_time
                    }}</Text><br>
                  </Tooltip>
                  <Tooltip
                    v-if="item.double_check"
                    placement="left"
                  >
                    <template #content>
                      {{ item.update_time }}
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >更新时间: {{ item.update_time
                    }}</Text><br>
                  </Tooltip>
                  <Text
                    bold
                    style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                  >场景: {{ item.scene_type }}</Text><br>
                  <Text
                    bold
                    style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                  >检测类型: {{(detectTypes.find(type =>
                    type.value === item.detect_type))?.label}}</Text><br>
                  <div v-if="item.double_check">
                    <Text
                      bold
                      style="margin-right:5px;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      标注状态:
                    </Text>
                    <Text
                      v-if="item.double_check === 'pass'"
                      bold
                      style="color: #28BC77;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.double_check }}
                    </Text>
                    <Text
                      v-else-if="item.double_check === 'fail'"
                      bold
                      style="color: #f03860;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.double_check }}
                    </Text>
                    <Icon
                      :icon="Write"
                      style="cursor: pointer; margin-left: 8px;"
                      @click="handleEdit(item)"
                    />
                    <br>
                    <Text
                      bold
                      style="margin-right:5px;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      检测状态:
                    </Text>
                    <Text
                      v-if="item.status === 'pass'"
                      bold
                      style="color: #28BC77;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.status }}
                    </Text>
                    <Text
                      v-else-if="item.status === 'fail'"
                      bold
                      style="color: #f03860;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.status }}
                    </Text>
                    <Text
                      v-else-if="item.status === 'N/T'"
                      bold
                      style="color: #f03860;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      无
                    </Text>
                    <br>

                    <Tooltip placement="left">
                      <template #content>
                        {{ item.message }}
                      </template>
                      <Text
                        bold
                        style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                      >
                        检测信息: {{ item.message }}
                      </Text><br>
                    </Tooltip>
                  </div>
                  <Text
                    bold
                    style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                  >
                    source: {{ item.request_info.source }}
                  </Text><br>
                  <Tooltip placement="left">
                    <template #content>
                      <div
                        v-for="(value, key) in item.request_info"
                        :key="key"
                      >
                        {{ key }}: {{ value }}
                      </div>
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      request_info: {{ item.request_info }}
                    </Text><br>
                  </Tooltip>
                  <Tooltip
                    v-if="item.double_check"
                    placement="left"
                  >
                    <template #content>
                      {{ item.data[0].remark }}
                    </template>
                    <!-- <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      remark: {{ item.data[0].remark }}
                    </Text><br> -->
                  </Tooltip>
                  <Tooltip
                    v-if="item.double_check"
                    placement="left"
                  >
                    <template #content>
                      <div style="max-width: 300px; word-break: break-all;">
                        <pre
                          style="white-space: pre-wrap; margin: 0;">{{ Array.isArray(item.data) ? JSON.stringify(item.data, null, 2) : item.data }}</pre>
                      </div>
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 200px; display: inline-block; cursor: pointer;"
                      @dblclick="copyData(item.data)"
                    >
                      详情: {{Array.isArray(item.data) ? item.data.map(d => d.remark || '').join(', ').slice(0, 30) +
                        '...' :
                        item.data}}
                    </Text>
                  </Tooltip>
                  <template
                    v-if="!item.double_check"
                    #footer
                  >
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      标注状态:
                    </Text>
                    <div style="display: flex; justify-content: space-between; width: 100%">
                      <Button
                        size="small"
                        style="flex: 1; margin-right: 10px; background-color:#28BC77; color:white;"
                        @click="markVideoDetectResult(item.id, 'pass')"
                      >PASS</Button>
                      <Button
                        size="small"
                        style="flex: 1; background-color:#f03860; color:white;"
                        @click="markVideoDetectResult(item.id, 'fail')"
                      >FAIL</Button>
                    </div>
                  </template>
                </Card>
              </NewBsBox>
            </template>
          </NewBsBox>
        </Spinner>
        <NewBsBox :bs="{ display: 'flex', margin: 20, alignItems: 'center', justifyContent: 'flex-end' }">
          <Pagination
            v-model="state.page_num"
            :total="state.total"
            :page-size-options="pageSizeOptions"
            @update:modelValue="handlePagination"
            @update:pageSize="handlepageSize"
          />
        </NewBsBox>
      </TabPane>
      <TabPane label="统计分析">
        <br>
        <Line
          v-if="videoDetectResult.filter(item => item.detect_type === '2').length === 0"
          :chart-data="chartData"
          :dimensions="dimensions"
          :metrics="metrics"
          :loading="loading"
          :chart-config="chartConfig"
          :colors="['#28BC77', '#1166CC']"
          style="width: 90%; height: auto; margin: 0 auto;"
        />
      </TabPane>
    </Tabs>
  </BeerPanel>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import {
    useStore,
  } from 'vuex'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  import {
    Search, Clear, Write, Help, CheckCorrect, LoopOnce,
    //  Delete,Notes,
  } from '@xhs/delight/icons'
  import {
    Select, Button, Text, DateRangePicker, Spinner, Icon, Tooltip, toast, Pagination, Card, Checkbox, Banner, Tabs, TabPane, Input,
  } from '@xhs/delight'
  import { Line } from '@xhs/delight-charts'
  import {
    getVideoDetectResult, updateVideoDetectResult, getVideoDetectStatistics, submitVideoDetectResultToPingCode2,
  } from '../../services/task'
  import { pageSizeOptions } from '../../utils/common'

  const store = useStore()
  const userEmail = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email : '路人'
  const dimensions = ref(['week'])
  const metrics = ref(['准确率', '召回率'])

  const chartConfig = ref({
    yAxis: {
      valueType: 'percent',
      digit: 0,
    },
    tooltip: {
      valueType: 'percent',
      digit: 0,
    },
  })
  const chartData = ref([])

  const videoDetectResult = ref([])
  const detectTypes = [
    {
      label: '普通检测 ',
      value: '1',
    },
    {
      label: '增强检测',
      value: '2',
    },
  ]

  const sourceType = [
    {
      label: 'default',
      value: 'default',
    },
    {
      label: 'test',
      value: 'test',
    },
    {
      label: 'image',
      value: 'image',
    },
    {
      label: 'player',
      value: 'player',
    },
    {
      label: 'player_xunjian',
      value: 'player_xunjian',
    },
    {
      label: 'player_huigui',
      value: 'player_huigui',
    },
    {
      label: 'player_cexin',
      value: 'player_cexin',
    },
    {
      label: 'image_xunjian',
      value: 'image_xunjian',
    },
  ]

  const messageType = [
    {
      label: '花屏',
      value: '花屏',
    },
    {
      label: '黑屏',
      value: '黑屏',
    },
    {
      label: '绿屏',
      value: '绿屏',
    },
    {
      label: '白屏',
      value: '白屏',
    },
    {
      label: '偏色',
      value: '偏色',
    },
    {
      label: '块效应',
      value: '块效应',
    },
    {
      label: '头部马赛克',
      value: '头部马赛克',
    },
    {
      label: '检测到静音',
      value: '检测到静音',
    },
    {
      label: '卡顿异常',
      value: '卡顿异常',
    },
  ]

  const loading = ref(false)

  const state = reactive({
    tabValue: '视频标注',
    create_time: {},
    detect_type: '1',
    status: '',
    double_check: '',
    source: '',
    correctlationId: '',
    filekey: '',
    message: '',
    total: 0,
    data: [],
    TP: 0,
    FP: 0,
    FN: 0,
    TN: 0,
    selected_count: 0,
    page_num: 1,
    page_size: 10,
  })

  async function searchVideoDetectResult() {
    loading.value = true
    try {
      const payload = {
        request: {
          create_time: {
            start: state.create_time.start,
            end: state.create_time.end,
          },
          page_num: state.page_num,
          page_size: state.page_size,
        },
      }
      if (state.status) payload.request.status = state.status
      if (state.detect_type) payload.request.detect_type = state.detect_type
      if (state.double_check) payload.request.double_check = state.double_check
      if (state.source) payload.request.source = state.source
      if (state.correctlationId) payload.request.correctlationId = state.correctlationId
      if (state.filekey) payload.request.filekey = state.filekey
      if (state.message) payload.request.message = state.message
      const res = await getVideoDetectResult(payload)
      console.log('视频标注结果:', res)
      videoDetectResult.value = res.data.map(item => {
        try {
          // 清理数据中的非法控制字符
          const cleanData = str => {
            if (typeof str !== 'string') return str
            // 移除所有控制字符和特殊字符
            // eslint-disable-next-line no-control-regex
            return str.replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
              // 修复常见的 JSON 格式问题
              .replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3') // 给没有引号的键添加引号
              .replace(/(\w+)(\s*:)/g, '"$1"$2') // 确保所有键都有引号
              .replace(/'/g, '"') // 将单引号替换为双引号
              .replace(/(\w+)(\s*})/g, '"$1"$2') // 修复对象末尾的键
              .replace(/(\w+)(\s*])/g, '"$1"$2') // 修复数组末尾的项
          }

          // 安全地解析JSON
          const safeParse = data => {
            if (Array.isArray(data)) return data
            if (typeof data !== 'string') return data

            try {
              // 首先尝试直接解析
              return JSON.parse(data)
            } catch (e) {
              console.warn('直接解析失败，尝试清理后解析:', e)
              try {
                // 如果直接解析失败，尝试清理后解析
                const cleanedData = cleanData(data)
                return JSON.parse(cleanedData)
              } catch (e2) {
                console.warn('清理后解析仍然失败，返回原始数据:', e2)
                // 如果清理后仍然解析失败，尝试提取有效部分
                try {
                  // 尝试提取看起来像 JSON 的部分
                  const jsonMatch = data.match(/\{[\s\S]*\}|\[[\s\S]*\]/)
                  if (jsonMatch) {
                    return JSON.parse(cleanData(jsonMatch[0]))
                  }
                } catch (e3) {
                  console.warn('提取 JSON 部分失败:', e3)
                }
                return data
              }
            }
          }

          return {
            ...item,
            data: safeParse(item.data),
            videos: safeParse(item.videos),
            request_info: safeParse(item.request_info),
            selected: false,
          }
        } catch (error) {
          console.error('解析数据出错:', error)
          return {
            ...item,
            data: [],
            videos: [],
            request_info: {},
            selected: false,
          }
        }
      })
      console.log('处理后的视频数据:', videoDetectResult.value)
      state.total = res.total_length
      state.TP = res.TP
      state.FP = res.FP
      state.FN = res.FN
      state.TN = res.TN
      state.selected_count = 0
    } catch (error) {
      toast.danger('获取视频标注结果失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }
  function handleEdit(item) {
    if (item.status === 'pass' && item.double_check === 'pass') {
      state.TP -= 1
    } else if (item.status === 'pass' && item.double_check === 'fail') {
      state.FP -= 1
    } else if (item.status === 'fail' && item.double_check === 'pass') {
      state.FN -= 1
    } else if (item.status === 'fail' && item.double_check === 'fail') {
      state.TN -= 1
    }
    item.double_check = ''
  }

  async function markVideoDetectResult(id, doubleCheck) {
    try {
      const payload = {
        request: {
          id,
          params: {
            double_check: doubleCheck,
          },
        },
      }
      const res = await updateVideoDetectResult(payload)
      console.log(res)
      // Find the index of the item with matching ID

      const index = videoDetectResult.value.findIndex(item => item.id === res.data.id)
      // Update the item if found
      if (index !== -1) {
        if (res.data.double_check !== videoDetectResult.value[index].double_check) {
          videoDetectResult.value[index].double_check = res.data.double_check
          if (videoDetectResult.value[index].status === 'pass' && videoDetectResult.value[index].double_check === 'pass') {
            state.TP += 1
          } else if (videoDetectResult.value[index].status === 'pass' && videoDetectResult.value[index].double_check === 'fail') {
            state.FP += 1
          } else if (videoDetectResult.value[index].status === 'fail' && videoDetectResult.value[index].double_check === 'pass') {
            state.FN += 1
          } else if (videoDetectResult.value[index].status === 'fail' && videoDetectResult.value[index].double_check === 'fail') {
            state.TN += 1
          }
        }
      }
    } catch (error) {
      toast.danger('获取视频标注结果失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  function markSelectedVideoDetectResult(doubleCheck) {
    const selectedItems = videoDetectResult.value.filter(item => item.selected)
    selectedItems.forEach(item => {
      if (item.status === 'pass' && item.double_check === 'pass') {
        state.TP -= 1
      } else if (item.status === 'pass' && item.double_check === 'fail') {
        state.FP -= 1
      } else if (item.status === 'fail' && item.double_check === 'pass') {
        state.FN -= 1
      } else if (item.status === 'fail' && item.double_check === 'fail') {
        state.TN -= 1
      }
      markVideoDetectResult(item.id, doubleCheck)
    })
  }

  function submitPingCode2() {
    const ids = videoDetectResult.value.filter(item => item.selected).map(item => item.id)
    const payload = {
      request: {
        ids,
        owner: userEmail,
        detect_type: state.detect_type,
      },
    }
    submitVideoDetectResultToPingCode2(payload).then(res => {
      if (res.code === 0) {
        toast.success(`提交成功:${res.bugId}`)
      } else {
        toast.danger(`提交失败:${res.msg}`)
      }
    }).catch(error => {
      toast.danger(`提交失败:${error.message}`)
    }).finally(() => {
      searchVideoDetectResult()
    })
  }

  function handlePagination() {
    searchVideoDetectResult()
  }

  function handlepageSize(e) {
    state.page_size = e
    state.page_num = 1
    searchVideoDetectResult()
  }

  function clearParam() {
    state.create_time = {}
    state.detect_type = '1'
    state.status = ''
    state.double_check = ''
    state.source = ''
    state.selected_count = 0
    state.correctlationId = ''
    state.filekey = ''
    state.message = ''
  }

  function handleAllCheckbox() {
    if (state.selected_count === videoDetectResult.value.length) {
      videoDetectResult.value.forEach(item => {
        item.selected = false
      })
      state.selected_count = 0
    } else {
      videoDetectResult.value.forEach(item => {
        item.selected = true
      })
      state.selected_count = videoDetectResult.value.length
    }
  }

  function handleCheckbox(item) {
    item.selected = !item.selected
    if (item.selected) {
      state.selected_count += 1
    } else {
      state.selected_count -= 1
    }
  }

  function getStatistics() {
    const payload = {
      request: {
        create_time: state.create_time,
        detect_type: state.detect_type,
        source: state.source,
      },
    }
    getVideoDetectStatistics(payload).then(res => {
      chartData.value = res.data.map(item => ({
        ...item,
        week: item.week,
        准确率: item.accuracy,
        召回率: item.recall,
      }))
    })
  }

  function handleVideoError(event, item, index) {
    console.error(`视频加载失败: ${item.videos[index]}`, event)
    toast.danger(`视频加载失败: ${item.filekey}`)
  }

  function handleVideoLoaded(event, item, index) {
    console.log(`视频加载成功: ${item.videos[index]}`)
  }

  const copyData = data => {
    try {
      const textToCopy = Array.isArray(data) ? JSON.stringify(data, null, 2) : data
      // 创建临时文本区域
      const textarea = document.createElement('textarea')
      textarea.value = textToCopy
      // 设置样式使其不可见
      textarea.style.position = 'fixed'
      textarea.style.left = '-9999px'
      textarea.style.top = '0'
      document.body.appendChild(textarea)
      textarea.select()

      // 尝试使用 execCommand
      const successful = document.execCommand('copy')
      document.body.removeChild(textarea)

      if (successful) {
        toast.success('复制成功')
      } else if (navigator.clipboard?.writeText) {
        // 如果 execCommand 失败，尝试使用 clipboard API
        navigator.clipboard.writeText(textToCopy)
          .then(() => toast.success('复制成功'))
          .catch(() => toast.danger('复制失败'))
      } else {
        toast.danger('复制失败：浏览器不支持复制功能')
      }
    } catch (error) {
      console.error('复制失败:', error)
      toast.danger('复制失败')
    }
  }

  onMounted(() => {
    searchVideoDetectResult()
    getStatistics()
  })
</script>

<style>
/* 搜索组件样式 */
.search-container {
  margin-bottom: 20px;
}

.search-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin-bottom: 16px;
  align-items: start;
}

.search-row:last-child {
  margin-bottom: 0;
}

.search-item {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.search-item.search-actions-item {
  justify-content: flex-end;
  align-items: flex-end;
  grid-column: 3;
}

.search-label {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.search-control {
  width: 100%;
}

.search-field {
  width: 100%;
  min-width: 180px;
}

.help-icon {
  color: #999;
  cursor: help;
  font-size: 14px;
  margin-left: 6px;
  transition: color 0.2s ease;
}

.help-icon:hover {
  color: #1890ff;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  margin-top: 22px;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .search-row {
    gap: 20px;
  }

  .search-field {
    min-width: 160px;
  }
}

@media (max-width: 1400px) {
  .search-row {
    gap: 16px;
    margin-bottom: 14px;
  }

  .search-field {
    min-width: 140px;
  }
}

@media (max-width: 1200px) {
  .search-row {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .search-item.search-actions-item {
    grid-column: 2;
    justify-self: end;
  }

  .search-actions {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .search-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .search-item.search-actions-item {
    grid-column: 1;
    justify-self: center;
  }

  .search-actions {
    justify-content: center;
    width: 100%;
    margin-top: 16px;
  }
}

@media (max-width: 1200px) {
  .search-row {
    justify-content: flex-start;
  }

  .search-item {
    margin-right: 12px;
  }

  .search-select,
  .search-input {
    width: 120px;
  }

  .search-label {
    min-width: 60px;
  }
}

@media (max-width: 768px) {
  .search-container {
    padding: 12px;
  }

  .search-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .search-item {
    margin-right: 0;
    margin-bottom: 4px;
    justify-content: space-between;
  }

  .search-label {
    min-width: 80px;
  }

  .search-select,
  .search-input {
    width: 200px;
  }

  .search-actions {
    margin-left: 0;
    justify-content: center;
    margin-top: 8px;
  }
}

/* 卡片样式 */
.result_card {
  height: 325px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 悬停效果 */
.result_card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  /* 轻微上浮 */
}

/* 响应式适配：小屏幕时卡片占满宽度 */
@media (max-width: 768px) {
  .result_card {
    flex-basis: 100% !important;
  }
}
</style>
