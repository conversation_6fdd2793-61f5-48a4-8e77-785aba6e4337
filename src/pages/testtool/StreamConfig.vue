<!-- ignore eslint -->
<template>
  <BeerPanel :bs="{ boxShadow: 'none', flex: 1, marginTop: 20, pt: 0 }">
    <BeerTitle :bs="{ mb: 16 }"> 工作流码流配置 </BeerTitle>
    <BeerDivider />
    <br>
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >工作流名称:</Text>
      <Input
        v-model="state.workflow_name"
        :style="{ width: '200px' }"
        clearable
        placeholder="请输入工作流名称"
      />
      <Text
        bold
        style="margin-left:10px;"
      >版本:</Text>
      <Input
        v-model="state.version"
        :style="{ width: '120px' }"
        clearable
        placeholder="请输入版本"
      />
      <Text
        bold
        style="margin-left:10px;"
      >码流类型:</Text>
      <Input
        v-model="state.stream_type"
        :style="{ width: '120px' }"
        clearable
        placeholder="请输入码流类型"
      />
      <Text
        bold
        style="margin-left:10px;"
      >环境:</Text>
      <Select
        v-model="state.env"
        :options="envOptions"
        :style="{ width: '120px' }"
        clearable
        placeholder="请选择环境"
      />
      <Text
        bold
        style="margin-left:10px;"
      >云类型:</Text>
      <Select
        v-model="state.cloud_type"
        :options="cloudTypeOptions"
        :style="{ width: '120px' }"
        clearable
        placeholder="请选择云类型"
      />
      <Text
        bold
        style="margin-left:10px;"
      >媒体类型:</Text>
      <Select
        v-model="state.media_type"
        :options="mediaTypeOptions"
        :style="{ width: '140px' }"
        clearable
        placeholder="请选择媒体类型"
      />
      <div style="display: flex; align-items: center;">
        <Button
          style="float:right;margin-left:10px;"
          type="primary"
          :icon="{ icon: Search }"
          @click="searchStreamtypeList()"
        >查询</Button>
        <Button
          style="float:right;margin-left:10px;"
          type="secondary"
          :icon="{ icon: Clear }"
          @click="clearParam"
        >重置</Button>
        <Button
          style="float:right;margin-left:10px;"
          type="primary"
          :icon="{ icon: Plus }"
          @click="handleCreate"
        >创建</Button>
      </div>
    </NewBsBox>
    <br>

    <Spinner
      :spinning="loading"
      tip="查询中"
      size="large"
    >
      <Table
        :columns="columns"
        :data-source="streamtypeList"
        :pagination="false"
        :loading="loading"
        row-key="id"
      >
        <template #create_time="{ rowData }">
          <Text size="small">{{ formatDateTime(rowData.create_time) }}</Text>
        </template>
        <template #update_time="{ rowData }">
          <Text size="small">{{ formatDateTime(rowData.update_time) }}</Text>
        </template>
        <template #operation="{ rowData }">
          <Space>
                          <Button
                size="small"
                type="primary"
                :icon="{ icon: Edit }"
                @click="handleEdit(rowData)"
              >编辑</Button>
            <Popconfirm
              title="确定要删除这条记录吗？"
              @confirm="handleDelete(rowData)"
            >
              <Button
                size="small"
                type="danger"
                :icon="{ icon: Delete }"
              >删除</Button>
            </Popconfirm>
          </Space>
        </template>
      </Table>
    </Spinner>

    <NewBsBox :bs="{ display: 'flex', margin: 20, alignItems: 'center', justifyContent: 'flex-end' }">
      <Pagination
        v-model="state.page_num"
        :total="state.total"
        :page-size-options="pageSizeOptions"
        @update:modelValue="handlePagination"
        @update:pageSize="handlepageSize"
      />
            </NewBsBox>
  </BeerPanel>

  <!-- 编辑弹窗 -->
  <Modal
    v-model:visible="editModalVisible"
    :title="isCreateMode ? '创建工作流码流配置' : '编辑工作流码流配置'"
    :with-footer="true"
    :style="{ width: '500px' }"
  >
    <Form
      ref="editFormRef"
      :model="editForm"
      label-width="100px"
    >
      <FormItem name="workflow_name" label="工作流名称" required>
        <Input
          v-model="editForm.workflow_name"
          placeholder="请输入工作流名称"
          clearable
          :status="workflowNameError ? 'error' : undefined"
          :help="workflowNameError ? '工作流名称不能为空' : undefined"
        />
      </FormItem>
      <FormItem name="version" label="版本">
        <Input
          v-model="editForm.version"
          placeholder="请输入版本"
          clearable
        />
      </FormItem>
      <FormItem name="stream_type" label="码流类型">
        <Input
          v-model="editForm.stream_type"
          placeholder="请输入码流类型"
          clearable
        />
      </FormItem>
      <FormItem name="env" label="环境">
        <Select
          v-model="editForm.env"
          :options="envOptions"
          placeholder="请选择环境"
          clearable
        />
      </FormItem>
      <FormItem name="cloud_type" label="云类型">
        <Select
          v-model="editForm.cloud_type"
          :options="cloudTypeOptions"
          placeholder="请选择云类型"
          clearable
        />
      </FormItem>
      <FormItem name="media_type" label="媒体类型">
        <Select
          v-model="editForm.media_type"
          :options="mediaTypeOptions"
          placeholder="请选择媒体类型"
          clearable
        />
      </FormItem>
    </Form>
    <template #footer>
      <Space>
        <Button
          type="primary"
          @click="handleEditSubmit"
        >确定</Button>
        <Button @click="editModalVisible = false">取消</Button>
      </Space>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted, computed } from 'vue'
  import {
    useStore,
  } from 'vuex'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  import {
    Search, Clear, Edit, Delete, Plus,
  } from '@xhs/delight/icons'
  import {
    Select, Button, Text, Spinner, toast, Pagination, Input, Table, Space, Popconfirm, Modal, Form, FormItem,
  } from '@xhs/delight'
  import {
    queryWorkflowStreamtypeList, updateWorkflowStreamtypeList, deleteWorkflowStreamtypeList, uploadWorkflowStreamtypeList,
  } from '../../services/task'

  // 时间格式化函数
  function formatDateTime(dateTimeStr: string) {
    if (!dateTimeStr) return ''
    try {
      const date = new Date(dateTimeStr)
      if (isNaN(date.getTime())) return dateTimeStr
      
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
      return dateTimeStr
    }
  }

  const pageSizeOptions = [10, 20, 50, 100, 200]

  const store = useStore()

  const loading = ref(false)
  const streamtypeList = ref([] as any[])
  const editModalVisible = ref(false)
  const editFormRef = ref()
  const isCreateMode = ref(false)
  const editForm = ref({
    id: '',
    workflow_name: '',
    version: '',
    stream_type: '',
    env: '',
    cloud_type: '',
    media_type: '',
  })

  // 监听工作流名称变化，实时校验
  const workflowNameError = computed(() => {
    return isCreateMode.value && (!editForm.value.workflow_name || editForm.value.workflow_name.trim() === '')
  })

  const envOptions = [
    { label: 'sit', value: 'sit' },
    { label: 'beta', value: 'beta' },
    { label: 'prod', value: 'prod' },
  ]

  const cloudTypeOptions = [
    { label: 'qc', value: 'qc' },
    { label: 'al', value: 'al' },
    { label: 'ros', value: 'ros' },
    { label: 'alsg', value: 'alsg' },
  ]

  const mediaTypeOptions = [
    { label: 'video', value: 'video' },
    { label: 'audio', value: 'audio' },
    { label: 'thumb', value: 'thumb' },
    { label: 'frame', value: 'frame' },
  ]

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工作流名称',
      dataIndex: 'workflow_name',
      key: 'workflow_name',
      width: 300,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
    },
    {
      title: '码流类型',
      dataIndex: 'stream_type',
      key: 'stream_type',
      width: 150,
    },
    {
      title: '环境',
      dataIndex: 'env',
      key: 'env',
      width: 120,
    },
    {
      title: '云类型',
      dataIndex: 'cloud_type',
      key: 'cloud_type',
      width: 120,
    },
    {
      title: '媒体类型',
      dataIndex: 'media_type',
      key: 'media_type',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 200,
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 200,
    },
    {
      title: '操作',
      key: 'operation',
      dataIndex: 'operation',
      width: 200,
      fixed: 'right' as const,
    },
  ] as any

  const state = reactive({
    workflow_name: '',
    version: '',
    stream_type: '',
    env: '',
    cloud_type: '',
    media_type: '',
    total: 0,
    page_num: 1,
    page_size: 10,
  } as any)

  async function searchStreamtypeList() {
    loading.value = true
    try {
      const payload = {
        request: {
          page_num: state.page_num,
          page_size: state.page_size,
        } as any,
      }
      if (state.workflow_name) payload.request.workflow_name = state.workflow_name
      if (state.version) payload.request.version = state.version
      if (state.stream_type) payload.request.stream_type = state.stream_type
      if (state.env) payload.request.env = state.env
      if (state.cloud_type) payload.request.cloud_type = state.cloud_type
      if (state.media_type) payload.request.media_type = state.media_type
      
      const res = await queryWorkflowStreamtypeList(payload)
      console.log('工作流码流配置结果:', res)
      
      if (res.status === 0) {
        streamtypeList.value = res.data || []
        state.total = res.dataLength || 0
        console.log('设置的数据:', streamtypeList.value)
      } else {
        toast.danger(res.message || '查询失败')
      }
    } catch (error) {
      toast.danger('获取工作流码流配置失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  function handleCreate() {
    console.log('创建新配置')
    // 重置表单
    editForm.value = {
      id: '',
      workflow_name: '',
      version: '',
      stream_type: '',
      env: '',
      cloud_type: '',
      media_type: '',
    }
    // 设置为创建模式
    isCreateMode.value = true
    // 显示弹窗
    editModalVisible.value = true
  }

  function handleEdit(record: any) {
    console.log('编辑记录:', record)
    // 设置为编辑模式
    isCreateMode.value = false
    // 填充编辑表单
    editForm.value = {
      id: record.id,
      workflow_name: record.workflow_name,
      version: record.version,
      stream_type: record.stream_type,
      env: record.env,
      cloud_type: record.cloud_type,
      media_type: record.media_type,
    }
    // 显示编辑弹窗
    editModalVisible.value = true
  }

  async function handleEditSubmit() {
    try {
      let payload
      if (isCreateMode.value) {
        // 创建模式校验
        if (!editForm.value.workflow_name || editForm.value.workflow_name.trim() === '') {
          toast.danger('工作流名称不能为空')
          return
        }
        
        // 创建模式 - 使用upload接口的格式
        payload = {
          request: {
            workflow_name: editForm.value.workflow_name,
            version: parseInt(editForm.value.version) || 0,
            stream_types: [editForm.value.stream_type], // 转换为数组格式
            env: editForm.value.env,
            cloud_type: editForm.value.cloud_type, // 使用cloudType字段名
            media_type: editForm.value.media_type,
          } as any,
        }
        const res = await uploadWorkflowStreamtypeList(payload)
        console.log('创建结果:', res)
        
        if (res.status === 0) {
          toast.success(res.message || '创建成功')
          editModalVisible.value = false
          searchStreamtypeList()
        } else {
          toast.danger(res.message || '创建失败')
        }
      } else {
        // 编辑模式 - 使用update接口的格式
        payload = {
          request: {
            id: editForm.value.id,
            workflow_name: editForm.value.workflow_name,
            version: editForm.value.version,
            stream_type: editForm.value.stream_type,
            env: editForm.value.env,
            cloud_type: editForm.value.cloud_type,
            media_type: editForm.value.media_type,
          } as any,
        }
        const res = await updateWorkflowStreamtypeList(payload)
        console.log('编辑结果:', res)
        
        if (res.status === 0) {
          toast.success(res.message || '编辑成功')
          editModalVisible.value = false
          searchStreamtypeList()
        } else {
          toast.danger(res.message || '编辑失败')
        }
              }
      } catch (error) {
        toast.danger(isCreateMode.value ? '创建失败' : '编辑失败')
        console.error(error)
      }
  }

  async function handleDelete(record: any) {
    try {
      const payload = {
        request: {
          id: record.id,
        },
      }
      
      const res = await deleteWorkflowStreamtypeList(payload)
      console.log('删除结果:', res)
      
      if (res.status === 0) {
        toast.success(res.message || '删除成功')
        // 刷新列表数据
        searchStreamtypeList()
      } else {
        toast.danger(res.message || '删除失败')
      }
    } catch (error) {
      toast.danger('删除失败')
      console.error(error)
    }
  }

  function handlePagination() {
    searchStreamtypeList()
  }

  function handlepageSize(e: any) {
    state.page_size = e
    state.page_num = 1
    searchStreamtypeList()
  }

  function clearParam() {
    state.workflow_name = ''
    state.version = ''
    state.stream_type = ''
    state.env = ''
    state.cloud_type = ''
    state.media_type = ''
    state.page_num = 1
  }

  onMounted(() => {
    searchStreamtypeList()
  })
</script>

<style>
/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}
</style>
