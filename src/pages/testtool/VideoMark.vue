<!-- ignore eslint -->
<template>
  <BeerPanel :bs="{ boxShadow: 'none', flex: 1, marginTop: 20, pt: 0 }">
    <BeerTitle :bs="{ mb: 16 }"> 模型标注 </BeerTitle>
    <BeerDivider />
    <br>
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >创建时间:</Text>
      <DateRangePicker
        v-model="state.create_time"
        unit="second"
        :style="{ width: 'auto' }"
        :placeholder="{ start: '开始日期', end: '结束日期' }"
      />
      <Text
        bold
        style="margin-left:10px;"
      >检测类型:</Text>
      <Tooltip>
        <template #content>
          检测类型:<br>
          1-单图大模型检测<br>
          2-两张图像素级对比<br>
          3-UI自动化断言检测
        </template>
        <Icon
          :icon="Help"
          :style="{ marginRight: '10px' }"
        />
      </Tooltip>
      <Select
        v-model="state.detect_type"
        :options="detectTypes"
        :style="{ width: '200px' }"
        clearable
        placeholder="默认为1"
      />
      <Text
        bold
        style="margin-left:10px;"
      >correctlationId:</Text>
      <Tooltip>
        <template #content>
          关联id
        </template>
        <Icon
          :icon="Help"
          :style="{ marginRight: '10px' }"
        />
      </Tooltip>
      <Input
        v-model="state.correctlationId"
        :style="{ width: '200px' }"
        clearable
        placeholder="请输入关联id"
      />
    </NewBsBox>
    <br>
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >检测状态:</Text>
      <Select
        v-model="state.status"
        :options="[{ label: 'PASS', value: 'pass' }, { label: 'FAIL', value: 'fail' }, { label: '无', value: 'N/T' }]"
        :style="{ width: '150px' }"
        clearable
      />
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >标注状态:</Text>
      <Select
        v-model="state.double_check"
        :options="[{ label: 'PASS', value: 'pass' }, { label: 'FAIL', value: 'fail' }, { label: '未标注', value: 'uncheck' }]"
        :style="{ width: '150px' }"
        clearable
      />
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >source:</Text>
      <Select
        v-model="state.source"
        :options="sourceType"
        :style="{ width: '170px' }"
        clearable
      />
      <Button
        style="float:right;margin-left:200px;"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchVideoAnalysisResult()"
      >查询</Button>
      <Button
        style="float:right;margin-left:10px;"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>
    <Tabs
      v-model="state.tabValue"
      style="margin-top: 10px;"
    >
      <TabPane label="模型标注">
        <br>
        <NewBsBox :bs="{ display: 'flex', justifyContent: 'space-around', width: '100%' }">
          <Banner
            align="center"
            type="success"
            :closeable="false"
            :style="{ width: '45%', borderRadius: '8px' }"
          >
            <template
              v-if="state.detect_type !== '2'"
              #description
            >
              准确率: {{ (((state.TP + state.TN) / (state.TP + state.TN + state.FP + state.FN)) * 100).toFixed(2) }}%
            </template>
            <template
              v-else
              #description
            >
              准确率: 100%
            </template>
            <template #icon>
              <Icon :icon="CheckCorrect" />
            </template>
          </Banner>
          <Banner
            align="center"
            :closeable="false"
            :style="{ width: '45%', borderRadius: '8px' }"
          >
            <template
              v-if="state.detect_type !== '2'"
              #description
            >
              召回率: {{ ((state.TP / (state.TP + state.FN)) * 100).toFixed(2) }}%
            </template>
            <template
              v-else
              #description
            >
              召回率: 100%
            </template>
            <template #icon>
              <Icon
                :icon="LoopOnce"
                :rotate="180"
              />
            </template>
          </Banner>
        </NewBsBox>
        <br>

        <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
          <Checkbox
            :checked="state.selected_count === videoAnalysisResult.length"
            :indeterminate="state.selected_count > 0 && state.selected_count < videoAnalysisResult.length"
            :disabled="state.total === 0"
            style="margin-left:20px;margin-right:20px"
            label="全部查询结果"
            description="点击全选/取消全选"
            @click="handleAllCheckbox"
          />
          <Button
            size="small"
            style="margin-right: 10px; background-color:#28BC77; color:white;"
            @click="markSelectedVideoAnalysisResult('pass')"
          >批量通过</Button>
          <Button
            size="small"
            style="margin-right: 10px; background-color:#f03860; color:white;"
            @click="markSelectedVideoAnalysisResult('fail')"
          >批量失败</Button>
          <Button
            size="small"
            style="margin-right: 10px;background-color:#0038a0; color:white;"
            @click="submitPingCode2"
          >一键PingCode2</Button>
          <Tooltip placement="right">
            <template #content>
              选择需要提交Pingcode2的卡片后<br>
              点击左侧按钮提交<br>
              所有卡片生成一个需求<br>
              查看链接：https://pingcode2.devops.xiaohongshu.com/my-space/84/0000001425/tasks?viewId=3&tab=detail
            </template>
            <Icon
              :icon="Help"
              :style="{ marginRight: '10px' }"
            />
          </Tooltip>
        </NewBsBox>
        <br>

        <Spinner
          :spinning="loading"
          tip="查询中"
          size="large"
        >
          <NewBsBox
            :bs="{
              display: 'flex',
              flexWrap: 'wrap', // 关键：允许换行
              justifyContent: 'flex-start',
              alignItems: 'center',
              gap: 10, // 替代 margin，统一间距
            }"
          >
            <template
              v-for="item in videoAnalysisResult"
              :key="item.id"
            >
              <!-- 每个卡片 -->
              <NewBsBox
                class="result_card"
                :bs="{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                  padding: 10,
                  flexShrink: 0 // 禁止压缩
                }"
              >
                <!-- 图片固定尺寸 + 保持比例 -->
                <img
                  :src="item.images[0]"
                  :style="{
                    maxHeight: '90%',
                    objectFit: 'cover', // 避免拉伸
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px' // 可选：图片圆角
                  }"
                  @click="viewImgs(item.images, {
                    closeOnMask: true,
                  })"
                >
                <img
                  v-if="item.detect_type === '2'"
                  :src="item.images[1]"
                  :style="{
                    marginLeft: '5px',
                    maxHeight: '90%',
                    objectFit: 'cover', // 避免拉伸
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px' // 可选：图片圆角
                  }"
                  @click="viewImgs(item.images, {
                    closeOnMask: true,
                  })"
                >
                <!-- 文字信息 -->
                <Card
                  :bordered="false"
                  :style="{
                    width: '225px',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                  }"
                  :title="`数据Id:${item.id}`"
                >
                  <template #extra>
                    <Checkbox
                      :checked="item.selected"
                      @click="handleCheckbox(item)"
                    />
                  </template>
                  <Tooltip placement="left">
                    <template #content>
                      {{ item.create_time }}
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >创建时间: {{ item.create_time
                    }}</Text><br>
                  </Tooltip>
                  <Tooltip
                    v-if="item.double_check"
                    placement="left"
                  >
                    <template #content>
                      {{ item.update_time }}
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >更新时间: {{ item.update_time
                    }}</Text><br>
                  </Tooltip>
                  <Text
                    bold
                    style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                  >场景: {{ item.scene_type }}</Text><br>
                  <Text
                    bold
                    style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                  >检测类型: {{ (detectTypes.find(type =>
                    type.value === item.detect_type))?.label }}</Text><br>
                  <div v-if="item.double_check">
                    <Text
                      bold
                      style="margin-right:5px;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      标注状态:
                    </Text>
                    <Text
                      v-if="item.double_check === 'pass'"
                      bold
                      style="color: #28BC77;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.double_check }}
                    </Text>
                    <Text
                      v-else-if="item.double_check === 'fail'"
                      bold
                      style="color: #f03860;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.double_check }}
                    </Text>
                    <Icon
                      :icon="Write"
                      style="cursor: pointer; margin-left: 8px;"
                      @click="handleEdit(item)"
                    />
                    <br>
                    <Text
                      bold
                      style="margin-right:5px;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      检测状态:
                    </Text>
                    <Text
                      v-if="item.status === 'pass'"
                      bold
                      style="color: #28BC77;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.status }}
                    </Text>
                    <Text
                      v-else-if="item.status === 'fail'"
                      bold
                      style="color: #f03860;font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      {{ item.status }}
                    </Text><br>
                    <Tooltip placement="left">
                      <template #content>
                        {{ item.message }}
                      </template>
                      <Text
                        bold
                        style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                      >
                        检测信息: {{ item.message }}
                      </Text><br>
                    </Tooltip>
                  </div>
                  <Text
                    bold
                    style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                  >
                    source: {{ item.request_info.source }}
                  </Text><br>
                  <Tooltip placement="left">
                    <template #content>
                      <div
                        v-for="(value, key) in item.request_info"
                        :key="key"
                      >
                        {{ key }}: {{ value }}
                      </div>
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      request_info: {{ item.request_info }}
                    </Text><br>
                  </Tooltip>
                  <Tooltip
                    v-if="item.double_check && item.data && item.data.length > 0 && item.data[0].remark"
                    placement="left"
                  >
                    <template #content>
                      {{ item.data[0].remark }}
                    </template>
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      remark: {{ item.data[0].remark }}
                    </Text><br>
                  </Tooltip>
                  <template
                    v-if="!item.double_check"
                    #footer
                  >
                    <Text
                      bold
                      style="font-size: 14px; white-space: nowrap; overflow: hidden;"
                    >
                      标注状态:
                    </Text>
                    <div style="display: flex; justify-content: space-between; width: 100%">
                      <Button
                        size="small"
                        style="flex: 1; margin-right: 10px; background-color:#28BC77; color:white;"
                        @click="markVideoAnalysisResult(item.id, 'pass')"
                      >PASS</Button>
                      <Button
                        size="small"
                        style="flex: 1; background-color:#f03860; color:white;"
                        @click="markVideoAnalysisResult(item.id, 'fail')"
                      >FAIL</Button>
                    </div>
                  </template>
                </Card>
              </NewBsBox>
            </template>
          </NewBsBox>
        </Spinner>
        <NewBsBox :bs="{ display: 'flex', margin: 20, alignItems: 'center', justifyContent: 'flex-end' }">
          <Pagination
            v-model="state.page_num"
            :total="state.total"
            :page-size-options="pageSizeOptions"
            @update:modelValue="handlePagination"
            @update:pageSize="handlepageSize"
          />
        </NewBsBox>
      </TabPane>
      <TabPane label="统计分析">
        <br>
        <Line
          v-if="videoAnalysisResult.filter(item => item.detect_type === '2').length === 0"
          :chart-data="chartData"
          :dimensions="dimensions"
          :metrics="metrics"
          :loading="loading"
          :chart-config="chartConfig"
          :colors="['#28BC77', '#1166CC']"
          style="width: 90%; height: auto; margin: 0 auto;"
        />
      </TabPane>
    </Tabs>
  </BeerPanel>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import {
    useStore,
  } from 'vuex'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  import {
    Search, Clear, Write, Help, CheckCorrect, LoopOnce,
    //  Delete,Notes,
  } from '@xhs/delight/icons'
  import {
    Select, Button, Text, DateRangePicker, Spinner, Icon, Tooltip, toast, Pagination, viewImgs, Card, Checkbox, Banner, Tabs, TabPane,Input,
  } from '@xhs/delight'
  import { Line } from '@xhs/delight-charts'
  import {
    getVideoAnalysisResult, updateVideoAnalysisResult, getVideoAnalysisStatistics, submitVideoAnalysisResultToPingCode2,
  } from '../../services/task'
  import { pageSizeOptions } from '../../utils/common'

  const store = useStore()
  const userEmail = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email : '路人'
  const dimensions = ref(['week'])
  const metrics = ref(['准确率', '召回率'])

  const chartConfig = ref({
    yAxis: {
      valueType: 'percent',
      digit: 0,
    },
    tooltip: {
      valueType: 'percent',
      digit: 0,
    },
  })
  const chartData = ref([])

  const videoAnalysisResult = ref([])
  const detectTypes = [
    {
      label: '单图大模型检测 ',
      value: '1',
    },
    {
      label: '两张图像素级对比',
      value: '2',
    },
    {
      label: 'UI自动化断言检测',
      value: '3',
    },
  ]

  const sourceType = [
    {
      label: 'default',
      value: 'default',
    },
    {
      label: 'test',
      value: 'test',
    },
    {
      label: 'image',
      value: 'image',
    },
    {
      label: 'player',
      value: 'player',
    },
    {
      label: 'image_xunjian',
      value: 'image_xunjian',
    },
    {
      label: 'player_xunjian',
      value: 'player_xunjian',
    },
    {
      label: 'player_huigui',
      value: 'player_huigui',
    },
    {
      label: 'player_cexin',
      value: 'player_cexin',
    },
  ]

  const loading = ref(false)

  const state = reactive({
    tabValue: '模型标注',
    create_time: {},
    detect_type: '1',
    status: '',
    correctlationId: '',
    double_check: '',
    source: '',
    total: 0,
    TP: 0,
    FP: 0,
    FN: 0,
    TN: 0,
    selected_count: 0,
    page_num: 1,
    page_size: 10,
  })

  async function searchVideoAnalysisResult() {
    loading.value = true
    try {
      const payload = {
        request: {
          create_time: {
            start: state.create_time.start,
            end: state.create_time.end,
          },
          page_num: state.page_num,
          page_size: state.page_size,
        },
      }
      if (state.status) payload.request.status = state.status
      if (state.detect_type) payload.request.detect_type = state.detect_type
      if (state.double_check) payload.request.double_check = state.double_check
      if (state.correctlationId) payload.request.correctlationId = state.correctlationId
      if (state.source) payload.request.source = state.source
      const res = await getVideoAnalysisResult(payload)
      // console.log(res)
      videoAnalysisResult.value = res.data.map(item => ({
        ...item,
        data: JSON.parse(item.data),
        images: JSON.parse(item.images),
        request_info: JSON.parse(item.request_info),
        selected: false,
      }))
      // console.log(videoAnalysisResult.value)
      state.total = res.total_length
      state.TP = res.TP
      state.FP = res.FP
      state.FN = res.FN
      state.TN = res.TN
      state.selected_count = 0
    } catch (error) {
      toast.danger('获取视频分析结果失败')
      // console.error(error)
    } finally {
      loading.value = false
    }
  }
  function handleEdit(item) {
    if (item.status === 'pass' && item.double_check === 'pass') {
      state.TP -= 1
    } else if (item.status === 'pass' && item.double_check === 'fail') {
      state.FP -= 1
    } else if (item.status === 'fail' && item.double_check === 'pass') {
      state.FN -= 1
    } else if (item.status === 'fail' && item.double_check === 'fail') {
      state.TN -= 1
    }
    item.double_check = ''
  }

  async function markVideoAnalysisResult(id, doubleCheck) {
    try {
      const payload = {
        request: {
          id,
          params: {
            double_check: doubleCheck,
          },
        },
      }
      const res = await updateVideoAnalysisResult(payload)
      // console.log(res)
      // Find the index of the item with matching ID

      const index = videoAnalysisResult.value.findIndex(item => item.id === res.data.id)
      // Update the item if found
      if (index !== -1) {
        if (res.data.double_check !== videoAnalysisResult.value[index].double_check) {
          videoAnalysisResult.value[index].double_check = res.data.double_check
          if (videoAnalysisResult.value[index].status === 'pass' && videoAnalysisResult.value[index].double_check === 'pass') {
            state.TP += 1
          } else if (videoAnalysisResult.value[index].status === 'pass' && videoAnalysisResult.value[index].double_check === 'fail') {
            state.FP += 1
          } else if (videoAnalysisResult.value[index].status === 'fail' && videoAnalysisResult.value[index].double_check === 'pass') {
            state.FN += 1
          } else if (videoAnalysisResult.value[index].status === 'fail' && videoAnalysisResult.value[index].double_check === 'fail') {
            state.TN += 1
          }
        }
      }
    } catch (error) {
      toast.danger('获取视频分析结果失败')
      // console.error(error)
    } finally {
      loading.value = false
    }
  }

  function markSelectedVideoAnalysisResult(doubleCheck) {
    const selectedItems = videoAnalysisResult.value.filter(item => item.selected)
    selectedItems.forEach(item => {
      if (item.status === 'pass' && item.double_check === 'pass') {
        state.TP -= 1
      } else if (item.status === 'pass' && item.double_check === 'fail') {
        state.FP -= 1
      } else if (item.status === 'fail' && item.double_check === 'pass') {
        state.FN -= 1
      } else if (item.status === 'fail' && item.double_check === 'fail') {
        state.TN -= 1
      }
      markVideoAnalysisResult(item.id, doubleCheck)
    })
  }

  function submitPingCode2() {
    const ids = videoAnalysisResult.value.filter(item => item.selected).map(item => item.id)
    const payload = {
      request: {
        ids,
        owner: userEmail,
        detect_type: state.detect_type,
      },
    }
    submitVideoAnalysisResultToPingCode2(payload).then(res => {
      if (res.code === 0) {
        toast.success(`提交成功:${res.bugId}`)
      } else {
        toast.danger(`提交失败:${res.msg}`)
      }
    }).catch(error => {
      toast.danger(`提交失败:${error.message}`)
    }).finally(() => {
      searchVideoAnalysisResult()
    })
  }

  function handlePagination() {
    searchVideoAnalysisResult()
  }

  function handlepageSize(e) {
    state.page_size = e
    state.page_num = 1
    searchVideoAnalysisResult()
  }

  function clearParam() {
    state.create_time = {}
    state.detect_type = '1'
    state.status = ''
    state.double_check = ''
    state.source = ''
    state.selected_count = 0
    state.correctlationId = ''
  }

  function handleAllCheckbox() {
    if (state.selected_count === videoAnalysisResult.value.length) {
      videoAnalysisResult.value.forEach(item => {
        item.selected = false
      })
      state.selected_count = 0
    } else {
      videoAnalysisResult.value.forEach(item => {
        item.selected = true
      })
      state.selected_count = videoAnalysisResult.value.length
    }
  }

  function handleCheckbox(item) {
    item.selected = !item.selected
    if (item.selected) {
      state.selected_count += 1
    } else {
      state.selected_count -= 1
    }
  }

  function getStatistics() {
    const payload = {
      request: {
        create_time: state.create_time,
        detect_type: state.detect_type,
        source: state.source,
      },
    }
    getVideoAnalysisStatistics(payload).then(res => {
      chartData.value = res.data.map(item => ({
        ...item,
        week: item.week,
        准确率: item.accuracy,
        召回率: item.recall,
      }))
    })
  }

  onMounted(() => {
    searchVideoAnalysisResult()
    getStatistics()
  })
</script>

<style>
/* 卡片样式 */
.result_card {
  height: 310px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 悬停效果 */
.result_card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  /* 轻微上浮 */
}

/* 响应式适配：小屏幕时卡片占满宽度 */
@media (max-width: 768px) {
  .result_card {
    flex-basis: 100% !important;
  }
}
</style>
