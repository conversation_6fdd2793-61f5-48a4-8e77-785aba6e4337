<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >服务名:</Text>
      <Input
        v-model="state.sourceName"
        :style="{ width: '180px', }"
      />
      <!-- <Text
        bold
        style="margin-left:10px"
      >任务名称:</Text>
      <Input
        v-model="state.taskComment"
        :style="{ width: '120px' ,}"
      />
      <Text
        bold
        style="margin-left:10px"
      >执行人:</Text>
      <Input
        v-model="state.userName"
        :style="{ width: '120px' ,}"
      /> -->
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchTaskRecord()"
      >查询</Button>
      <Button
        style="float:right;margin:10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex',boxShadow: 'none', ml: 30, flex: 1,alignItems: 'center' }">
      <Button
        :style="{ backgroundColor: '#28BC77', color: 'white',marginLeft: '-50px', }"
        :icon="{ icon: Add }"
        @click="handleAddJob"
      >创建服务</Button>
    </NewBsBox>
    <!-- <NewBsBox :bs="{ display: 'flex',boxShadow: 'none', ml: 30, flex: 1,alignItems: 'center' }">
      <Button
        :style="{ backgroundColor: '#28BC77', color: 'white',marginLeft: '-50px', }"
        :icon="{ icon: Add }"
        @click="testNhj"
      >测试</Button>
    </NewBsBox> -->
  </Space>
  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <Table2
      :columns="perftaskcolumns"
      :data-source="state.dataSource"
      :table-layout="auto"
      :loading="loading"
      size="small"
    >
      <template #sourceEnv="{ rowData }">
        <Tag color="green">{{ rowData.sourceEnv }}</Tag>
      </template>
      <template #sourceFrom="{ rowData }">
        <Text>{{ rowData.sourceFrom ===1 ? "算法" : "音视频" }}</Text>
      </template>
      <template #sourceType="{ rowData }">
        <Text>{{ rowData.sourceType ===1 ? "RPC" : "HTTP" }}</Text>
      </template>
      <template #operation2="{ rowData }">
        <Link
          :style="{margin: '5px',}"
          @click="getAllApi(rowData)"
        >获取接口</Link>
        <Link
          :style="{margin: '5px',}"
          @click="deleteRpc(rowData)"
        >删除</Link>
        <Link
          :style="{margin: '5px',}"
          @click="updateSever(rowData)"
        >更新</Link>
      </template>
    </Table2>
    <NewBsBox :bs="{ margin: 20, }">
      <Pagination
        v-model="state.pageNum"
        :total="state.dataLength"
        align="end"
        @update:modelValue="handlePagination"
        @update:pageSize="handlepageSize"
      />
    </NewBsBox>
  </NewBsBox>
  <Modal
    v-model:visible="showTaskModal"
    :title="state.isEdit==1?'编辑服务':'创建服务'"
    :with-footer="false"
    :style="{ width: 'auto' }"
    @cancel="handleTClose"
  >
    <Form
      ref="formRef"
      :label-position="labelPosition"
      :rules="rules"
      :model="taskModel"
      label-width="150px"
    >
      <FormItem label="服务名">
        <Input
          v-model="taskModel.sourceName"
          placeholder="服务名"
        />
      </FormItem>
      <FormItem label="服务描述">
        <Input
          v-model="taskModel.sourceComment"
          placeholder="服务描述"
        />
      </FormItem>
      <FormItem
        name="sourceEnv"
        label="执行环境"
        help="选择环境"
        on-error="必填项"
      >
        <RadioGroup
          v-model="taskModel.sourceEnv"
          :options="[{ label: 'sit', value: 'sit' }, { label: 'staging', value: 'staging' },{ label: 'prod', value: 'prod' }]"
          required
        />
      </FormItem>
      <FormItem
        name="sourceFrom"
        label="服务方向"
        help="选择服务方向"
        on-error="必填项"
      >
        <RadioGroup
          v-model="taskModel.sourceFrom"
          :options="[{ label: '音视频', value: '0' }, { label: '算法', value: '1' }]"
          required
        />
      </FormItem>
      <FormItem
        name="sourceType"
        label="服务类型"
        help="选择服务类型"
        on-error="必填项"
      >
        <RadioGroup
          v-model="taskModel.sourceType"
          :options="[{ label: 'HTTP', value: '0' }, { label: 'RPC', value: '1' }]"
          required
        />
      </FormItem>
      <FormItem label="http请求的域名">
        <Input
          v-model="taskModel.respUrl"
          placeholder="接口URL"
        />
      </FormItem>
      <FormItem label="rpc服务注册名">
        <Input
          v-model="taskModel.consulName"
          placeholder="rpc服务注册名"
        />
      </FormItem>
      <!-- <FormItem label="rpc类全量路径名">
        <Input
          v-model="taskModel.classAllPath"
          placeholder="rpc类全量路径名"
        />
      </FormItem>

      <FormItem
      label="isFramed"
    >
      <Switch v-model="taskModel.isFramed" required/>
    </FormItem>
    <FormItem label="groupId">
        <Input
          v-model="taskModel.groupId"
          placeholder="groupId"
        />
      </FormItem>
      <FormItem label="artifactId">
        <Input
          v-model="taskModel.artifactId"
          placeholder="artifactId"
        />
      </FormItem> -->
      <FormItem label="idlUrl">
        <Input
          v-model="taskModel.idlUrl"
          placeholder="idlUrl"
        />
      </FormItem>
      <FormItem label="serviceFileName">
        <Input
          v-model="taskModel.serviceFileName"
          placeholder="serviceFileName"
        />
      </FormItem>

      <FormItem label=" ">
        <Space>
          <Button
            type="primary"
            @click="submitTask"
          >创建</Button>
          <Button
            @click="handleTClose"
          >取消</Button>
        </Space>
      </FormItem>
    </Form>
  </Modal>
</template>
<script setup lang="ts">
  import {
    reactive, ref, onMounted,
  } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Space, RadioGroup, Table2, Text, Button, toast, Pagination, Input, Tag, Form2 as Form, FormItem2 as FormItem, Modal, Link,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  import {
    Search, Clear, Add,
  } from '@xhs/delight/icons'
  import { useStore } from 'vuex'

  import '@xhs/delight-charts/dist/style.css'
  import {
    serviceInfoList, sourceRegisterCreate, GetApiTemplate, deleteRpcTask, updateRpcTask,
  } from '../../services/servicemanage'

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'
  const router = useRouter()

  const showTaskModal = ref(false)

  const formRef = ref()

  const loading = ref(false)
  const state = reactive({
    isEdit: 0,
    editID: 0,
    dataSource: [],
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    sourceName: '',
    userName: '',
    taskComment: '',
    devdataSource: [],

  })
  const taskModel = reactive({
    taskComment: '',
    deviceIdList: [],
    intervalTime: 5,
  })
  const perftaskcolumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      width: 60,
    },
    {
      title: '服务名',
      dataIndex: 'sourceName',
    },
    {
      title: '服务介绍',
      dataIndex: 'sourceComment',
    },
    {
      title: '注册中心服务名',
      dataIndex: 'consulName',
      width: 180,
    },
    // {
    //   title: '类全路径名',
    //   dataIndex: 'classAllPath',
    // },
    // {
    //   title: '是否framed',
    //   dataIndex: 'isFramed',
    // },
    // {
    //   title: 'groupId',
    //   dataIndex: 'groupId',
    // },
    // {
    //   title: 'artifactId',
    //   dataIndex: 'artifactId',
    // },
    {
      title: 'idl文件地址',
      dataIndex: 'idlUrl',
      width: 300,
    },
    {
      title: 'thriftName',
      dataIndex: 'serviceFileName',
      width: 180,
    },
    {
      title: '环境',
      dataIndex: 'sourceEnv',
    },
    {
      title: '服务来源',
      dataIndex: 'sourceFrom',
    },
    {
      title: '服务类型',
      dataIndex: 'sourceType',
    },
    {
      title: '创建人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation2',
      fixed: 'right',
      // width: 200,
    },
  ]

  //   function strtolist(str) {
  //     const arr = JSON.parse(str.replace(/'/g, '"'))
  //     return arr
  //   }

  function getServiceInfoList() {
    loading.value = true
    const queryparam = {}

    // if (state.userName) queryparam.userName = state.userName
    // if (state.taskstatus) queryparam.status = state.taskstatus
    if (state.sourceName) queryparam.sourceName = state.sourceName
    // const queryparam = {
    //   userName:"wuzhihao",
    //   status:"OK",
    //   taskComment:""
    // }
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }
    console.log('ninghuanjun-request--', payload)

    state.dataSource = []
    serviceInfoList(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger('无数据！请更改查询条件。')
        } else {
          state.dataLength = res.dataLength
          state.dataSource = res.data
          // if (res.dataLength !== 0) {
          //   state.dataSource.map(item => {
          //     item.key = item.id
          //     return item
          //   })
          // }
        }
      })
  }
  function handlePagination() {
    getServiceInfoList()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getServiceInfoList()
  }
  function handleAddJob() {
    showTaskModal.value = true
  }
  //   function testNhj() {
  //     // console.log("6666666")
  //     fetch('http://v1.yiketianqi.com/api?unescape=1&version=v91&appid=&appsecret=', {
  //   method: 'GET',
  //   mode: 'cors', // 设置CORS模式
  //   headers: {
  //     'Content-Type': 'application/json'
  //   }
  // }).then(res =>{
  //   console.log("7777777")
  //   console.log(res)
  //   console.log(res.json())
  // })

  //   }
  function handleTClose() {
    showTaskModal.value = false
    state.isEdit = 0
  }

  const submitTask = () => {
    const request = {
      sourceName: taskModel.sourceName,
      sourceComment: taskModel.sourceComment,
      sourceEnv: taskModel.sourceEnv,
      sourceFrom: taskModel.sourceFrom,
      sourceType: taskModel.sourceType,
      respUrl: taskModel.respUrl,
      consulName: taskModel.consulName,
      // classAllPath: taskModel.classAllPath,
      // isFramed: taskModel.isFramed,
      // groupId: taskModel.groupId,
      // artifactId: taskModel.artifactId,
      idlUrl: taskModel.idlUrl,
      serviceFileName: taskModel.serviceFileName,
      userName,
    }
    if (state.isEdit === 0) {
      const payload = {
        request,
      }

      sourceRegisterCreate(payload).then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          showTaskModal.value = false
          toast.success(res.message)
          getServiceInfoList()
        }
      })
    } else {
      // 编辑
      request.id = state.editID
      const payload = {
        request,
      }
      formRef.value.validate()
        .then(() => {
          updateRpcTask(payload).then(res => {
            if (res.status !== 0) {
              toast.danger(res.message)
            } else {
              showTaskModal.value = false
              toast.success(res.message)
              state.isEdit = 0
              getServiceInfoList()
            }
          })
        })
        .catch(e => {
          console.log(e, 'error')
        })
    }
  }

  function searchTaskRecord() {
    getServiceInfoList()
  }
  function jumpRpcDetail(data:any, res:any) {
    console.log('a------', data, res)
    // 跳转到执行记录页面记住ID
    localStorage.setItem('RpcDetail', JSON.stringify(data))
    localStorage.setItem('RpcRes', JSON.stringify(res))
    router.push({
      name: 'RpcDetail',
      params: {
        id: data.id,
      },
    })
  }

  function getAllApi(data:any) {
    const payload = {
      request: {
        sourceId: data.id,
      },
    }
    GetApiTemplate(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          jumpRpcDetail(data, res)
          // toast.success(res.message)
        }
      })
  }

  function deleteRpc(data:any) {
    // console.log("-----")api/vqa/sourceRegister/delete
    //
    const payload = {
      request: {
        id: data.id,
      },
    }
    deleteRpcTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          getServiceInfoList()
        }
      })
  }
  function updateSever(data:any) {
    state.isEdit = 1
    showTaskModal.value = true
    state.editID = data.id
    taskModel.consulName = data.consulName.toString()
    taskModel.sourceName = data.sourceName
    taskModel.sourceComment = data.sourceComment
    taskModel.sourceEnv = String(data.sourceEnv)
    taskModel.sourceFrom = String(data.sourceFrom)
    taskModel.sourceType = String(data.sourceType)
    taskModel.respUrl = data.respUrl
    taskModel.idlUrl = data.idlUrl
    taskModel.serviceFileName = data.serviceFileName
  }

  //   const showDevice = (row:any) => {
  //     console.log(row)
  //   }

  onMounted(() => {
    getServiceInfoList()
  })
</script>

<style lang="stylus" scoped>

</style>
