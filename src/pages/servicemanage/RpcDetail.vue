<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >服务名:</Text>
      <Tag>
        {{ state.RpcDetail.sourceName }}
      </Tag>
      <Text
        bold
        style="margin-left:15px"
      >服务注册名:</Text>
      <Tag>
        {{ state.RpcDetail.consulName }}
      </Tag>
      <Text
        bold
        style="margin-left:15px"
      >环境:</Text>
      <Tag>
        {{ state.RpcDetail.sourceEnv }}
      </Tag>
      <Text
        bold
        style="margin-left:15px"
      >serviceFileName:</Text>
      <Tag>
        {{ state.RpcDetail.serviceFileName }}
      </Tag>
    </NewBsBox>
  </Space>
  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <div style="display: flex;">
      <div style="width: 10%;">
        <Tree
          :tree-data="state.treeData"
          :default-expand-all="true"
          @select="handleSelect"
        />
      </div>
      <div style="width: 90%;"><JsonEditorVue
        v-model="state.jsonData"
        :show-btns="false"
        style="margin-right: 80px;height:500px;"
        class="editor"
        @blur="jsonValidate"
      /></div>
    </div>
  </NewBsBox>
  <NewBsBox :bs="{ display: 'flex',boxShadow: 'none', ml: 80,mt: 15, flex: 1,alignItems: 'center' }">
    <Button
      :style="{ backgroundColor: '#28BC77', color: 'white',marginLeft: '60px', }"
      @click="runJob"
    >运行服务</Button>
  </NewBsBox>

</template>
<script setup lang="ts">
  import {
    reactive, onMounted,
  } from 'vue'
  // import { useRouter } from 'vue-router'
  import {
    Space, Button, toast, Tag, Tree, Text,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  // import {
  //    Add, MessageEmoji,
  // } from '@xhs/delight/icons'
  // import { useStore } from 'vuex'

  import '@xhs/delight-charts/dist/style.css'
  import JsonEditorVue from 'json-editor-vue3'
  import {
    serviceRpcRun,
  } from '../../services/servicemanage'

  // const store = useStore()
  // const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'
  // const router = useRouter()

  // const loading = ref(false)
  const state = reactive({
    RpcDetail: {},
    RpcRes: {},
    treeData: [],
    jsonData: {},

  })
  const handleSelect = keys => {
    console.log('tree selected keys changed: ', keys)
  }
  function runJob() {
    const request = {
      jiekouRPC: state.jsonData,
      sourceName: state.RpcDetail.sourceName,
      sourceEnv: state.RpcDetail.sourceEnv,
      consulName: state.RpcDetail.consulName,
      serviceFileName: state.RpcDetail.serviceFileName,
    }
    const payload = {
      request,
    }
    serviceRpcRun(payload).then(res => {
      if (res.status !== 0) {
        toast.danger(res.message)
      } else {
        toast.success(res.message)
      }
    })
  }

  onMounted(() => {
    if (localStorage.getItem('RpcDetail')) {
      state.RpcDetail = JSON.parse(localStorage.getItem('RpcDetail'))
    }
    if (localStorage.getItem('RpcRes')) {
      state.RpcRes = JSON.parse(localStorage.getItem('RpcRes'))

      const rpcKeys = Object.keys(state.RpcRes.jiekouRPC)
      const newData = rpcKeys.map(key => ({
        title: key,
        key,
        children: [],
      }))

      state.treeData = [
        ...newData,
      ]

      console.log(state.treeData)
      state.jsonData = state.RpcRes.jiekouRPC
    }
  })
</script>

<style lang="stylus" scoped>

</style>
