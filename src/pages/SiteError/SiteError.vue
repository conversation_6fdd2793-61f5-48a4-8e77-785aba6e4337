<template>
  <Result
    status="fail"
    title="加载失败"
    sub-title="加载页面失败，请稍后重试。"
    style="min-height: 100%"
  >
    <Space>
      <Button
        type="primary"
        @click="backHome"
      >
        返回首页
      </Button>
      <Button @click="logout">登出</Button>
    </Space>
  </Result>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router'
  import { Result, Space, Button } from '@xhs/delight'
  import { logout } from '../../services/user'

  const router = useRouter()

  function backHome() {
    router.push('/')
  }
</script>
