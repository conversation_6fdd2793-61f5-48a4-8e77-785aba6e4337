<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
      <Text
        bold
        style="margin-left: -15px"
      >实验名称:</Text>
      <Input
        v-model="state.sourceName"
        :style="{ width: '180px' }"
      />
      <!-- <Text
          bold
          style="margin-left:10px"
        >任务名称:</Text>
        <Input
          v-model="state.taskComment"
          :style="{ width: '120px' ,}"
        />
        <Text
          bold
          style="margin-left:10px"
        >执行人:</Text>
        <Input
          v-model="state.userName"
          :style="{ width: '120px' ,}"
        /> -->
      <Button
        style="float: right; margin: 10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchTaskRecord()"
      >查询</Button>
      <Button
        style="float: right; margin: 10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', ml: 30, flex: 1, alignItems: 'center' }">
      <Button
        :style="{ backgroundColor: '#28BC77', color: 'white', marginLeft: '-50px' }"
        :icon="{ icon: Add }"
        @click="handleAddJob"
      >添加实验</Button>
    </NewBsBox>
  </Space>
  <NewBsBox :bs="{ ml: 20, mr: 20 }">
    <Table2
      :columns="expercolumns"
      :data-source="state.dataSource"
      :table-layout="auto"
      :loading="loading"
      size="small"
    >
      <template #type="{ rowData }">
        <Tag
          v-if="rowData.type === 1"
          color="green"
          size="small"
        >bool类型</Tag>
        <Tag
          v-if="rowData.type === 2"
          size="small"
        >数字类型</Tag>
      </template>
      <template #status="{ rowData }">
        <Tag
          v-if="rowData.status === true"
          theme="solid"
          color="green"
          size="small"
        >生效</Tag>
        <Tag
          v-if="rowData.status === false"
          theme="solid"
          size="small"
        >下线</Tag>
      </template>
      <template #createdAt="{ rowData }">
        <Text>{{ formatDate(rowData.createdAt) }}</Text>
      </template>
      <template #updatedAt="{ rowData }">
        <Text>{{ formatDate(rowData.updatedAt) }}</Text>
      </template>
      <template #operation2="{ rowData }">
        <!-- <Link :style="{ margin: '5px' }" @click="delExperiment(rowData)">删除</Link>
        <Link :style="{ margin: '5px' }" @click="updateSever(rowData)">更新</Link> -->
        <Button
          :style="{ margin: '5px' }"
          type="secondary"
          size="small"
          @click="updateExperiment(rowData)"
        >更新</Button>

        <Popconfirm
          :closeable="false"
          @confirm="delExperiment(rowData)"
          @cancel="handleClose"
        >
          <template #description>
            <Space>
              <Icon color="info"><component :is="Presets.Info" /></Icon>
              <Text>确定要删除吗？</Text>
            </Space>
          </template>
          <Button
            :style="{ margin: '5px' }"
            type="danger"
            size="small"
          >删除</Button>
        </Popconfirm>
      </template>

    </Table2>
    <NewBsBox :bs="{ margin: 20 }">
      <Pagination
        v-model="state.pageNum"
        :total="state.dataLength"
        align="end"
        @update:modelValue="handlePagination"
        @update:pageSize="handlepageSize"
      />
    </NewBsBox>
  </NewBsBox>
  <Modal
    v-model:visible="showTaskModal"
    :title="state.isEdit == 1 ? '编辑实验' : '创建实验'"
    :with-footer="false"
    :style="{ width: 'auto' }"
    @cancel="handleTClose"
  >
    <Form2
      ref="formRef"
      :label-position="labelPosition"
      :rules="rules"
      :model="taskModel"
      label-width="150px"
    >
      <FormItem2
        label="实验版本"
        name="version"
        :hide-required-mark="true"
      >
        <Input
          v-model="taskModel.version"
          placeholder="实验生效版本"
        />
      </FormItem2>
      <FormItem2
        label="实验名称"
        name="name"
        :hide-required-mark="true"
      >
        <Input
          v-model="taskModel.name"
          placeholder="实验名称"
        />
      </FormItem2>
      <FormItem2
        label="实验状态"
        name="status"
        :hide-required-mark="true"
      >
        <Switch
          v-model="taskModel.status"
          autofocus
        />
      </FormItem2>
      <FormItem2
        label="实验类型"
        name="type"
        help="实验类型"
        on-error="必填项"
        :hide-required-mark="true"
      >
        <RadioGroup
          v-model="taskModel.type"
          :options="[
            { label: 'bool类型', value: '1' },
            { label: '数字类型', value: '2' },
          ]"
          required
        />
      </FormItem2>

      <FormItem2
        label="code"
        name="code"
        :hide-required-mark="true"
      >
        <Input
          v-model="taskModel.code"
          placeholder=""
        />
      </FormItem2>
      <FormItem2 label=" ">
        <Space>
          <Button
            type="primary"
            @click="submitTask"
          >创建</Button>
          <Button @click="handleTClose">取消</Button>
        </Space>
      </FormItem2>
    </Form2>
  </Modal>
</template>
<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  // import { useRouter } from 'vue-router'
  import {
    Space,
    RadioGroup,
    Table2,
    Text,
    Button,
    toast,
    Pagination,
    Input,
    Tag,
    Form2,
    FormItem2,
    Modal,
    Switch, Popconfirm, Icon,
  } from '@xhs/delight'

  import { NewBsBox } from '@xhs/yam-beer'
  import {
    Search, Clear, Add, Presets,
  } from '@xhs/delight/icons'
  import { useStore } from 'vuex'

  import '@xhs/delight-charts/dist/style.css'

  const store = useStore()
  const userName = store.state.Auth.userInfo.email
    ? store.state.Auth.userInfo.email.split('@')[0]
    : '路人'

  const showTaskModal = ref(false)

  const formRef = ref()

  const loading = ref(false)
  const state = reactive({
    isEdit: 0,
    editID: 0,
    dataSource: [],
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    sourceName: '',
    userName: '',
    taskComment: '',
    devdataSource: [],
  })
  const taskModel = reactive({
    status: false,
    type: '1',
    code: '',
  })

  function validateCode(rule, value, callback) {
    if (taskModel.type === '2' && !value) {
      return callback(new Error('实验为数字类型，code必填！'))
    }
    return callback()
  }
  const rules = {
    name: [
      { required: true, message: '请输入实验名称！', trigger: 'blur' },
    ],
    type: { required: true, message: '请选择实验类型！', trigger: 'change' },
    version: { required: true, message: '请输入实验生效版本！', trigger: 'blur' },
    code: [
      { required: taskModel.type === '2', message: '实验为数字类型，code必填！', trigger: 'blur' },
      {
        validator: validateCode,
        trigger: 'blur',
      },
    ],
  }

  const expercolumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60,
    },
    {
      title: 'APP版本',
      dataIndex: 'version',
    },
    {
      title: '实验名称',
      dataIndex: 'name',
    },
    {
      title: '实验类型',
      dataIndex: 'type',
    // width: 180,
    },
    {
      title: '实验开关',
      dataIndex: 'status',
    },

    {
      title: '创建时间',
      dataIndex: 'createdAt',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
    },
    {
      title: '创建人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation2',
      fixed: 'right',
      width: 230,
    },
  ]

  function getExperiments() {
    loading.value = true
    fetch('http://v1.yiketianqi.com/api?unescape=1&version=v91&appid=&appsecret=', {
      method: 'GET',
      mode: 'cors', // 设置CORS模式
      headers: {
        'Content-Type': 'application/json',
      },
    }).then(res => {
      loading.value = false
      console.log('7777777')
      console.log(res)
      // 检查响应是否成功
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`)
      }

      // 将 res.json() 的 Promise 返回
      return res.json()
    })
      .then(data => {
        // console.log(data) data为整体返回，跨域待测试
        // data = {
        //   code: 200,
        //   msg: 'success',
        //   success: true,
        //   data: [{
        //     id: 3, version: '821', name: 'demo_experiment', status: true, type: 1, code: 0, createdAt: '2024-08-19T12:45:35.000+0000', updatedAt: '2024-08-19T12:45:35.000+0000',
        //   }, {
        //     id: 4, version: '821', name: 'e', status: true, type: 1, code: 0, createdAt: '2024-08-19T12:45:35.000+0000', updatedAt: '2024-08-19T12:45:35.000+0000',
        //   }],
        // }
        if (data.success !== true) {
          toast.danger('无数据！请更改查询条件。')
        } else {
          state.dataLength = data.data.length
          state.dataSource = data.data
        }
      })
      .catch(error => {
        console.error('There was an error!', error)
      })
  // .then(response => response.json())
  // .catch(error => console.error('Error:', error));

  // const queryparam = {};
  // if (state.sourceName) queryparam.sourceName = state.sourceName;
  // const payload = {
  //   request: {
  //     pageNum: state.pageNum,
  //     pageSize: state.pageSize,
  //     queryparam,
  //   },
  // };
  // console.log("ninghuanjun-request--", payload);

  // state.dataSource = [];
  // serviceInfoList(payload).then((res) => {
  //   loading.value = false;
  //   if (res.status !== 0) {
  //     toast.danger("无数据！请更改查询条件。");
  //   } else {
  //     state.dataLength = res.dataLength;
  //     state.dataSource = res.data;
  //   }
  // });
  }
  function handlePagination() {
    getExperiments()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getExperiments()
  }
  function handleAddJob() {
    showTaskModal.value = true
  }
  function getCurrentISODate() {
    const now = new Date()

    // 获取各个时间部分
    const year = now.getUTCFullYear()
    const month = String(now.getUTCMonth() + 1).padStart(2, '0') // 月份从0开始，需要加1
    const day = String(now.getUTCDate()).padStart(2, '0')
    const hours = String(now.getUTCHours()).padStart(2, '0')
    const minutes = String(now.getUTCMinutes()).padStart(2, '0')
    const seconds = String(now.getUTCSeconds()).padStart(2, '0')
    const milliseconds = String(now.getUTCMilliseconds()).padStart(3, '0')

    // 生成 ISO 8601 字符串，并手动追加时区信息
    const isoString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+0000`

    return isoString
  }
  function formatDate(isoString) {
    // 解析日期字符串
    const date = new Date(isoString)

    // 提取各个部分
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份是0-11，所以要加1
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    // 拼接“年月日时分秒”格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  function handleTClose() {
    showTaskModal.value = false
    state.isEdit = 0
    formRef.value.resetFields()
  }
  function handleClose() {
    toast.danger('点击了取消')
  }

  const submitTask = () => {
    const request = {
      version: taskModel.version,
      name: taskModel.name,
      status: taskModel.status,
      type: taskModel.type,
      code: taskModel.code,
      createdAt: getCurrentISODate(),
      updatedAt: getCurrentISODate(),
      userName,
    }
    if (state.isEdit === 0) {
      const payload = [request]
      formRef.value
        .validate()
        .then(() => {
          // console.log(payload)
          // 添加接口
          fetch('https://example.com/api/endpoint', {
            method: 'POST',
            mode: 'cors',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          })
            .then(response => response.json())
            .then(data => {
              console.log('Success:', data)
              getExperiments()
            })
            .catch(error => {
              console.error('Error:', error)
            })
        })
    } else {
      // 编辑
      request.id = state.editID
      const payload = [request]
      formRef.value
        .validate()
        .then(() => {
          fetch('https://example.com/api/endpoint', {
            method: 'POST',
            mode: 'cors',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          })
            .then(response => response.json())
            .then(data => {
              console.log('Success:', data)
              getExperiments()
            })
            .catch(error => {
              console.error('Error:', error)
            })
        })
        .catch(e => {
          console.log(e, 'error')
        })
    }
  }

  function searchTaskRecord() {
    getExperiments()
  }

  // http://v1.yiketianqi.com/api?unescape=1&version=v91&appid=&appsecret=
  function delExperiment(rowData: any) {
    console.log(rowData)
    fetch(`http://xxxxx/api/qa/community/v1/delete/experiments?verison=${rowData.verison}&name=${rowData.name}`, {
      method: 'GET',
      mode: 'cors', // 设置CORS模式
      headers: {
        'Content-Type': 'application/json',
      },
    }).then(res => {
      loading.value = false
      // 检查响应是否成功
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`)
      }

      // 将 res.json() 的 Promise 返回
      return res.json()
    })
      .then(data => {
        console.log('88888')
        console.log(data)
        if (data.success !== true) {
          toast.danger('删除失败！')
        } else {
          toast.success('删除成功！')
          getExperiments()
        }
      })
      .catch(error => {
        console.error('There was an error!', error)
      })
  }
  function updateExperiment(data: any) {
    console.log(data)
    state.isEdit = 1
    showTaskModal.value = true
    state.editID = data.id
    taskModel.name = data.name
    taskModel.version = data.version
    taskModel.status = data.status
    taskModel.type = String(data.type)
    taskModel.code = data.code
  }

  onMounted(() => {
    getExperiments()
  })
</script>

<style lang="stylus" scoped></style>
