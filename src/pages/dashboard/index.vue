<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', width: '100%' }">
      <Button
        size="large"
        style="margin-right: 10px; background-color:#28BC77; color:white;"
        @click="handleUserManual"
      >用户手册</Button>
    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'space-between', width: '100%' }">
      <Card>
        <GridItem>
          <Statistic
            title="用例总数"
            :value="state.taskNum"
          >
            <template #title>
              <div :style="{ display: 'flex', alignItems: 'center' }">
                <Text color="text-description">用例总数</Text>
                <Tooltip content="平台创建用例总数">
                  <Icon
                    color="text-description"
                    :icon="Help"
                    size="small"
                    :style="{ marginLeft: '4px' }"
                  />
                </Tooltip>
              </div>
            </template>
          </Statistic>
        </GridItem>
      </Card>
      <Card>
        <GridItem>
          <Statistic
            title="运行次数"
            :value="state.taskRunNum"
          >
            <template #title>
              <div :style="{ display: 'flex', alignItems: 'center' }">
                <Text color="text-description">运行次数</Text>
                <Tooltip content="任务运行次数">
                  <Icon
                    color="text-description"
                    :icon="Help"
                    size="small"
                    :style="{ marginLeft: '4px' }"
                  />
                </Tooltip>
              </div>
            </template>
          </Statistic>
        </GridItem>
      </Card>
      <Card>
        <GridItem>
          <Statistic
            title="使用人数"
            :value="state.userNum"
          >
            <template #title>
              <div :style="{ display: 'flex', alignItems: 'center' }">
                <Text color="text-description">使用人数</Text>
                <Tooltip content="平台使用人数">
                  <Icon
                    color="text-description"
                    :icon="Help"
                    size="small"
                    :style="{ marginLeft: '4px' }"
                  />
                </Tooltip>
              </div>
            </template>
          </Statistic>
        </GridItem>
      </Card>
    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', marginTop: '7px', width: '100%' }">
      <DateRangePicker
        v-model="state.rangeValue"
        @update:modelValue="handleChangeDate"
      />
    </NewBsBox>
    <NewBsBox :bs="{ marginTop: '25px', width: '100%' }">
      <div
        id="statChartVa"
        :style="{ margin: 0, height: '400px', width: '90%' }"
      />
    </NewBsBox>

  </Space>
</template>

<script setup lang="tsx">
  import { reactive, onMounted } from 'vue'
  // import { useRouter } from 'vue-router'
  import {
    Space, Card, Text, GridItem, Statistic, Icon, Tooltip, DateRangePicker, toast, Button,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  import {
    Help,
  } from '@xhs/delight/icons'
  import * as echarts from 'echarts'

  // import { useStore } from 'vuex'
  import { getVaCount, getVaStatinfo } from '../../services/dashboard'
  import {
    getCurrentDate, beforeDays,
  } from '../../utils/common'

  // const store = useStore()
  // const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'
  // const router = useRouter()
  // const route = useRoute()

  const taskdata = reactive({
    weekList: [] as any[],
    taskRunNumList: [] as any[],
    taskSuccRateList: [] as any[],
  })

  const state = reactive({
    taskNum: '0',
    taskRunNum: '0',
    userNum: '0',
    rangeValue: { start: '', end: '' },
    statoption: {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
      },
      toolbox: {
        feature: {
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ['line', 'bar'] },
          restore: { show: true },
          saveAsImage: { show: true },
        },
      },
      legend: {
        data: ['运行次数', '成功率'],
      },
      xAxis: [
        {
          type: 'category',
          data: taskdata.weekList, // 横轴为四周
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '个',
          min: 0,
          max: 200,
          interval: 20,
          axisLabel: {
            formatter: '{value} ',
          },
        },
        {
          type: 'value',
          name: '百分比',
          min: 0,
          max: 100,
          interval: 20,
          axisLabel: {
            formatter: '{value} %',
          },
        },
      ],
      series: [
        {
          name: '运行次数',
          barMaxWidth: 30,
          type: 'bar',
          tooltip: {
            valueFormatter(value: any) {
              return `${value} 个`
            },
          },
          data: taskdata.taskRunNumList,
          label: {
            show: true, // 开启显示
            position: 'inside', // 在上方显示
            color: 'black',
            fontSize: 12,
          },
        },
        {
          name: '成功率',
          type: 'line',
          barMaxWidth: 30,
          yAxisIndex: 1,
          tooltip: {
            valueFormatter(value: any) {
              return `${value} %`
            },
          },
          data: taskdata.taskSuccRateList,
          label: {
            show: true, // 开启显示
            position: 'top', // 在上方显示
            color: 'black',
            fontSize: 12,
            formatter(params: any) {
              return `${params.value}%`
            },
          },
        },

      ],
    },
  })
  function handleStatinfo() {
    const payload = {
      startTime: state.rangeValue.start,
      endTime: state.rangeValue.end,
    }
    taskdata.weekList = []
    taskdata.taskRunNumList = []
    taskdata.taskSuccRateList = []
    getVaStatinfo(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          // 接口返回后赋值
          res.data.forEach((item: any) => {
            taskdata.weekList.push(item.weekName)
            taskdata.taskRunNumList.push(item.taskRunNum)
            taskdata.taskSuccRateList.push((Number(item.succRate) * 100).toFixed(2)) // Number(point*100).toFixed(1)
          })

          state.statoption.xAxis[0].data = taskdata.weekList
          state.statoption.series[0].data = taskdata.taskRunNumList
          state.statoption.series[1].data = taskdata.taskSuccRateList

          if (document.getElementById('statChartVa')!.hasAttribute('_echarts_instance_')) {
            document.getElementById('statChartVa')!.removeAttribute('_echarts_instance_')
          }
          const mystatChart = echarts.init(document.getElementById('statChartVa')!)
          mystatChart.clear()
          mystatChart.setOption(state.statoption, true)
          window.onresize = function () {
            mystatChart.resize()
          }
        }
      })
  }
  function handlegetVaCount() {
    getVaCount()
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.taskNum = res.data.taskNum
          state.taskRunNum = res.data.taskRunNum
          state.userNum = res.data.userNum
        }
      })
  }

  function handleChangeDate() {
    if (typeof state.rangeValue.start !== 'undefined' && typeof state.rangeValue.end !== 'undefined') {
      handleStatinfo()
    }
  }

  function handleUserManual() {
    window.open('https://docs.xiaohongshu.com/doc/6271aa7b329b166418eab10ee1148312')
  }

  onMounted(() => {
    state.rangeValue.end = getCurrentDate()
    state.rangeValue.start = beforeDays(29)
    handlegetVaCount()
    handleStatinfo()
  })

</script>
