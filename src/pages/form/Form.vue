<template>
  <Spinner
    :spinning="formRef?.status === 'waiting'"
    tip="校验中"
    size="large"
  >
    <Space
      direction="vertical"
      block
      style="padding: calc(var(--size-space-step-default) * 10)"
    >
      <Form
        ref="formRef"
        v-model="form"
        :config="config"
        @submit="handleSubmit"
      >
        <FormItem
          name="name"
          label="姓名"
          help="填写用户姓名"
          description="填写用户姓名"
        >
          <Input
            :max-length="6"
            max-length-error="最多输入 6 个字符"
            placeholder="用户姓名"
            clearable
            required
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="sex"
          label="性别"
          help="填写用户性别"
          description="填写用户性别"
        >
          <RadioGroup
            :options="[
              { label: '男', value: 'male' },
              { label: '女', value: 'female' }
            ]"
            required
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="age"
          label="年龄"
          help="填写用户年龄"
          description="填写用户年龄"
        >
          <InputNumber
            placeholder="用户年龄"
            clearable
            required
            required-error="必填项"
          />
        </FormItem>
      </Form>
      <Text type="description">{{ form }}</Text>
    </Space>
  </Spinner>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useStore } from 'vuex'
  import {
    Spinner, Space, Input, InputNumber, TextArea, Select, RadioGroup, CheckboxGroup, Switch, Form, FormItem, Text,
  } from '@xhs/delight'

  const formRef = ref()

  const store = useStore()
  const { name } = store.state.user.userInfo
  const form = ref(
    {
      name,
      type: ['normal', 'member'],
      favor: ['basketball', 'football', 'tennis'],
    },
  )

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function syncValidate({ modelValue, fullValue }) {
    return modelValue?.length <= 2 || '同步报错，最多选择 2 个爱好'
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function asyncValidate({ modelValue, fullValue }) {
    return new Promise(
      resolve => setTimeout(
        () => resolve(
          !(modelValue?.includes('normal') && modelValue?.includes('member')) || '异步报错，不能同时为普通用户和会员',
        ),
        1500,
      ),
    )
  }

  const config = ref(
    [
      {
        name: 'favor',
        label: '爱好',
        help: '填写用户爱好',
        description: '填写用户爱好',
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        onError: ({ modelValue, fullValue }) => '当常规报错手段不满足时可以通过 onError 来获取到当前选项的完整信息并组织内容',
        component: {
          is: Select,
          options: [
            { name: '篮球', value: 'basketball' },
            { name: '足球', value: 'football' },
            { name: '网球', value: 'tennis' },
          ],
          placeholder: '用户爱好',
          validate: syncValidate,
          multiple: true,
          clearable: true,
          required: true,
          requiredError: '必填项',
        },
      },
      {
        name: 'type',
        label: '用户类型',
        help: '填写用户类型',
        description: '填写用户类型',
        component: {
          is: CheckboxGroup,
          options: [
            { label: '普通用户', value: 'normal' },
            { label: '会员', value: 'member' },
            { label: '博主', value: 'blogger' },
          ],
          validate: asyncValidate,
          required: true,
          requiredError: '必填项',
        },
      },
      {
        name: 'new',
        label: '新用户',
        help: '是否为新用户',
        description: '是否为新用户',
        component: {
          is: Switch,
        },
      },
      {
        name: 'description',
        label: '简介',
        help: '填写用户简介',
        description: '填写用户简介',
        component: {
          is: TextArea,
          maxLength: 20,
          maxLengthError: '最多输入 20 个字符',
          placeholder: '用户简介',
          clearable: true,
        },
      },
    ],
  )

  function handleSubmit(v) {
    console.log(v)
  }
</script>
