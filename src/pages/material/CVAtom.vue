<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >场景分类:</Text>
      <Select
        v-model="state.sceneType"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.sceneTypeList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >二级场景分类:</Text>
      <Select
        v-model="state.subsceneType"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.subsceneTypeList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >景别:</Text>
      <Select
        v-model="state.shotsType"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.shotsTypeList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >姿态:</Text>
      <Select
        v-model="state.postureType"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.postureTypeList"
        multiple
        filterable
        clearable
        multi-line
      />

    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >人数:</Text>
      <Select
        v-model="state.peopleNum"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.peopleNumList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >光线:</Text>
      <Select
        v-model="state.lightType"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.lightTypeList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >case属性:</Text>
      <Select
        v-model="state.caseType"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.caseTypeList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >适用算法:</Text>
      <Select
        v-model="state.algorithm"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.algorithmList"
        multiple
        filterable
        clearable
        multi-line
      />
      <Text
        bold
        style="margin-left:10px"
      >角色:</Text>
      <Select
        v-model="state.role"
        :style="{ width: '120px' }"
        type="secondary"
        :options="state.roleList"
        multiple
        filterable
        clearable
        multi-line
      />
    </NewBsBox>

    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >图像宽高比:</Text>
      <Tooltip content="示例:x-y(英文-)">
        <Icon :icon="Help" />
      </Tooltip>
      <Input
        v-model="state.imgDAR"
        :style="{ width: '120px', }"
      />
      <Text
        bold
        style="margin-left:10px"
      >宽度:</Text>
      <Tooltip content="示例:x-y(英文-)">
        <Icon :icon="Help" />
      </Tooltip>
      <Input
        v-model="state.imgWidth"
        :style="{ width: '120px' }"
      />
      <Text
        bold
        style="margin-left:10px"
      >高度:</Text>
      <Tooltip content="示例:x-y(英文-)">
        <Icon :icon="Help" />
      </Tooltip>
      <Input
        v-model="state.imgHeight"
        :style="{ width: '120px' }"
      />

      <Text
        bold
        style="margin-left:10px"
      >图像大小:</Text>
      <Tooltip content="示例:x-y(英文-)">
        <Icon :icon="Help" />
      </Tooltip>
      <Input
        v-model="state.imgSize"
        :style="{ width: '120px' }"
      />
      <Text
        bold
        style="margin-left:10px"
      >图像分辨率:</Text>
      <Tooltip content="示例:x-y(英文-)">
        <Icon :icon="Help" />
      </Tooltip>
      <Input
        v-model="state.resolutionRatio"
        :style="{ width: '120px' }"
      />

    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex',width:'100%',flexDirection:'row-reverse'}">
      <NewBsBox :bs="{ boxShadow: 'none', mr: 20, flex: 1,}">
        <Button
          style="float:right;margin:10px"
          type="secondary"
          :icon="{ icon: Clear }"
          @click="clearParam"
        >重置</Button>
        <Button
          style="float:right;margin:10px"
          type="primary"
          :icon="{ icon: Search }"
          @click="searchCvset()"
        >查询</Button>

      </NewBsBox>
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ display: 'flex',width:'100%'}">
    <NewBsBox :bs="{ display: 'flex',boxShadow: 'none', ml: 30, flex: 1,alignItems: 'center' }">
      <!-- <Text style="margin-right:10px">查询结果共: {{ state.dataLength }}条</Text> -->
      <Checkbox
        v-model:checked="state.csvchecked"
        style="margin-right:20px"
        value="全部查询结果"
        label="全部查询结果"
        description="勾选后，通过csv创建任务素材集"
        :onUpdate:checked="changeChecked"
      />
      <!-- <Text>当前选中状态：{{ state.csvchecked }}</Text> -->
      <Button
        :style="{ backgroundColor: '#28BC77', color: 'white' }"
        :icon="{ icon: Add }"
        @click="handleAddJobset"
      >任务素材</Button>
      <!-- <Button
        :style="{ backgroundColor: '#28BC77', color: 'blue' }"
        @click="test"
      >测试</Button> -->
    </NewBsBox>
  </NewBsBox>

  <NewBsBox :bs="{ ml: 20, mr: 20, mt: 20,}">

    <Table
      v-model:selected="state.selectedList"
      :columns="cvatomColumns"
      :data-source="state.cvatomdataSource"
      :row-selection="rowSelection"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #filekey="{ data }">
        <Tooltip>
          <Tag
            color="green"
            size="small"
            @click="copyUrl(data)"
          >filekey</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #fileurl="{ data }">
        <Tooltip>
          <Tag
            color="blue"
            size="small"
            @click="copyUrl(data)"
          >地址</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>

      <template #operation="{ rowData }">
        <Button
          size="small"
          :style="{ backgroundColor: '#3366FF', color: 'white' }"
          @click="jsonDetail(rowData)"
        >查看</Button>
      </template>

    </Table>
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <Modal
    v-model:visible="showNewJobModal"
    title="任务素材集"
    :with-footer="false"
    :style="{ width: 'auto' }"
  >
    <Form
      v-model="createForm"
      :style="{ marginTop: '20px' }"
      size="default"
      @submit="handleSubmit"
    >
      <FormItem
        name="setName"
        label="任务素材集名称"
      >
        <Input required />
      </FormItem>
      <FormItem
        name="setType"
        label="素材集类型"
      >
        <Select
          :options="setTypeList"
          required
        />
      </FormItem>
      <FormItem
        name="setContent"
        label="任务素材集详情"
      >
        <TextArea />
      </FormItem>
      <FormItem
        v-if="state.csvchecked === true"
        name="setSource"
        label="任务来源"
      >
        <!-- <Text required>{{state.csvURL}}</Text> -->
        <TextArea
          disabled
          :model-value="state.csvURL"
          required
        />
      </FormItem>
    </Form>
  </Modal>

  <Modal
    v-model:visible="showjsonDataModal"
    :mask-closeable="maskCloseable"
    title="数据"
    :with-footer="false"
    :style="{ width: '800px',height:'800px' }"
  >
    <json-viewer
      :value="state.jsonData"
      copyable
    />
  </Modal>

  <Modal
    v-model:visible="infoVisible"
    type="danger"
    title="错误"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      请勾选原子素材集后，再创建！
    </Text>
  </Modal>

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import { useStore } from 'vuex'
  import {
    Space, Table, Text, Button, toast, Pagination, Input, Select, Tag, Icon, Tooltip, Checkbox, Modal, Form, FormItem, TextArea,
  } from '@xhs/delight'
  import {
    Search, Add, Help, Clear,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'
  import { useRouter } from 'vue-router'
  import JsonViewer from 'vue-json-viewer'
  import { getCvsetinit, getCvsetData, cvsetCreateTask } from '../../services/material'
  import {
    setTypeList,
  } from '../../utils/common'

  const router = useRouter()

  const cvatomColumns = [
    {
      title: '场景分类',
      dataIndex: 'sceneType',
    },
    {
      title: '二级场景分类',
      dataIndex: 'subsceneType',
    },
    {
      title: '景别',
      dataIndex: 'shotsType',
    },
    {
      title: '姿态',
      dataIndex: 'postureType',
    },
    {
      title: '人数',
      dataIndex: 'peopleNum',
    },
    {
      title: '光线',
      dataIndex: 'lightType',
    },
    {
      title: 'filekey',
      dataIndex: 'filekey',
    },
    {
      title: 'fileurl',
      dataIndex: 'fileurl',
    },
    {
      title: '宽高比',
      dataIndex: 'imgDAR',
    },
    {
      title: '图像宽度',
      dataIndex: 'imgWidth',
    },
    {
      title: '图像高度',
      dataIndex: 'imgHeight',
    },
    {
      title: '图像大小',
      dataIndex: 'imgSize',
    },
    {
      title: 'case属性',
      dataIndex: 'caseType',
    },
    {
      title: '适用算法',
      dataIndex: 'algorithm',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]
  const loading = ref(false)
  const showNewJobModal = ref(false)
  const showjsonDataModal = ref(false)
  const maskCloseable = ref(false)
  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

  const createForm = ref({ setName: '', setContent: '', setSource: '' })
  const infoVisible = ref(false)

  const state = reactive({
    id: '',
    taskName: '',
    sceneType: '',
    sceneTypeList: [],
    subsceneType: '',
    subsceneTypeList: [],
    shotsType: '',
    shotsTypeList: [],
    postureType: '',
    postureTypeList: [],
    peopleNum: '',
    peopleNumList: [],
    lightType: '',
    lightTypeList: [],
    caseType: '',
    caseTypeList: [],
    algorithm: '',
    algorithmList: [],
    role: '',
    roleList: [],

    imgDAR: '',
    imgWidth: '',
    imgHeight: '',
    imgSize: '',
    resolutionRatio: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    cvatomdataSource: [],
    filtersetName: '',

    csvURL: '',
    csvchecked: false,
    selectedList: [],
    jsonData: {},

  })

  const changeChecked = () => {
    if (state.csvchecked === true) {
      state.selectedList = []
    }
  }

  function handleSelectedChange(v) {
    if (v) {
      state.csvchecked = false
    }
  }

  function notempty(param) {
    const arr = []
    param.forEach(val => {
      // 过滤规则为，不为空串、不为null、不为undefined，也可自行修改
      if (val !== '' && val !== undefined) {
        arr.push(val)
      }
    })
    return arr
  }

  function cvsetinit() {
    state.sceneTypeList = []
    state.subsceneTypeList = []
    state.shotsTypeList = []
    state.postureTypeList = []
    state.lightTypeList = []
    state.peopleNumList = []
    state.caseTypeList = []
    state.algorithmList = []
    state.roleList = []

    getCvsetinit()
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.sceneTypeList = res.data.sceneType
          state.subsceneTypeList = res.data.subsceneType
          state.shotsTypeList = res.data.shotsType
          state.postureTypeList = res.data.postureType
          state.lightTypeList = res.data.lightType
          state.peopleNumList = res.data.peopleNum
          state.caseTypeList = res.data.caseType
          state.algorithmList = res.data.algorithm
          state.roleList = res.data.role
        }
      })
  }

  function searchCvset() {
    loading.value = true
    const queryparam = {}

    if (state.sceneType.length !== 0) {
      const tmpArr = notempty(state.sceneType)
      state.sceneType = tmpArr
      queryparam.sceneType = state.sceneType
    }

    if (state.subsceneType.length !== 0) {
      const tmpArr = notempty(state.subsceneType)
      state.subsceneType = tmpArr
      queryparam.subsceneType = state.subsceneType
    }

    if (state.shotsType.length !== 0) {
      const tmpArr = notempty(state.shotsType)
      state.shotsType = tmpArr
      queryparam.shotsType = state.shotsType
    }
    if (state.postureType.length !== 0) {
      const tmpArr = notempty(state.postureType)
      state.postureType = tmpArr
      queryparam.postureType = state.postureType
    }
    if (state.peopleNum.length !== 0) {
      const tmpArr = notempty(state.peopleNum)
      state.peopleNum = tmpArr
      queryparam.peopleNum = state.peopleNum
    }
    if (state.lightType.length !== 0) {
      const tmpArr = notempty(state.lightType)
      state.lightType = tmpArr
      queryparam.lightType = state.lightType
    }
    if (state.caseType.length !== 0) {
      const tmpArr = notempty(state.caseType)
      state.caseType = tmpArr
      queryparam.caseType = state.caseType
    }
    if (state.algorithm.length !== 0) {
      const tmpArr = notempty(state.algorithm)
      state.algorithm = tmpArr
      queryparam.algorithm = state.algorithm
    }
    if (state.role.length !== 0) {
      const tmpArr = notempty(state.role)
      state.role = tmpArr
      queryparam.role = state.role
    }
    if (state.imgDAR !== '') queryparam.imgDAR = state.imgDAR
    if (state.imgWidth !== '') queryparam.imgWidth = state.imgWidth
    if (state.imgHeight !== '') queryparam.imgHeight = state.imgHeight
    if (state.imgSize !== '') queryparam.imgSize = state.imgSize
    if (state.resolutionRatio !== '') queryparam.resolutionRatio = state.resolutionRatio

    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }
    // console.log('ninghuanjun-从VS额--request--', payload)

    state.cvatomdataSource = []

    getCvsetData(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.cvatomdataSource = res.data
          state.csvURL = res.csv
          if (res.dataLength !== 0) {
            state.cvatomdataSource.map(item => {
              item.key = item.id
              return item
            })
          }
        }
      })
  }

  function handlePagination() {
    searchCvset()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchCvset()
  }

  function clearParam() {
    state.imgDAR = ''
    state.imgWidth = ''
    state.imgHeight = ''
    state.imgSize = ''
    state.resolutionRatio = ''
    state.sceneType = ''
    state.subsceneType = ''
    state.shotsType = ''
    state.postureType = ''
    state.peopleNum = ''
    state.lightType = ''
    state.caseType = ''
    state.algorithm = ''
    state.role = ''

    searchCvset()
  }

  function handleClose() {
    infoVisible.value = false
  }
  const handleAddJobset = () => {
    // showNewJobModal.value = true
    if ((state.csvchecked === false && state.selectedList.length === 0) || (state.csvchecked === true && state.csvURL === '')) {
      infoVisible.value = true
    } else {
      showNewJobModal.value = true
      infoVisible.value = false
    }
  }

  const handleSubmit = () => {
    const payload = {
      request: {
        userName, // 写死，接登入后改
        setName: createForm.value.setName,
        setContent: createForm.value.setContent ? createForm.value.setContent : '',
        setType: createForm.value.setType,
      },
    }
    if (state.csvchecked === true && state.csvURL !== '') payload.request.csv = state.csvURL
    if (state.csvchecked === false && state.selectedList !== []) payload.request.setIdList = state.selectedList
    console.log(payload)

    cvsetCreateTask(payload)
      .then(res => {
        createForm.value = { setName: '', setContent: '', setSource: '' }
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          showNewJobModal.value = false
          // 跳转到任务页面
          router.push({
            name: 'CVTask',
            params: {},
          })
        }
      })
  }

  const rowSelection = {
  }

  function jsonDetail(data:any) {
    showjsonDataModal.value = true
    state.jsonData = data
    maskCloseable.value = true
  }

  onMounted(() => {
    cvsetinit()
    searchCvset()
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
