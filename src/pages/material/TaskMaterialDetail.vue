<template>
  <!-- <NewBsBox :bs="{ margin: 20}">

<Banner
      style="font-size: 18px;font-weight:bold"
      type="warning"
      :closeable="false"
    >
      <template #icon>
        <Icon :icon="AlignTextBothOne" />
      </template>
<template #description>
        任务ID：{{state.theRequest['id']}}
      </template>
</Banner>
</NewBsBox> -->
  <BeerPanel :bs="{ boxShadow: 'none', flex: 1, margin: 20, pt: 0 }">
    <BeerTitle :bs="{ mb: 16 }"> 任务【{{ state.theRequest['setName'] }}】素材集 </BeerTitle>
    <BeerDivider />
    <br>
    <NewBsBox
      v-if="isIndexOfArray(AvsetType, state.theRequest['setType'])"
      :bs="{ ml: 0, mr: 0 }"
    >
      <Table
        :columns="jobDetailColumns"
        :data-source="state.jobDetailSource"
        :row-selection="rowSelection"
        :loading="loading"
        size="small"
        @selectedChange="handleSelectedChange"
        @selectedAll="handleSelectedAll"
      >
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
        <template #isMusic="{ data }">
          <Text>{{ data === 1 ? "是" : "否" }}</Text>
        </template>
        <template #filekey="{ data }">
          <Tooltip>
            <Tag
              color="green"
              size="small"
              @click="copyUrl(data)"
            >filekey</Tag>
            <template #content>
              {{ data }}
            </template>
          </Tooltip>
        </template>
        <template #fileurl="{ data }">
          <Tooltip>
            <Tag
              color="blue"
              size="small"
              @click="copyUrl(data)"
            >地址</Tag>
            <template #content>
              {{ data }}
            </template>
          </Tooltip>
        </template>
        <template #lightType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #colorType="{ data }">
          <Text>{{ getMapLable(colorTypeList, data) }}</Text>
        </template>
        <template #spaceType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #travelType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #contrastType="{ data }">
          <Text>{{ getMapLable(commonTypeList, data) }}</Text>
        </template>
        <template #isMovePicture="{ data }">
          <Text>{{ getMapLable(isMovePictureList, data) }}</Text>
        </template>
        <template #operation="{ rowData }">
          <Button
            size="small"
            :style="{ backgroundColor: '#3366FF', color: 'white' }"
            @click="jsonDetail(rowData)"
          >查看</Button>
        </template>

      </Table>

    </NewBsBox>
    <NewBsBox
      v-if="isIndexOfArray(CvsetType, state.theRequest.setType)"
      :bs="{ ml: 0, mr: 0 }"
    >
      <Table
        :columns="cvdetailColumns"
        :data-source="state.jobDetailSource"
        :row-selection="rowSelection"
        :loading="loading"
        size="small"
        @selectedChange="handleSelectedChange"
        @selectedAll="handleSelectedAll"
      >
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
        <template #filekey="{ data }">
          <Tooltip>
            <Tag
              color="green"
              size="small"
              @click="copyUrl(data)"
            >filekey</Tag>
            <template #content>
              {{ data }}
            </template>
          </Tooltip>
        </template>
        <template #fileurl="{ data }">
          <Tooltip>
            <Tag
              color="blue"
              size="small"
              @click="copyUrl(data)"
            >地址</Tag>
            <template #content>
              {{ data }}
            </template>
          </Tooltip>
        </template>

        <template #operation="{ rowData }">
          <Button
            size="small"
            :style="{ backgroundColor: '#3366FF', color: 'white' }"
            @click="jsonDetail(rowData)"
          >查看</Button>
        </template>

      </Table>
    </NewBsBox>
  </BeerPanel>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      :page-size-options="pageSizeOptions"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'
  import {
    Table, Text, Button, toast, Tooltip, Pagination, Tag,
  } from '@xhs/delight'
  import {
    NewBsBox, BeerPanel, BeerTitle, BeerDivider,
  } from '@xhs/yam-beer'
  //   import {
  //     AlignTextBothOne,
  //   } from '@xhs/delight/icons'
  import { checkData, cvcheckData } from '../../services/material'
  import {
    commonTypeList, colorTypeList, getMapLable, isIndexOfArray, CvsetType, AvsetType, isMovePictureList, pageSizeOptions,
  } from '../../utils/common'

  const route = useRoute()
  const jobDetailColumns = [
    {
      title: 'id',
      dataIndex: 'id',
      minWidth: 50,
    },
    {
      title: 'videoid',
      dataIndex: 'videoid',
      minWidth: 80,
    },
    {
      title: '场景分类',
      dataIndex: 'sceneType',
      minWidth: 80,
    },
    {
      title: '是否纯音频',
      dataIndex: 'isMusic',
      minWidth: 100,
    },
    {
      title: 'cloud',
      dataIndex: 'cloud',
      minWidth: 70,
    },

    {
      title: 'filekey',
      dataIndex: 'filekey',
      minWidth: 70,
    },
    {
      title: 'fileurl',
      dataIndex: 'fileurl',
      minWidth: 70,
    },
    {
      title: '详情',
      dataIndex: 'videodetails',
      minWidth: 40,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      minWidth: 80,
    },
    {
      title: '视频编码',
      dataIndex: 'videoCodec',
      minWidth: 80,
    },
    {
      title: '宽高比',
      dataIndex: 'videoDAR',
      minWidth: 70,
    },
    {
      title: 'width',
      dataIndex: 'videoWidth',
      minWidth: 70,
    },
    {
      title: 'height',
      dataIndex: 'videoHeight',
      minWidth: 70,
    },
    {
      title: 'fps',
      dataIndex: 'videoFps',
      minWidth: 60,
    },
    {
      title: 'pix_fmt',
      dataIndex: 'pix_fmt',
      minWidth: 80,
    },
    {
      title: 'color_space',
      dataIndex: 'color_space',
      minWidth: 110,
    },
    {
      title: 'color_transfer',
      dataIndex: 'colorTransfer',
      minWidth: 120,
    },
    {
      title: 'color_primaries',
      dataIndex: 'colorPrim',
      minWidth: 130,
    },
    {
      title: 'color_range',
      dataIndex: 'color_range',
      minWidth: 110,
    },
    {
      title: 'bits_per_raw_sample',
      dataIndex: 'bits_per_raw_sample',
      minWidth: 170,
    },
    {
      title: 'profile',
      dataIndex: 'profile',
      minWidth: 70,
    },
    {
      title: 'level',
      dataIndex: 'level',
      minWidth: 60,
    },
    {
      title: 'refs',
      dataIndex: 'refs',
      minWidth: 60,
    },
    {
      title: 'video_duration',
      dataIndex: 'duration',
      minWidth: 130,
    },
    {
      title: '音频编码',
      dataIndex: 'audioCodec',
      minWidth: 80,
    },
    {
      title: 'sample_rate',
      dataIndex: 'audioSampleRate',
      minWidth: 120,
    },
    {
      title: 'channels',
      dataIndex: 'audiochannel',
      minWidth: 90,
    },
    {
      title: 'audio_duration',
      dataIndex: 'audio_duration',
      minWidth: 130,
    },
    // {
    //   title: '封装类型',
    //   dataIndex: 'packageType',
    //   minWidth: 80,
    // },
    // {
    //   title: 'Hdr',
    //   dataIndex: 'isHdr',
    //   minWidth: 60,
    // },
    // {
    //   title: '图片编码',
    //   dataIndex: 'picCodec',
    //   minWidth: 80,
    // },
    // {
    //   title: '视频bit',
    //   dataIndex: 'videoDynamic',
    //   minWidth: 80,
    // },
    // {
    //   title: '不良类型',
    //   dataIndex: 'badType',
    //   // width: 55,
    // },
    // {
    //   title: '亮度',
    //   dataIndex: 'lightType',

    // },
    // {
    //   title: '饱和度',
    //   dataIndex: 'colorType',
    //   // width: 55,
    // },
    // {
    //   title: '空间复杂度',
    //   dataIndex: 'spaceType',
    //   // width: 70,
    // },
    // {
    //   title: '运动复杂度',
    //   dataIndex: 'travelType',
    //   // width: 70,
    // },
    // {
    //   title: '对比度',
    //   dataIndex: 'contrastType',
    //   // width: 55,
    // },
    // {
    //   title: 'isVolume',
    //   dataIndex: 'isVolume',
    //   minWidth: 90,
    // },
    // {
    //   title: 'MovePicture',
    //   dataIndex: 'isMovePicture',
    //   minWidth: 110,
    // },
    // {
    //   title: '操作',
    //   dataIndex: 'operation',
    //   fixed: 'right',
    // },
  ]

  const cvdetailColumns = [
    {
      title: '场景分类',
      dataIndex: 'sceneType',
    },
    {
      title: '二级场景分类',
      dataIndex: 'subsceneType',
    },
    {
      title: '景别',
      dataIndex: 'shotsType',
    },
    {
      title: '姿态',
      dataIndex: 'postureType',
    },
    {
      title: '人数',
      dataIndex: 'peopleNum',
    },
    {
      title: '光线',
      dataIndex: 'lightType',
    },
    {
      title: 'filekey',
      dataIndex: 'filekey',
    },
    {
      title: 'fileurl',
      dataIndex: 'fileurl',
    },
    {
      title: '宽高比',
      dataIndex: 'imgDAR',
    },
    {
      title: '图像宽度',
      dataIndex: 'imgWidth',
    },
    {
      title: '图像高度',
      dataIndex: 'imgHeight',
    },
    {
      title: '图像大小',
      dataIndex: 'imgSize',
    },
    {
      title: 'case属性',
      dataIndex: 'caseType',
    },
    {
      title: '适用算法',
      dataIndex: 'algorithm',
    },
    // {
    //   title: '操作',
    //   dataIndex: 'operation',
    //   fixed: 'right',
    // },

  ]

  const loading = ref(false)
  const state = reactive({
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    jobDetailSource: [],
    filtersetName: '',
    theRequest: {
      id: '',
      setType: '',
      setName: '',
    },
    sceneTypeList: [],
    packageTypeList: [],
    badTypeList: [],
    videoFpsList: [],
    videoCodecList: [],
    audioCodecList: [],
    picCodecList: [],
    pix_fmtList: [],
    colorTransferList: [],
    colorPrimList: [],
    videodetailsList: [],

  })

  function avsetinit() {
    state.sceneTypeList = []
    state.packageTypeList = []
    state.badTypeList = []
    state.videoFpsList = []
    state.videoCodecList = []
    state.audioCodecList = []
    state.picCodecList = []
    state.pix_fmtList = []
    state.colorTransferList = []
    state.colorPrimList = []
    state.videodetailsList = []
  }

  function getTaskDetail(id: any, type: any) {
    loading.value = true
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        id,

      },
    }

    state.jobDetailSource = []

    if (isIndexOfArray(AvsetType, type)) {
      checkData(payload)
        .then(res => {
          loading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.dataLength = res.dataLength
            state.jobDetailSource = res.data
          }
        })
    }
    if (isIndexOfArray(CvsetType, type)) {
      cvcheckData(payload)
        .then(res => {
          loading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.dataLength = res.dataLength
            state.jobDetailSource = res.data
          }
        })
    }
  }

  function handlePagination() {
    getTaskDetail(state.theRequest.id, state.theRequest.setType)
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getTaskDetail(state.theRequest.id, state.theRequest.setType)
  }

  //   function jumpDetailSet(data) {
  //   }

  //   function downloadSet(param) {
  //     const payload = {
  //       request: {
  //         id: param.id,
  //       },
  //     }
  //     taskDownload(payload)
  //       .then(res => {
  //         if (res.status !== 0) {
  //           toast.danger(res.message)
  //         } else {
  //           toast.success(res.message)
  //           window.open(res.data)
  //         }
  //       })
  //   }

  onMounted(() => {
    avsetinit()
    // state.theRequest = route.params
    state.theRequest = {
      id: route.params.id.toString(),
      setType: route.params.setType.toString(),
      setName: route.params.setName.toString(),
    }
    console.log('ssss------', state.theRequest)
    getTaskDetail(state.theRequest.id, state.theRequest.setType)
  })

</script>
