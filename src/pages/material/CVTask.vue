<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="end"
  >

    <NewBsBox
      style="float:right;"
      :bs="{ boxShadow: 'none', mr: -20,mb: -20, flex: 1,}"
    >
      <SearchInput
        v-model="state.filtersetName"
        action-position="inner"
        placeholder="根据任务名称搜索"
        @search="searchJobset"
      />
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <Table
      :columns="jobColumns"
      :data-source="state.jobdataSource"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #setType="{ data }">
        <Tag
          color="teal"
          size="small"
        >{{ getMapLable(setTypeList,data) }}</Tag>
      </template>
      <template #operation="{ rowData }">
        <Button
          size="small"
          :style="{ backgroundColor: '#3366FF', color: 'white' }"
          @click="jumpDetailSet(rowData)"
        >详细</Button>
        <Button
          size="small"
          :style="{ backgroundColor: '#28BC77', color: 'white' }"
          @click="downloadSet(rowData)"
        >下载</Button>
      </template>
    </Table>
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Space, Table, Text, Button, toast, Pagination, Tag,
  } from '@xhs/delight'
  import { SearchInput, NewBsBox } from '@xhs/yam-beer'
  //   import {
  //      Help,
  //   } from '@xhs/delight/icons'
  import { getCVTaskSet } from '../../services/material'
  import { setTypeList, getMapLable } from '../../utils/common'

  const router = useRouter()

  const jobColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
    },
    {
      title: '任务名称',
      dataIndex: 'setName',
    },
    {
      title: '任务详情',
      dataIndex: 'setContent',
    },
    {
      title: '素材集类型',
      dataIndex: 'setType',
    },
    {
      title: '下载地址',
      dataIndex: 'cosCsvUrl',
    },

    {
      title: '创建人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]
  const loading = ref(false)
  const state = reactive({
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    jobdataSource: [],
    filtersetName: '',

  })

  function searchJobset() {
    loading.value = true
    const queryparam = {}
    if (state.filtersetName !== '') queryparam.setName = state.filtersetName
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }
    console.log('ninghuanjun-jobrequest--', payload)

    state.jobdataSource = []
    getCVTaskSet(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.jobdataSource = res.data
        //   if(res.dataLength!=0){
        //       state.jobdataSource.map((item, index) => {
        //       item.key = item.id
        //       return item
        //     })
        //   }
        }
      })
  }

  function handlePagination() {
    searchJobset()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchJobset()
  }

  function jumpDetailSet(data) {
    console.log('ninghuanjun---来源---', data)
    // 跳转到任务页面
    router.push({
      name: 'TaskMaterialDetail',
      params: {
        id: data.id,
        setName: data.setName,
        setType: data.setType,
      },
    })
  }

  function downloadSet(param) {
    let strurl = ''
    if (param.cosCsvUrl.indexOf('http:') !== -1) {
      strurl = param.cosCsvUrl.replace(/http/, 'https')
    } else {
      strurl = param.cosCsvUrl
    }

    const filename = strurl.split('/')[strurl.split('/').length - 1]
    const x = new XMLHttpRequest()
    x.open('GET', strurl, true)
    x.responseType = 'blob'
    x.onload = e => {
      console.log(e)
      // 会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
      const url = window.URL.createObjectURL(x.response)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.click()
    }
    x.send()
  }

  onMounted(() => {
    searchJobset()
  })

</script>
