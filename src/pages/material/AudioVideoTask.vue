<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="end"
  >
    <NewBsBox
      :bs="{ display: 'flex', width: '100%', flexDirection: 'row', justifyContent: 'space-between', gap: '10px' }"
    >
      <NewBsBox :bs="{ boxShadow: 'none' }">
        <Button
          style="background-color:#28BC77;color:white;"
          size="small"
          :icon="{ icon: Redo }"
          @click="recreateDailyTask()"
        >巡检素材集重试</Button>
      </NewBsBox>

      <NewBsBox :bs="{ boxShadow: 'none', display: 'flex', gap: '10px', alignItems: 'center' }">
        <Text bold>素材类型:</Text>
        <Select
          v-model="state.filterSourceType"
          :options="sourcetypeOptions"
          clearable
          placeholder="请选择素材类型"
          :style="{ width: '150px' }"
          @change="searchJobset"
        />
        <SearchInput
          v-model="state.filtersetName"
          action-position="inner"
          placeholder="根据任务名称搜索"
          @search="searchJobset"
        />
      </NewBsBox>
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ ml: 20, mr: 20, }">
    <Table
      :style="{ border: '1px solid #ccc' }"
      :columns="jobColumns"
      :data-source="state.jobdataSource"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #setType="{ data }">
        <Tag
          color="teal"
          size="small"
        >{{ getMapLable(setTypeList, data) }}</Tag>
      </template>
      <template #sceneType="{ data }">
        <Tag
          color="cyan"
          size="small"
        >{{ getMapLable(sceneTypeList, data) }}</Tag>
      </template>
      <template #setEnv="{ data }">
        <Tag
          color="purple"
          size="small"
        >{{ data }}</Tag>
      </template>
      <template #sourceType="{ data }">
        <Tag
          color="blue"
          size="small"
        >{{ getMapLable(sourcetypeList, data) }}</Tag>
      </template>
      <template #operation="{ rowData }">
        <Button
          size="small"
          style="background-color:#3366FF;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          @click="showCopySet(rowData)"
        >复制</Button>
        <Button
          size="small"
          style="background-color:#3366FF;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          @click="jumpDetailSet(rowData)"
        >详细</Button>
        <Button
          size="small"
          style="background-color:#28BC77;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          @click="downloadSet(rowData)"
        >下载</Button>
        <Popconfirm
          title="删除"
          description="确认删除该任务吗？"
          @confirm="handleDelConfirm(rowData)"
          @cancel="handleTaskClose"
        >
          <Button
            size="small"
            style="background-color:#f03860;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          >删除</Button>
        </Popconfirm>
      </template>
    </Table>
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      :page-size-options="pageSizeOptions"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <Modal
    v-model:visible="showCopySetModal"
    title="任务素材集"
    :with-footer="false"
    :style="{ width: 'auto' }"
  >
    <Form
      v-model="copyForm"
      :style="{ marginTop: '20px' }"
      size="default"
      @submit="handleCopySet"
      @reset="resetCopySet"
      @close="closeCopySet"
    >
      <FormItem
        name="setName"
        label="任务素材集名称"
      >
        <Input
          v-model="copyForm.setName"
          required
        />
      </FormItem>
      <FormItem
        name="setType"
        label="素材集类型"
      >
        <Select
          v-model="copyForm.setType"
          :options="setTypeList"
          required
        />
      </FormItem>
      <FormItem
        name="setEnv"
        label="环境"
      >
        <RadioGroup
          v-model="copyForm.setEnv"
          :options="[{ label: 'sit', value: 'sit', checked: true }, { label: 'prod', value: 'prod' }]"
          required
        />
      </FormItem>
      <FormItem
        name="hasVideoId"
        label="是否生成视频ID"
      >
        <RadioGroup
          v-model="copyForm.hasVideoId"
          :options="[{ label: '是', value: '1', checked: true }, { label: '否', value: '0' }]"
          required
        />
      </FormItem>
      <FormItem
        name="setContent"
        label="任务素材集详情"
      >
        <TextArea v-model="copyForm.setContent" />
      </FormItem>
    </Form>
  </Modal>

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  // import { useRouter } from 'vue-router'
  import { Redo } from '@xhs/delight/icons'
  import {
    Space, Table, Text, Button, toast, Pagination, Tag, Popconfirm, Modal, FormItem, Input, Form, Select, RadioGroup, TextArea,
  } from '@xhs/delight'
  import { SearchInput, NewBsBox } from '@xhs/yam-beer'
  //   import {
  //      Help,
  //   } from '@xhs/delight/icons'
  // import * as XLSX from 'xlsx'
  import {
    generateTaskMaterialDaily, getTaskSet, deleteTask, avsetCreateTask, checkData,
  } from '../../services/material'
  import { setTypeList, getMapLable, pageSizeOptions } from '../../utils/common'

  const sourcetypeList = [
    { label: '视频', value: '1' },
    // { name: '音频', value: '2' },
    { label: '图片', value: '3' }, 
  ]

  // 用于下拉选择器的选项
  const sourcetypeOptions = [
    { label: '视频', value: '1' },
    { label: '图片', value: '3' },
  ]
  const sceneTypeList = [
    { label: '国内', value: '无' },
    { label: '海外', value: '海外' },
  ]
  // const router = useRouter()

  const jobColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '任务名称',
      dataIndex: 'setName',
    },
    {
      title: '场景',
      dataIndex: 'sceneType',
      minWidth: 60,
    },
    {
      title: '环境',
      dataIndex: 'setEnv',
      minWidth: 60,
    },
    {
      title: '素材类型',
      dataIndex: 'sourceType',
    },
    {
      title: '任务详情',
      dataIndex: 'setContent',
    },
    {
      title: '素材集类型',
      dataIndex: 'setType',
      minWidth: 100,
    },
    // {
    //   title: '下载地址',
    //   dataIndex: 'cosCsvUrl',
    // },
    {
      title: '创建人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]

  const showCopySetModal = ref(false)
  const copyForm = ref({
    userName: '',
    setName: '',
    setType: '',
    setEnv: '',
    hasVideoId: '',
    setContent: '',
    setSource: '',
    sourceType: '',
    setIdList: [],
  })

  const loading = ref(false)
  const state = reactive({
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    jobdataSource: [],
    filtersetName: '',
    filterSourceType: '', // 新增素材类型筛选字段

  })

  function recreateDailyTask() {
    const payload = {
      request: {
        before_n_day: 2,
      },
    }
    generateTaskMaterialDaily(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
        }
      })
  }

  function searchJobset() {
    loading.value = true
    const queryparam = {}
    if (state.filtersetName !== '') queryparam.setName = state.filtersetName
    if (state.filterSourceType !== '') queryparam.sourceType = state.filterSourceType
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }
    console.log('ninghuanjun-jobrequest--', payload)

    state.jobdataSource = []

    getTaskSet(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.jobdataSource = res.data
          //   if(res.dataLength!=0){
          //       state.jobdataSource.map((item, index) => {
          //       item.key = item.id
          //       return item
          //     })
          //   }
        }
      })
  }

  function handleDelConfirm(data: any) {
    const payload = {
      request: {
        id: data.id,
      },
    }
    deleteTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          searchJobset()
        }
      })
  }

  //
  function handleTaskClose() {
    console.log('删除')
  }
  function handlePagination() {
    searchJobset()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchJobset()
  }

  // async function getCsvData(csvUrl) {
  //   const strUrl = csvUrl.includes('http:') ? csvUrl.replace('http', 'https') : csvUrl

  //   try {
  //     console.log('csvUrl', csvUrl)
  //     const response = await fetch(strUrl)

  //     if (!response.ok) {
  //       toast.danger(`Failed to fetch CSV file: ${response.statusText}`)
  //       return []
  //     }

  //     const blob = await response.blob()
  //     const arrayBuffer = await blob.arrayBuffer()
  //     const workbook = XLSX.read(arrayBuffer, { type: 'array' })
  //     const sheetName = workbook.SheetNames[0]
  //     const worksheet = workbook.Sheets[sheetName]

  //     // 将工作表转换为 JSON
  //     const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
  //     // 保留完整数据
  //     return jsonData
  //   } catch (error) {
  //     toast.danger(`Error fetching or processing CSV: ${error}`)
  //     return []
  //   }
  // }

  function resetCopySet() {
    copyForm.value = {
      userName: '',
      setName: '',
      setType: '',
      setEnv: '',
      hasVideoId: '',
      setContent: '',
      setSource: '',
      setIdList: [],
    }
  }

  function closeCopySet() {
    showCopySetModal.value = false
    resetCopySet()
  }

  function handleCopySet(data) {
    const payload = {
      request: {
        userName: data.userName,
        setName: data.setName,
        setContent: data.setContent ? data.setContent : '',
        setType: data.setType,
        setEnv: data.setEnv,
        hasVideoId: data.hasVideoId,
        sourceType: '1',
        sceneType: data.sceneType,
        bizId: Date.now(),
        setIdList: data.setIdList,
      },
    }

    avsetCreateTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          closeCopySet()
          searchJobset()
        }
      })
  }

  function showCopySet(data) {
    loading.value = true
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: 10000,
        id: data.id,
      },
    }
    checkData(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          // 提取 id 列的数据
          const ids = res.data.map(item => item.id)
          console.log(ids) // 打印id列表
          copyForm.value = {
            userName: data.userName,
            setName: data.setName,
            setType: data.setType.toString(),
            setEnv: data.setEnv,
            hasVideoId: data.hasVideoId,
            setContent: data.setContent,
            setSource: data.cosCsvUrl,
            sourceType: '1',
            sceneType: data.sceneType,
            setIdList: ids,
          }
          console.log('copyForm', copyForm.value)
          showCopySetModal.value = true
        }
      })
  }

  function jumpDetailSet(data) {
    const id = encodeURIComponent(data.id)
    const setType = encodeURIComponent(data.setType)
    const setName = encodeURIComponent(data.setName)
    
    // 根据本地环境区分使用不同地址
    // 调试环境变量
    console.log('import.meta.env:', import.meta.env)
    console.log('VITE_ENV:', (import.meta.env as any)?.VITE_ENV)
    console.log('MODE:', (import.meta.env as any)?.MODE)
    console.log('NODE_ENV:', (import.meta.env as any)?.NODE_ENV)
    
    // 尝试多种方式获取环境信息
    const isDev = (import.meta.env as any)?.VITE_ENV === 'dev' || 
                  (import.meta.env as any)?.MODE === 'development' || 
                  (import.meta.env as any)?.NODE_ENV === 'development' ||
                  window.location.hostname === 'localhost' || 
                  window.location.hostname === '127.0.0.1' || 
                  window.location.port === '3000'
    
    console.log('是否为开发环境:', isDev)
    let url = ''
    if (isDev) {
      url = `http://127.0.0.1:1388/material/taskmaterialdetail/${id}/${setType}/${setName}`
    } else {
      url = `https://va.devops.sit.xiaohongshu.com/material/taskmaterialdetail/${id}/${setType}/${setName}`
    }
    console.log('最终URL:', url)
    window.open(url, '_blank')
  }

  function downloadSet(param) {
    let strurl = ''
    if (param.cosCsvUrl.indexOf('http:') !== -1) {
      strurl = param.cosCsvUrl.replace(/http/, 'https')
    } else {
      strurl = param.cosCsvUrl
    }

    const filename = strurl.split('/')[strurl.split('/').length - 1].split('?')[0]
    const x = new XMLHttpRequest()
    x.open('GET', strurl, true)
    x.responseType = 'blob'
    x.onload = e => {
      console.log(e)
      // 会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
      const url = window.URL.createObjectURL(x.response)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.click()
    }
    x.send()
  }

  onMounted(() => {
    searchJobset()
  })

</script>
