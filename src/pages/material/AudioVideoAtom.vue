<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'space-between', width: '100%' }">
      <NewBsBox :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">
        <ButtonGroup
          v-model="state.activeItem"
          :options="sourcetypeList"
          active-variant="primary"
          variant="secondary"
          @update:modelValue="changeSourcetype"
        />
      </NewBsBox>
      <NewBsBox :bs="{ boxShadow: 'none', mr: 30, flex: 1, }">
        <!-- <Button
          style="float:right;"
          type="primary"
          :icon="{ icon: Add }"
        >新增素材</Button> -->
      </NewBsBox>
    </NewBsBox>

    <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>场景分类:</Text>
        <Select
          v-model="state.sceneTypeSelect"
          :style="{ width: '150px' }"
          type="secondary"
          :options="state.sceneTypeList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 10, flex: 1, alignItems: 'center' }">
        <Text
          bold
          style="margin-right: 5px;"
        >纯音频:</Text>
        <RadioGroup v-model="state.isMusic">
          <Radio
            value="-1"
            label="全部"
            :style="{ width: '20px' }"
            checked
          />
          <Radio
            value="1"
            label="是"
            :style="{ width: '20px' }"
          />
          <Radio
            value="0"
            label="否"
            :style="{ width: '20px' }"
          />
          <!-- <Text>当前选中项：{{ state.isMusic }}</Text> -->
        </RadioGroup>
      </NewBsBox>
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>封装类型:</Text>
        <Select
          v-model="state.packageTypeSelect"
          :style="{ width: '150px' }"
          type="secondary"
          :options="state.packageTypeList"
          filterable
          clearable
        />
      </NewBsBox> -->
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 10, flex: 1, alignItems: 'center' }">
        <Text bold>cloud:</Text>
        <Select
          v-model="state.cloudSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.cloudList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 10, flex: 1, alignItems: 'center' }">
        <Text
          bold
          style="width: 50px;"
        >filekey:</Text>
        <Input
          v-model="state.filekey"
          :style="{ width: '240px' }"
        />
      </NewBsBox>
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>色调:</Text>
        <Select
          v-model="state.colorPrimSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.colorPrimList"
          filterable
          clearable
        />
      </NewBsBox> -->
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>色域:</Text>
        <Select
          v-model="state.colorTransferSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.colorTransferList"
          filterable
          clearable
        />
      </NewBsBox> -->
    </NewBsBox>

    <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>videoDynamic:</Text>
        <Select
          v-model="state.videoDynamicSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="videoDynamicList"
          filterable
          clearable
        />
      </NewBsBox> -->
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text
          v-if="state.activeItem === '1'"
          bold
          style="width: 80px;"
        >视频编码:</Text><Text
          v-if="state.activeItem === '3'"
          bold
          style="width: 80px;"
        >图片编码:</Text>
        <Select
          v-model="state.videoCodecSelect"
          :style="{ width: '120px', marginRight: '10px' }"
          type="secondary"
          :options="state.videoCodecList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoCodecSelect}}</Text> -->
      </NewBsBox>
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>视频采样格式:</Text>
        <Select
          v-model="state.pix_fmtSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.pix_fmtList"
          filterable
          clearable
          multiple
          multi-line
        />
      </NewBsBox> -->
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>宽高比:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.videoDAR"
          :style="{ width: '90px' }"
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>width:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.videoWidth"
          :style="{ width: '90px' }"
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>height:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.videoHeight"
          :style="{ width: '90px' }"
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>pix_fmt:</Text>
        <Select
          v-model="state.pix_fmtSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.pix_fmtList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
    </NewBsBox>
    <NewBsBox
      v-if="state.activeItem === '1'"
      :bs="{ display: 'flex', width: '100%' }"
    >
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>isHdr:</Text>
        <Input
          v-model="state.isHdr"
          :style="{ width: '90px' }"
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>r_frame_rate:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.r_frame_rate"
          :style="{ width: '90px' }"
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>avg_frame_rate:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.videoFps"
          :style="{ width: '90px' }"
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>fps_type:</Text>
        <Select
          v-model="state.fps_typeSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.fps_typecList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
    </NewBsBox>
    <NewBsBox
      v-if="state.activeItem === '1' || state.activeItem === '3'"
      :bs="{ display: 'flex', width: '100%' }"
    >
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>color_space:</Text>
        <Select
          v-model="state.color_spaceSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.color_spaceList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>color_transfer:</Text>
        <Select
          v-model="state.colorTransferSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.colorTransferList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>color_primaries:</Text>
        <Select
          v-model="state.colorPrimSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.colorPrimList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>color_range:</Text>
        <Select
          v-model="state.color_rangeSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.color_rangeList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
      <!-- <NewBsBox
        v-if="state.activeItem !== '3'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>时长:</Text>
        <Tooltip content="单位:s 示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.duration"
          :style="{ width: '90px' }"
        />
      </NewBsBox> -->
      <!-- <NewBsBox
        v-if="state.activeItem !== '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>图片编码格式:</Text>
        <Select
          v-model="state.picCodecSelect"
          :style="{ width: '220px' }"
          type="secondary"
          :options="state.picCodecList"
          multiple
          filterable
          clearable
          multi-line
        />
      </NewBsBox> -->
    </NewBsBox>

    <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text
          bold
          style="width: 150px;"
        >bits_per_raw_sample:</Text>
        <Input
          v-model="state.bits_per_raw_sample"
          :style="{ width: '80px' }"
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          :style="{ width: '50px', marginLeft: '10px' }"
        >profile:</Text>
        <Input
          v-model="state.profile"
          :style="{ width: '100px' }"
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>rotation:</Text>
        <Select
          v-model="state.rotationSelect"
          :style="{ width: '100px' }"
          type="secondary"
          :options="state.rotationList"
          multiple
          filterable
          clearable
          multi-line
        />
        <!-- <Text bold>{{state.videoFpsSelect}}</Text> -->
      </NewBsBox>
    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          style="width: 50px;"
        >level:</Text>
        <Input
          v-model="state.level"
          :style="{ width: '80px' }"
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          style="width: 50px;"
        >refs:</Text>
        <Input
          v-model="state.refs"
          :style="{ width: '80px' }"
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          style="width: 50px;"
        >avg_si:</Text>
        <Input
          v-model="state.avg_si"
          :style="{ width: '80px' }"
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          style="width: 50px;"
        >avg_ti:</Text>
        <Input
          v-model="state.avg_ti"
          :style="{ width: '80px' }"
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text
          v-if="state.activeItem === '1'"
          bold
          style="width: 105px;"
        >video_duration:</Text>
        <Tooltip
          v-if="state.activeItem === '1'"
          content="示例:x-y(英文-)"
        >
          <Icon :icon="Help" />
        </Tooltip>
        <Text
          v-if="state.activeItem === '3'"
          bold
          style="width: 60px;"
        >file_size:</Text>
        <Input
          v-model="state.duration"
          :style="{ width: '80px' }"
        />
      </NewBsBox>
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>亮度:</Text>
        <Select
          v-model="state.lightTypeSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="commonTypeList"
          filterable
          clearable
        />
      </NewBsBox> -->
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>饱和度:</Text>
        <Select
          v-model="state.colorTypeSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="colorTypeList"
          filterable
          clearable
        />
      </NewBsBox> -->
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>空间复杂度:</Text>
        <Select
          v-model="state.spaceTypeSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="commonTypeList"
          filterable
          clearable
        />
      </NewBsBox> -->
      <!-- <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>运动复杂度:</Text>
        <Select
          v-model="state.travelTypeSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="commonTypeList"
          filterable
          clearable
        />
      </NewBsBox> -->
    </NewBsBox>

    <NewBsBox
      v-if="state.activeItem === '1'"
      :bs="{ display: 'flex', width: '100%' }"
    >
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>音频编码:</Text>
        <Select
          v-model="state.audioCodecSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.audioCodecList"
          multiple
          filterable
          clearable
          multi-line
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>sample_rate:</Text>
        <Select
          v-model="state.audioSampleRateSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="audioSampleRateList"
          filterable
          clearable
        />
      </NewBsBox>

      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>channels:</Text>
        <Input
          v-model="state.audiochannel"
          :style="{ width: '90px' }"
        />
      </NewBsBox>

      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>audio_duration:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.audio_duration"
          :style="{ width: '90px' }"
        />
      </NewBsBox>

    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          style="width: 100px;"
        >video_bit_rate:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.video_bit_rate"
          :style="{ width: '100px' }"
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text
          bold
          style="width: 105px;"
        >audio_bit_rate:</Text>
        <Tooltip content="示例:x-y(英文-)">
          <Icon :icon="Help" />
        </Tooltip>
        <Input
          v-model="state.audio_bit_rate"
          :style="{ width: '100px' }"
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>详情:</Text>
        <Input
          v-model="state.videodetails"
          :style="{ width: '200px' }"
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem !== '3'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>不良类型:</Text>
        <Select
          v-model="state.badTypeSelect"
          :style="{ width: '150px' }"
          type="secondary"
          :options="state.badTypeList"
          filterable
          clearable
        />
      </NewBsBox>
    </NewBsBox>

    <!-- <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>HDR:</Text>
        <Select
          v-model="state.isHdrSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.isHdrList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>对比度:</Text>
        <Select
          v-model="state.contrastTypeSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="commonTypeList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem !== '3'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>响度均衡算法:</Text>
        <Select
          v-model="state.isVolumeSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="isVolumeList"
          filterable
          clearable
        />
      </NewBsBox>
    </NewBsBox>

    <NewBsBox :bs="{ display: 'flex', width: '100%' }">
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>视频细节:</Text>
        <Select
          v-model="state.videodetailsSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.videodetailsList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }">
        <Text bold>存储类型:</Text>
        <Select
          v-model="state.cloudSelect"
          :style="{ width: '120px' }"
          type="secondary"
          :options="state.cloudList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '3'"
        :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }"
      >
        <Text bold>是否是动图:</Text>
        <Select
          v-model="state.isMovePicture"
          :style="{ width: '120px' }"
          type="secondary"
          :options="isMovePictureList"
          filterable
          clearable
        />
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', boxShadow: 'none', margin: 0, flex: 1, alignItems: 'center' }" />
    </NewBsBox> -->

    <NewBsBox :bs="{ display: 'flex', width: '100%', flexDirection: 'row-reverse' }">
      <NewBsBox :bs="{ boxShadow: 'none', mr: 20, flex: 1, }">
        <Button
          style="float:right;margin:10px"
          type="secondary"
          :icon="{ icon: Clear }"
          @click="clearParam"
        >重置</Button>
        <Button
          style="float:right;margin:10px"
          type="primary"
          :icon="{ icon: Search }"
          @click="avsetQuery()"
        >查询</Button>
      </NewBsBox>
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ display: 'flex', width: '100%' }">
    <NewBsBox
      :bs="{ display: 'flex', boxShadow: 'none', ml: 30, alignItems: 'center', justifyContent: 'space-between', width: '100%', marginRight: '20px' }"
    >
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <Checkbox
          v-model:checked="state.csvchecked"
          style="margin-right:20px"
          value="全部查询结果"
          label="全部查询结果"
          description="勾选后，通过csv创建任务素材集"
          :on-update:checked="changeChecked"
        />
        <NewBsBox :bs="{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }">
          <Button
            :style="{ backgroundColor: '#28BC77', color: 'white' }"
            :icon="{ icon: Add }"
            style="margin-right: 10px;"
            @click="handleAddJobset"
          >创建任务</Button>
          <Button
            :style="{ backgroundColor: '#28BC77', color: 'white' }"
            :icon="{ icon: Add }"
            @click="handleInsertJobset"
          >插入素材</Button>
        </NewBsBox>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }">
        <Button
          type="primary"
          :icon="{ icon: Add }"
          style="margin-right: 10px;"
          @click="addLocalMaterial"
        >本地上传</Button>
        <Button
          type="primary"
          :icon="{ icon: Add }"
          style="margin-right: 10px;"
          @click="addMultiMaterial"
        >批量添加</Button>
        <Button
          type="primary"
          :icon="{ icon: Add }"
          style="margin-right: 10px;"
          @click="AddMaterials"
        >添加素材</Button>

        <Button
          type="danger"
          :icon="{ icon: Add }"
          @click="deleteMultiMaterial"
        >批量删除</Button>
      </NewBsBox>
    </NewBsBox>
  </NewBsBox>

  <NewBsBox
    :bs="{ margin: 20, }"
    :style="{ textAlign: 'center', }"
  >
    <!-- <Text>selected: {{state.selectedList}}</Text> -->
    <!-- <Spinner
      :spinning="loading"
      tip="加载中"
      size="large"
    > -->
    <Table
      v-if="state.activeItem === '1'"
      v-model:selected="state.selectedList"
      :style="{ border: '1px solid #ccc' }"
      :columns="videocolumns"
      :data-source="state.dataSource"
      :row-selection="rowSelection"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #isMusic="{ data }">
        <Text>{{ data === 1 ? "是" : "否" }}</Text>
      </template>
      <template #filekey="{ data }">
        <Tooltip>
          <Tag
            color="green"
            size="small"
            @click="copyUrl(data)"
          >filekey</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #fileurl="{ data }">
        <Tooltip>
          <Tag
            color="blue"
            size="small"
            @click="copyUrl(data)"
          >地址</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #lightType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #colorType="{ data }">
        <Text>{{ getMapLable(colorTypeList, data) }}</Text>
      </template>
      <template #spaceType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #travelType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #contrastType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #isMovePicture="{ data }">
        <Text>{{ getMapLable(isMovePictureList, data) }}</Text>
      </template>
      <template #operation="{ rowData }">
        <Button
          size="small"
          style="background-color:#3366FF;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
          @click="jsonDetail(rowData)"
        >查看</Button>
        <Button
          size="small"
          style="background-color:#999966;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          type="primary"
          @click="handleeditMaterial(rowData)"
        >编辑</Button>
        <Popconfirm
          title="删除"
          description="确认删除该条素材吗？"
          @confirm="handleDelConfirm(rowData)"
          @cancel="handleMaterialClose"
        >
          <Button
            size="small"
            style="background-color:#f03860;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          >删除</Button>
        </Popconfirm>
      </template>

    </Table>
    <Table
      v-if="state.activeItem === '3'"
      v-model:selected="state.selectedList"
      :style="{ border: '1px solid #ccc' }"
      :columns="picturecolumns"
      :data-source="state.dataSource"
      :row-selection="rowSelection"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #isMusic="{ data }">
        <Text>{{ data === 1 ? "是" : "否" }}</Text>
      </template>
      <template #filekey="{ data }">
        <Tooltip>
          <Tag
            color="green"
            size="small"
            @click="copyUrl(data)"
          >filekey</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #fileurl="{ data }">
        <Tooltip>
          <Tag
            color="blue"
            size="small"
            @click="copyUrl(data)"
          >地址</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #lightType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #colorType="{ data }">
        <Text>{{ getMapLable(colorTypeList, data) }}</Text>
      </template>
      <template #spaceType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #travelType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #contrastType="{ data }">
        <Text>{{ getMapLable(commonTypeList, data) }}</Text>
      </template>
      <template #isMovePicture="{ data }">
        <Text>{{ getMapLable(isMovePictureList, data) }}</Text>
      </template>
      <template #operation="{ rowData }">
        <Button
          size="small"
          style="background-color:#3366FF;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
          @click="jsonDetail(rowData)"
        >查看</Button>
        <Button
          size="small"
          style="background-color:#999966;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          type="primary"
          @click="handleeditMaterial(rowData)"
        >编辑</Button>
        <Popconfirm
          title="删除"
          description="确认删除该条素材吗？"
          @confirm="handleDelConfirm(rowData)"
          @cancel="handleMaterialClose"
        >
          <Button
            size="small"
            style="background-color:#f03860;color:white;margin:-4px;padding-left:5px;padding-right:5px;"
          >删除</Button>
        </Popconfirm>
      </template>

    </Table>
    <!-- </Spinner> -->
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      :page-size-options="pageSizeOptions"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <Modal
    v-model:visible="showNewJobModal"
    title="任务素材集"
    :with-footer="false"
    :style="{ width: 'auto' }"
  >
    <Spinner
      :spinning="loading"
      tip="请稍等..."
    >
      <Form
        v-model="createForm"
        :style="{ marginTop: '20px' }"
        size="default"
        @submit="handleSubmit"
      >
        <FormItem
          name="setName"
          label="任务素材集名称"
        >
          <Input required />
        </FormItem>
        <FormItem
          name="setType"
          label="素材集类型"
        >
          <Select
            :options="setTypeList"
            required
          />
        </FormItem>
        <FormItem
          name="sceneType"
          label="场景分类"
        >
          <RadioGroup
            :options="state.sceneTypeOptions"
            required
          />
        </FormItem>
        <FormItem
          name="setEnv"
          label="环境"
        >
          <RadioGroup
            :options="[{ label: 'sit', value: 'sit', checked: true }, { label: 'prod', value: 'prod' }]"
            required
          />
        </FormItem>
        <FormItem
          name="hasVideoId"
          label="是否生成视频ID"
        >
          <RadioGroup
            :options="[{ label: '是', value: '1', checked: true }, { label: '否', value: '0' }]"
            required
          />
        </FormItem>
        <FormItem
          name="setContent"
          label="任务素材集详情"
        >
          <TextArea />
        </FormItem>
        <FormItem
          v-if="state.csvchecked === true"
          name="setSource"
          label="任务来源"
        >
          <!-- <Text required>{{state.csvURL}}</Text> -->
          <TextArea
            disabled
            :model-value="state.csvURL"
            required
          />
        </FormItem>

      </Form>
      <!-- <Text>当前选中项：{{  createForm }}</Text> -->
    </Spinner>
  </Modal>

  <Modal
    v-model:visible="showInsertJobModal"
    title="任务素材集"
    :with-footer="false"
    :style="{ width: 'auto' }"
  >
    <Spinner
      :spinning="loading"
      tip="请稍等..."
    >
      <Form
        v-model="insertForm"
        :style="{ marginTop: '20px' }"
        size="default"
        @submit="handleInsert"
      >
        <FormItem
          name="setName"
          label="任务素材集名称"
        >
          <Select
            type="secondary"
            :options="insertFormNameList"
            filterable
            clearable
            required
          />
        </FormItem>
      </Form>
      <!-- <Text>当前选中项：{{  createForm }}</Text> -->
    </Spinner>
  </Modal>

  <Modal
    v-model:visible="showNewMaterials"
    :with-footer="false"
    :style="{ width: 'auto' }"
    @cancel="handleMClose"
  >
    <template #title>
      <p :style="{ fontSize: '16px', fontFamily: '黑体', fontWeight: 'bold', marginRight: '30px' }">
        {{ state.isMEdit == 1 ? '编辑素材' : '添加素材' }}
      </p>
      <Button
        type="secondary"
        @click="getAttribute"
      >获取元数据</Button>
      <p :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', }"> 可先填写必填内容后，点击获取元数据，自动填充属性</p>
    </template>
    <Form2
      ref="formRef"
      :label-position="labelPosition"
      :model="materialsModel"
      :rules="rules"
      label-width="100px"
    >
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="素材分类"
          name="sourceType"
          :hide-required-mark="true"
        >
          <!-- 44444 -->
          <Select
            v-model="materialsModel.sourceType"
            :style="{ width: '90px' }"
            :options="sourcetypeList"
            required
          />
        </FormItem2>
        <FormItem2
          label="场景分类"
          name="sceneType"
          :hide-optional-text="true"
        >
          <Select
            v-model="materialsModel.sceneType"
            :style="{ width: '90px' }"
            :options="[{ label: '无', value: '无' }, { label: '海外', value: '海外' }]"
            required
          />
        </FormItem2>
        <FormItem2
          name="isMusic"
          label="是否纯音频"
        >
          <Select
            v-model="materialsModel.isMusic"
            :style="{ width: '90px' }"
            :options="[{ label: '是', value: 1 }, { label: '否', value: 0 }]"
            required
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="filekey"
          name="filekey"
          :hide-optional-text="true"
          :hide-required-mark="false"
        >
          <Input v-model="materialsModel.filekey" />
        </FormItem2>
        <FormItem2
          label="fileurl"
          name="fileurl"
          :hide-optional-text="true"
          :hide-required-mark="false"
        >
          <Input v-model="materialsModel.fileurl" />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="存储类型"
          name="cloud"
        >
          <Select
            v-model="materialsModel.cloud"
            :style="{ width: '90px' }"
            :options="state.cloudList"
            required
          />
        </FormItem2>
        <FormItem2
          label="bucket"
          name="bucket"
        >
          <Input v-model="materialsModel.bucket" />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          v-if="state.activeItem === '1'"
          label="视频编码"
          name="videoCodec"
        >
          <Input
            v-model="materialsModel.videoCodec"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '3'"
          label="图片编码"
          name="videoCodec"
        >
          <Input
            v-model="materialsModel.videoCodec"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          label="宽高比"
          name="videoDAR"
        >
          <Input
            v-model="materialsModel.videoDAR"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          label="width"
          name="videoWidth"
        >
          <Input
            v-model="materialsModel.videoWidth"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          label="height"
          name="videoHeight"
        >
          <Input
            v-model="materialsModel.videoHeight"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          label="pix_fmt"
          name="pix_fmt"
        >
          <Input
            v-model="materialsModel.pix_fmt"
            :style="{ width: '90px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          v-if="state.activeItem === '1'"
          label="isHdr"
          name="isHdr"
        >
          <Input
            v-model="materialsModel.isHdr"
            :style="{ width: '90px', marginRight: '30px' }"
          />
        </FormItem2>
        <FormItem2
          label="color_space"
          name="color_space"
        >
          <Input
            v-model="materialsModel.color_space"
            :style="{ width: '90px', marginRight: '30px' }"
          />
        </FormItem2>
        <FormItem2
          label="color_transfer"
          name="colorTransfer"
        >
          <Input
            v-model="materialsModel.colorTransfer"
            :style="{ width: '90px', marginRight: '50px' }"
          />
        </FormItem2>
        <FormItem2
          label="color_primaries"
          name="colorPrim"
        >
          <Input
            v-model="materialsModel.colorPrim"
            :style="{ width: '90px', marginRight: '30px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="color_range"
          name="color_range"
        >
          <Input
            v-model="materialsModel.color_range"
            :style="{ width: '90px', marginRight: '30px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          v-if="state.activeItem === '1'"
          label="r_frame_rate"
          name="r_frame_rate"
        >
          <Input
            v-model="materialsModel.r_frame_rate"
            :style="{ width: '90px', marginRight: '40px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="avg_frame_rate"
          name="videoFps"
        >
          <Input
            v-model="materialsModel.videoFps"
            :style="{ width: '90px', marginRight: '10px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="fps_type"
          name="fps_type"
        >
          <Input
            v-model="materialsModel.fps_type"
            :style="{ width: '90px', marginRight: '80px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '3'"
          :style="{ width: '0px', marginRight: '50px' }"
        />
        <FormItem2
          label="bits_per_raw_sample"
          name="bits_per_raw_sample"
        >
          <Input
            v-model="materialsModel.bits_per_raw_sample"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="profile"
          name="profile"
        >
          <Input
            v-model="materialsModel.profile"
            :style="{ width: '90px', marginRight: '10px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="rotation"
          name="rotation"
        >
          <Input
            v-model="materialsModel.rotation"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="level"
          name="level"
        >
          <Input
            v-model="materialsModel.level"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="refs"
          name="refs"
        >
          <Input
            v-model="materialsModel.refs"
            :style="{ width: '90px', marginRight: '40px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '1'"
          label="video_duration"
          name="duration"
        >
          <Input
            v-model="materialsModel.duration"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          v-if="state.activeItem === '3'"
          label="file_size"
          name="duration"
        >
          <Input
            v-model="materialsModel.duration"
            :style="{ width: '110px' }"
          />
        </FormItem2>
        <FormItem2
          label="详情"
          name="videodetails"
        >
          <Input
            v-model="materialsModel.videodetails"
            :style="{ width: '180px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', alignItems: 'center' }"
      >
        <FormItem2
          label="音频编码"
          name="audioCodec"
        >
          <Input
            v-model="materialsModel.audioCodec"
            :style="{ width: '90px', marginRight: '10px' }"
          />
        </FormItem2>
        <FormItem2
          label="sample_rate"
          name="audioSampleRate"
        >
          <Input
            v-model="materialsModel.audioSampleRate"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          label="channels"
          name="audiochannel"
        >
          <Input
            v-model="materialsModel.audiochannel"
            :style="{ width: '90px', marginRight: '30px' }"
          />
        </FormItem2>
        <FormItem2
          label="audio_duration"
          name="audio_duration"
        >
          <Input
            v-model="materialsModel.audio_duration"
            :style="{ width: '100px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox
        v-if="state.activeItem === '1'"
        :bs="{ display: 'flex', alignItems: 'center' }"
      >
        <FormItem2
          label="avg_si"
          name="avg_si"
        >
          <Input
            v-model="materialsModel.avg_si"
            :style="{ width: '90px', marginRight: '10px' }"
          />
        </FormItem2>
        <FormItem2
          label="avg_ti"
          name="avg_ti"
        >
          <Input
            v-model="materialsModel.avg_ti"
            :style="{ width: '90px', marginRight: '30px' }"
          />
        </FormItem2>
        <FormItem2
          label="video_bit_rate"
          name="video_bit_rate"
        >
          <Input
            v-model="materialsModel.video_bit_rate"
            :style="{ width: '100px', marginRight: '30px' }"
          />
        </FormItem2>
        <FormItem2
          label="audio_bit_rate"
          name="audio_bit_rate"
        >
          <Input
            v-model="materialsModel.audio_bit_rate"
            :style="{ width: '100px', marginRight: '10px' }"
          />
        </FormItem2>
        <FormItem2
          label="不良类型"
          name="badType"
        >
          <Select
            v-model="materialsModel.badType"
            :options="state.badTypeList"
            allow-create
            clearable
            filterable
            :style="{ width: '150px' }"
          />
        </FormItem2>
      </NewBsBox>
      <!-- <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="视频bit"
          name="videoDynamic"
        >
          <Input
            v-model="materialsModel.videoDynamic"
            :style="{ width: '90px' }"
          />
        </FormItem2>
        <FormItem2
          label="封装类型"
          name="packageType"
        >
          <Input
            v-model="materialsModel.packageType"
            :style="{ width: '90px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="亮度"
          name="lightType"
        >
          <Select
            v-model="materialsModel.lightType"
            :style="{ width: '90px' }"
            :options="commonTypeList"
            required
          />
        </FormItem2>
        <FormItem2
          label="饱和度"
          name="colorType"
        >
          <Select
            v-model="materialsModel.colorType"
            :style="{ width: '90px' }"
            :options="colorTypeList"
            required
          />
        </FormItem2>
        <FormItem2
          label="空间复杂度"
          name="spaceType"
        >
          <Select
            v-model="materialsModel.spaceType"
            :style="{ width: '90px' }"
            :options="commonTypeList"
            required
          />
        </FormItem2>
        <FormItem2
          label="运动复杂度"
          name="travelType"
        >
          <Select
            v-model="materialsModel.travelType"
            :style="{ width: '90px' }"
            :options="commonTypeList"
            required
          />
        </FormItem2>
        <FormItem2
          label="对比度"
          name="contrastType"
        >
          <Select
            v-model="materialsModel.contrastType"
            :style="{ width: '90px' }"
            :options="commonTypeList"
            required
          />
        </FormItem2>
        <FormItem2
          label="isHdr"
          name="isHdr"
        >
          <Input
            v-model="materialsModel.isHdr"
            :style="{ width: '90px' }"
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          label="图片编码"
          name="picCodec"
        >
          <Input v-model="materialsModel.picCodec" />
        </FormItem2>
        <FormItem2
          label="是否是动图"
          name="isMovePicture"
        >
          <Select
            v-model="materialsModel.isMovePicture"
            :style="{ width: '90px' }"
            :options="isMovePictureList"
            required
          />
        </FormItem2>
        <FormItem2
          label="响应均衡算法"
          name="isVolume"
        >
          <Select
            v-model="materialsModel.isVolume"
            :style="{ width: '90px' }"
            :options="isVolumeList"
            required
          />
        </FormItem2>
      </NewBsBox> -->
      <FormItem2 label=" ">
        <Space>
          <Button
            type="primary"
            @click="submitMaterials"
          >创建</Button>
          <Button
            v-if="state.isMEdit == 0"
            @click="reset"
          >重置</Button>
          <Button
            v-if="state.isMEdit == 1"
            @click="editMClose"
          >取消</Button>
        </Space>
      </FormItem2>
    </Form2>
  </Modal>

  <Modal
    v-model:visible="showLocalMaterials"
    :with-footer="false"
    :style="{ width: 'auto' }"
    @cancel="handleLocalClose"
  >
    <template #title>
      <p :style="{ fontSize: '16px', fontFamily: '黑体', fontWeight: 'bold', marginRight: '30px' }">
        本地上传
      </p>
      <p :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
        上传至腾讯云COS，桶为note-video-qc-1251524319
      </p>
    </template>

    <Form2
      ref="formRef"
      label-width="100px"
    >
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2 :bs="{ display: 'flex', width: '100%' }">
          <Text
            bold
            :style="{ marginRight: '10px' }"
          >详情:</Text>
          <Input v-model="localFile.details" />
        </FormItem2>
      </NewBsBox>

      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2 :bs="{ display: 'flex', width: '100%' }">
          <Upload
            v-model="videoList"
            drag
            multiple
            :custom-request="videoCustomRequest"
            :style="{ display: 'block', margin: 'auto' }"
          >
            <template #files="{ files }">
              <!-- 为每个文件显示文件名和独立进度条 -->
              <div
                v-for="(file) in files"
                :key="file.uid"
              >
                <Space
                  v-if="file.name"
                  block
                  :style="{ marginTop: '10px' }"
                >
                  <div style="width: 100%">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px">
                      <Text>{{ file.name }}</Text>
                      <Text>{{ file.percent || 0 }}%</Text>
                    </div>
                  </div>
                </Space>
              </div>
            </template>
          </Upload>
        </FormItem2>
      </NewBsBox>

      <NewBsBox
        v-if="videoList.length > 0"
        :bs="{ display: 'flex', alignItems: 'center' }"
      >
        <FormItem2 label="">
          <Space>
            <Button
              type="primary"
              @click="submitLocalMaterials"
            >
              <Text style="color: white;">添加</Text>
            </Button>
          </Space>
        </FormItem2>
      </NewBsBox>
    </Form2>
  </Modal>

  <Modal
    v-model:visible="showMultiMaterials"
    :with-footer="false"
    :style="{ width: 'auto' }"
    @cancel="handleMultiClose"
  >
    <template #title>
      <p :style="{ fontSize: '16px', fontFamily: '黑体', fontWeight: 'bold', marginRight: '30px' }">
        批量添加
      </p>
      <p :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
        请确保上传数据表格包含以下字段：bucket，filekey，fileurl，details
      </p>
    </template>
    <Form2
      ref="formRef"
      label-width="100px"
    >
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          name="sceneType"
          label="场景"
        >
          <Select
            v-model="excelData.sceneType"
            :options="[{ label: '海外', value: '海外' }, { label: '无', value: '无' }]"
            :style="{ width: '100px', marginLeft: '10px', marginRight: '10px' }"
            required
          />
        </FormItem2>
        <FormItem2
          name="sourceType"
          label="素材分类"
        >
          <Select
            v-model="excelData.sourceType"
            :options="sourcetypeList"
            :style="{ width: '100px', marginLeft: '10px', marginRight: '10px' }"
            required
          />
        </FormItem2>
        <FormItem2
          name="isMusic"
          label="是否纯音频"
        >
          <Select
            v-model="excelData.isMusic"
            :style="{ width: '90px' }"
            :options="[{ label: '是', value: 1 }, { label: '否', value: 0 }]"
            required
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <FormItem2
          name="cloud"
          label="cloud"
        >
          <Select
            v-model="excelData.cloud"
            :options="state.cloudList"
            :style="{ width: '100px', marginLeft: '10px' }"
            required
          />
        </FormItem2>
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', width: '100%', justifyContent: 'center' }">
        <FormItem2 :bs="{ display: 'flex', width: '100%' }">
          <Upload
            v-model="materialFile"
            drag
            required
            accept=".xlsx"
            :custom-request="uploadCustomRequest"
            :style="{ display: 'block', margin: 'auto' }"
            :limit="1"
            :show-upload-list-clearer="false"
          >
            <template #files="{ files }">
              <Space
                v-for="(file) in files"
                :key="file.uid"
                block
              >
                <Text>{{ file.name }}</Text>
              </Space>
            </template>
          </Upload>
        </FormItem2>
      </NewBsBox>
      <NewBsBox
        v-if="excelData.data.length > 0"
        :bs="{ display: 'flex', width: '100%', justifyContent: 'center' }"
      >
        <div>
          <Text :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
            总计：{{ excelData.data.length }}条数据
          </Text>
          <br>
          <Text :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
            蓝色：{{ excelData.cnt_running }}条数据正在运行
          </Text>
          <br>
          <Text :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
            红色：{{ excelData.cnt_error }}条数据获取元数据失败
          </Text>
          <br>
          <Text :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
            黄色：{{ excelData.cnt_ready }}条数据获取元数据成功，上传失败
          </Text>
          <br>
          <Text :style="{ fontSize: '12px', fontFamily: 'Arial, sans-serif', marginRight: '30px' }">
            绿色：{{ excelData.cnt_success }}条数据上传成功
          </Text>
        </div>
      </NewBsBox>
      <NewBsBox
        v-if="excelData.data.length > 0"
        :bs="{ display: 'flex', width: '100%', justifyContent: 'center' }"
      >
        <div :style="{ maxHeight: '240px', overflowY: 'auto' }">
          <table :style="{ 'table-layout': 'fixed', width: '100%' }">
            <thead>
              <tr>
                <th
                  v-for="column in requiredColumns"
                  :key="column.dataIndex"
                  :style="{ width: `${column.width}px` }"
                >
                  {{ column.title }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(row, index) in excelData.data"
                :key="index"
              >
                <td
                  v-for="column in requiredColumns"
                  :key="column.dataIndex"
                  :style="{ width: `${column.width}px`, ...getRowStyle(row) }"
                >
                  <input
                    v-model="row[column.dataIndex]"
                    class="editable-cell"
                    :type="'text'"
                    :disabled="row.state === 'success' || row.state === 'ready'"
                  >
                </td>
              </tr>
            </tbody>
          </table>
        </div>

      </NewBsBox>

      <FormItem2 label="">
        <Space>
          <Button
            type="primary"
            @click="submitMultiMaterials"
          >
            <Text
              v-if="excelError.length == 0"
              style="color: white;"
            >添加</Text>
            <Text
              v-if="excelError.length > 0"
              style="color: white;"
            >重试</Text>
          </Button>
          <Button @click="removeUploadedFile">重置</Button>
          <Button
            v-if="excelError.length > 0"
            type="danger"
            @click="exportExcelError"
          >导出失败数据</Button>
        </Space>
      </FormItem2>
    </Form2>
  </Modal>

  <Modal
    v-model:visible="showjsonDataModal"
    :mask-closeable="maskCloseable"
    title="数据"
    :with-footer="false"
    :style="{ width: '800px', height: '800px' }"
  >
    <json-viewer
      :value="state.jsonData"
      copyable
    />
  </Modal>

  <Modal
    v-model:visible="infoVisible"
    type="danger"
    title="错误"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      请先勾选原子素材集！
    </Text>
  </Modal>

  <Modal
    v-model:visible="showMultiDelCheck"
    type="info"
    title="提醒"
    @confirm="handleMultiDelConfirm"
    @cancel="handleMultiDelClose"
  >
    <Text>
      确认删除这些素材吗？
    </Text>
  </Modal>

</template>

<script setup lang="tsx">
  /* eslint-disable no-await-in-loop */
  import COS from 'cos-js-sdk-v5'
  import { reactive, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Space, Table, Text, Button, Select, Input, toast, RadioGroup, Radio, Icon, Form, FormItem, Tooltip, Pagination, Tag, Checkbox, Modal, TextArea,
    Form2, FormItem2, Popconfirm, toast2, Upload, Spinner,
  } from '@xhs/delight'
  import * as XLSX from 'xlsx'
  import { ButtonGroup, NewBsBox } from '@xhs/yam-beer'
  import {
    Add, Search, Clear, Help,
  } from '@xhs/delight/icons'
  import JsonViewer from 'vue-json-viewer'
  import { useStore } from 'vuex'
  import {
    getCosKey, insertVideoInfoToVA, getAvsetinit, getAvsetData, avsetCreateTask, insertMaterial, deleteMaterial, editMaterial, getMaterialInfo, getTaskSet, avsetInsertTask,
  } from '../../services/material'
  import {
    commonTypeList, audioSampleRateList, colorTypeList, getMapLable, setTypeList, isMovePictureList, pageSizeOptions,
  } from '../../utils/common'

  // import {videoDynamicList} from '../../utils/const'

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'
  const router = useRouter()
  // const route = useRoute()

  const sourcetypeList = [
    { name: '视频', value: '1' },
    // { name: '音频', value: '2' },
    { name: '图片', value: '3' },
  ]

  const requiredColumns = [
    // { title: '素材分类', dataIndex: 'sourceType', width: 100 },
    { title: 'bucket', dataIndex: 'bucket', width: 100 },
    { title: 'filekey', dataIndex: 'filekey', width: 100 },
    { title: 'fileurl', dataIndex: 'fileurl', width: 100 },
    { title: '详情', dataIndex: 'details', width: 100 },
    // { title: 'isHdr', dataIndex: 'isHdr', width: 100 },
  ]
  const selectedColumns = [
    {
      title: 'isHdr',
      dataIndex: 'isHdr',
      width: 100,
      default: 0,
    },
  ]

  const stateColor = {
    default: '#ffffff',
    success: '#eefaf0',
    error: '#ffeded',
    ready: '#fff8e6',
    running: '#e6f7ff',
  }

  const insertFormNameList = ref([])

  const videoList = ref([])
  const localFile = ref({
    details: '',
  })

  const materialFile = ref([])
  const excelData = ref({
    sceneType: '海外',
    cloud: 'cos',
    sourceType: '1',
    isMusic: 0,
    data: [],
    cnt_success: 0,
    cnt_error: 0,
    cnt_ready: 0,
    cnt_running: 0,
  })
  const excelError = ref([])
  const showLocalMaterials = ref(false)
  const showMultiMaterials = ref(false)
  const showInsertJobModal = ref(false)
  const showMultiDelCheck = ref(false)
  const insertForm = ref({ setName: '' })

  const loading = ref(false)
  const showNewJobModal = ref(false)
  const showNewMaterials = ref(false)
  const showjsonDataModal = ref(false)
  const maskCloseable = ref(false)
  const createForm = ref({
    setName: '',
    setContent: '',
    setSource: '',
    setEnv: '',
    hasVideoId: '',
    sceneType: '',
  })

  const infoVisible = ref(false)

  const labelPosition = ref('right')
  const state = reactive({
    isMEdit: 0,
    editID: -1,
    activeItem: sourcetypeList[0].value,
    sceneTypeSelect: '',
    sceneTypeList: [],
    videoFps: '',
    fps_typeSelect: '',
    fps_typecList: [],
    videoCodecSelect: '',
    videoCodecList: [],
    audioCodecSelect: '',
    audioCodecList: [],
    picCodecSelect: '',
    picCodecList: [],
    packageTypeSelect: '',
    packageTypeList: [],
    badTypeSelect: '',
    badTypeList: [],
    colorPrimSelect: '',
    colorPrimList: [],
    colorTransferSelect: '',
    colorTransferList: [],
    pix_fmtSelect: '',
    pix_fmtList: [],
    videoDynamicSelect: '',
    audioSampleRateSelect: '',
    lightTypeSelect: '',
    isMovePicture: '',
    colorTypeSelect: '',
    spaceTypeSelect: '',
    travelTypeSelect: '',
    contrastTypeSelect: '',
    selectedfilekeys: [],
    audiochannel: '',
    isMusic: '-1',
    isHdr: '',
    isVolumeSelect: '',
    videoDAR: '',
    videoWidth: '',
    videoHeight: '',
    videodetails: '',
    duration: '',

    color_spaceSelect: '',
    color_spaceList: [],
    color_rangeSelect: '',
    color_rangeList: [],
    bits_per_raw_sample: '',
    profile: '',
    level: '',
    refs: '',
    audio_duration: '',
    avg_si: '',
    avg_ti: '',
    video_bit_rate: '',
    audio_bit_rate: '',

    r_frame_rate: '',
    rotationSelect: '',
    rotationList: [],

    pageNum: 1,
    pageSize: 10,
    selectedList: [],
    hasNotOverseas: false,
    sceneTypeOptions: [],
    dataSource: [],
    dataLength: 0,
    videodetailsSelect: '',
    videodetailsList: [],
    cloudSelect: '',
    cloudList: [],
    csvchecked: false,
    csvURL: '',
    jsonData: {},
    filekey: '',

  })

  // 1111111
  const materialsModel = reactive({
    sourceType: '',
    sceneType: '海外',
    isMusic: 0,
    filekey: '',
    fileurl: '',
    videoDAR: '',
    videoWidth: '',
    videoHeight: '',
    videoFps: '',
    videodetails: '',
    colorPrim: '',
    colorTransfer: '',
    videoCodec: '',
    audioCodec: '',
    picCodec: '',
    pix_fmt: '',
    videoDynamic: '',
    audioSampleRate: '',
    packageType: '',
    badType: '',
    lightType: '',
    colorType: '',
    spaceType: '',
    travelType: '',
    contrastType: '',
    isVolume: '',
    audiochannel: '',
    duration: '',
    isHdr: '',
    cloud: '',
    bucket: '',
    isMovePicture: '',
    color_space: '',
    color_range: '',
    bits_per_raw_sample: '',
    profile: '',
    level: '',
    refs: '',
    audio_duration: '',
    r_frame_rate: '',
    fps_type: '',
    rotation: '',
    avg_si: '',
    avg_ti: '',
    video_bit_rate: '',
    audio_bit_rate: '',
    video_id: '',
  })

  const formRef = ref()

  // 2222
  // bucket字段校验函数
  const validateBucket = (value) => {
    if (!value) {
      return { status: 'error', message: 'bucket is required' }
    }
    // 只能包含数字(0-9)、字母(a-z, A-Z)、连字符(-)
    const bucketRegex = /^[a-zA-Z0-9-]+$/
    if (!bucketRegex.test(value)) {
      return { status: 'error', message: 'bucket格式错误：只能包含数字(0-9)、字母(a-z, A-Z)、连字符(-)' }
    }
    return { status: 'success' }
  }

  const rules = {
    sourceType: { required: true, message: 'sourceType is required', trigger: 'blur' },
    filekey: { required: true, message: 'filekey is required', trigger: 'blur' },
    fileurl: { required: true, message: 'fileurl is required', trigger: 'blur' },
    cloud: { required: true, message: 'cloud is required', trigger: 'blur' },
    bucket: {
      required: true,
      validator: (value) => validateBucket(value),
      trigger: 'blur'
    },
  }

  const reset = () => {
    formRef.value.resetFields()
  }
  const initMaterials = () => {
    materialsModel.sourceType = ''
    materialsModel.sceneType = '海外'
    materialsModel.isMusic = 0
    materialsModel.filekey = ''
    materialsModel.fileurl = ''
    materialsModel.videoDAR = ''
    materialsModel.videoWidth = ''
    materialsModel.videoHeight = ''
    materialsModel.videoFps = ''
    materialsModel.videodetails = ''
    materialsModel.colorPrim = ''
    materialsModel.colorTransfer = ''
    materialsModel.videoCodec = ''
    materialsModel.audioCodec = ''
    materialsModel.packageType = ''
    materialsModel.picCodec = ''
    materialsModel.pix_fmt = ''
    materialsModel.videoDynamic = ''
    materialsModel.audioSampleRate = ''
    materialsModel.badType = ''
    materialsModel.lightType = ''
    materialsModel.colorType = ''
    materialsModel.spaceType = ''
    materialsModel.travelType = ''
    materialsModel.contrastType = ''
    materialsModel.isVolume = ''
    materialsModel.audiochannel = ''
    materialsModel.duration = ''
    materialsModel.isHdr = ''
    materialsModel.cloud = ''
    materialsModel.bucket = ''
    materialsModel.isMovePicture = ''
    materialsModel.color_space = ''
    materialsModel.color_range = ''
    materialsModel.bits_per_raw_sample = ''
    materialsModel.profile = ''
    materialsModel.level = ''
    materialsModel.refs = ''
    materialsModel.audio_duration = ''
    materialsModel.r_frame_rate = ''
    materialsModel.fps_type = ''
    materialsModel.rotation = ''
    materialsModel.avg_si = ''
    materialsModel.avg_ti = ''
    materialsModel.video_bit_rate = ''
    materialsModel.audio_bit_rate = ''
  }
  const editMClose = () => {
    showNewMaterials.value = false
    state.isMEdit = 0
    initMaterials()
  }

  const rowSelection = {
    getCheckboxProps: v => ({
      selectable: v.id !== 0,
    }),
    // onSelect: v => console.log(v),
  }

  const videocolumns = [
    {
      title: 'id',
      dataIndex: 'id',
      minWidth: 50,
    },
    {
      title: '场景分类',
      dataIndex: 'sceneType',
      minWidth: 80,
    },
    {
      title: '是否纯音频',
      dataIndex: 'isMusic',
      minWidth: 100,
    },
    {
      title: 'cloud',
      dataIndex: 'cloud',
      minWidth: 70,
    },

    {
      title: 'filekey',
      dataIndex: 'filekey',
      minWidth: 70,
    },
    {
      title: 'fileurl',
      dataIndex: 'fileurl',
      minWidth: 70,
    },
    {
      title: '详情',
      dataIndex: 'videodetails',
      minWidth: 60,
    },
    {
      title: '不良类型',
      dataIndex: 'badType',
      minWidth: 55,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      minWidth: 80,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      minWidth: 80,
    },
    {
      title: '视频编码',
      dataIndex: 'videoCodec',
      minWidth: 80,
    },
    {
      title: 'isHdr',
      dataIndex: 'isHdr',
      minWidth: 80,
    },
    {
      title: '宽高比',
      dataIndex: 'videoDAR',
      minWidth: 70,
    },
    {
      title: 'width',
      dataIndex: 'videoWidth',
      minWidth: 70,
    },
    {
      title: 'height',
      dataIndex: 'videoHeight',
      minWidth: 70,
    },
    {
      title: 'r_frame_rate',
      dataIndex: 'r_frame_rate',
      minWidth: 110,
    },
    {
      title: 'avg_frame_rate',
      dataIndex: 'videoFps',
      minWidth: 130,
    },
    {
      title: 'fps_type',
      dataIndex: 'fps_type',
      minWidth: 90,
    },
    {
      title: 'pix_fmt',
      dataIndex: 'pix_fmt',
      minWidth: 80,
    },
    {
      title: 'color_space',
      dataIndex: 'color_space',
      minWidth: 110,
    },
    {
      title: 'color_transfer',
      dataIndex: 'colorTransfer',
      minWidth: 120,
    },
    {
      title: 'color_primaries',
      dataIndex: 'colorPrim',
      minWidth: 130,
    },
    {
      title: 'color_range',
      dataIndex: 'color_range',
      minWidth: 110,
    },
    {
      title: 'bits_per_raw_sample',
      dataIndex: 'bits_per_raw_sample',
      minWidth: 170,
    },
    {
      title: 'profile',
      dataIndex: 'profile',
      minWidth: 70,
    },
    {
      title: 'rotation',
      dataIndex: 'rotation',
      minWidth: 90,
    },
    {
      title: 'level',
      dataIndex: 'level',
      minWidth: 60,
    },
    {
      title: 'refs',
      dataIndex: 'refs',
      minWidth: 60,
    },
    {
      title: 'video_duration',
      dataIndex: 'duration',
      minWidth: 130,
    },
    {
      title: '音频编码',
      dataIndex: 'audioCodec',
      minWidth: 80,
    },
    {
      title: 'sample_rate',
      dataIndex: 'audioSampleRate',
      minWidth: 120,
    },
    {
      title: 'channels',
      dataIndex: 'audiochannel',
      minWidth: 90,
    },
    {
      title: 'audio_duration',
      dataIndex: 'audio_duration',
      minWidth: 130,
    },
    {
      title: 'avg_si',
      dataIndex: 'avg_si',
      minWidth: 70,
    },
    {
      title: 'avg_ti',
      dataIndex: 'avg_ti',
      minWidth: 70,
    },
    {
      title: 'video_bit_rate',
      dataIndex: 'video_bit_rate',
      minWidth: 130,
    },
    {
      title: 'audio_bit_rate',
      dataIndex: 'audio_bit_rate',
      minWidth: 130,
    },
    // {
    //   title: '封装类型',
    //   dataIndex: 'packageType',
    //   minWidth: 80,
    // },
    // {
    //   title: 'Hdr',
    //   dataIndex: 'isHdr',
    //   minWidth: 60,
    // },
    // {
    //   title: '图片编码',
    //   dataIndex: 'picCodec',
    //   minWidth: 80,
    // },
    // {
    //   title: '视频bit',
    //   dataIndex: 'videoDynamic',
    //   minWidth: 80,
    // },
    // {
    //   title: '亮度',
    //   dataIndex: 'lightType',

    // },
    // {
    //   title: '饱和度',
    //   dataIndex: 'colorType',
    //   // width: 55,
    // },
    // {
    //   title: '空间复杂度',
    //   dataIndex: 'spaceType',
    //   // width: 70,
    // },
    // {
    //   title: '运动复杂度',
    //   dataIndex: 'travelType',
    //   // width: 70,
    // },
    // {
    //   title: '对比度',
    //   dataIndex: 'contrastType',
    //   // width: 55,
    // },
    // {
    //   title: 'isVolume',
    //   dataIndex: 'isVolume',
    //   minWidth: 90,
    // },
    // {
    //   title: 'MovePicture',
    //   dataIndex: 'isMovePicture',
    //   minWidth: 110,
    // },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },
  ]

  const picturecolumns = [
    {
      title: 'id',
      dataIndex: 'id',
      minWidth: 50,
    },
    {
      title: '场景分类',
      dataIndex: 'sceneType',
      minWidth: 80,
    },
    {
      title: '是否纯音频',
      dataIndex: 'isMusic',
      minWidth: 100,
    },
    {
      title: 'cloud',
      dataIndex: 'cloud',
      minWidth: 70,
    },

    {
      title: 'filekey',
      dataIndex: 'filekey',
      minWidth: 70,
    },
    {
      title: 'fileurl',
      dataIndex: 'fileurl',
      minWidth: 70,
    },
    {
      title: '详情',
      dataIndex: 'videodetails',
      minWidth: 60,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      minWidth: 80,
    },
    {
      title: '图片编码',
      dataIndex: 'videoCodec',
      minWidth: 80,
    },
    {
      title: '宽高比',
      dataIndex: 'videoDAR',
      minWidth: 70,
    },
    {
      title: 'width',
      dataIndex: 'videoWidth',
      minWidth: 70,
    },
    {
      title: 'height',
      dataIndex: 'videoHeight',
      minWidth: 70,
    },
    {
      title: 'pix_fmt',
      dataIndex: 'pix_fmt',
      minWidth: 80,
    },
    {
      title: 'color_space',
      dataIndex: 'color_space',
      minWidth: 110,
    },
    {
      title: 'color_transfer',
      dataIndex: 'colorTransfer',
      minWidth: 120,
    },
    {
      title: 'color_primaries',
      dataIndex: 'colorPrim',
      minWidth: 130,
    },
    {
      title: 'rotation',
      dataIndex: 'rotation',
      minWidth: 90,
    },
    {
      title: 'bits_per_raw_sample',
      dataIndex: 'bits_per_raw_sample',
      minWidth: 170,
    },
    {
      title: 'file_size',
      dataIndex: 'duration',
      minWidth: 130,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },
  ]

  // const selected = ref([])

  // const rowSelection = {
  //   getCheckboxProps: v => ({
  //     checked: v.key === 'Nike Air Max 90 Premium',
  //     disabled: v.key === 'Nike Air Max 90',
  //     selectable: v.key !== 'Nike Air Force 1 Mid 07 WB',
  //   }),
  //   onSelected: v => console.log(v),
  // }
  function notempty(param) {
    const arr = []
    param.forEach(val => {
      // 过滤规则为，不为空串、不为null、不为undefined，也可自行修改
      if (val !== '' && val !== undefined) {
        arr.push(val)
      }
    })
    return arr
  }

  function searchAvset() {
    loading.value = true

    const queryparam = {
      streamType: 0,
    }
    queryparam.sourceType = state.activeItem
    if (state.sceneTypeSelect) queryparam.sceneType = state.sceneTypeSelect
    if (state.packageTypeSelect) queryparam.packageType = state.packageTypeSelect
    // if (state.colorPrimSelect) queryparam.colorPrim = state.colorPrimSelect
    // if (state.colorTransferSelect) queryparam.colorTransfer = state.colorTransferSelect
    if (state.videoDynamicSelect) queryparam.videoDynamic = state.videoDynamicSelect

    // videoCodecSelect
    if (state.videoCodecSelect.length !== 0) {
      const tmpArr = notempty(state.videoCodecSelect)
      state.videoCodecSelect = tmpArr
      queryparam.videoCodec = state.videoCodecSelect
    }
    if (state.rotationSelect.length !== 0) {
      const tmpArr = notempty(state.rotationSelect)
      state.rotationSelect = tmpArr
      queryparam.rotation = state.rotationSelect
    }
    // pix_fmtSelect
    // if (state.pix_fmtSelect.length !== 0) {
    //   const tmpArr = notempty(state.pix_fmtSelect)
    //   state.pix_fmtSelect = tmpArr
    //   queryparam.pix_fmt = state.pix_fmtSelect
    // }

    if (state.videoDAR !== '') queryparam.videoDAR = state.videoDAR
    if (state.videoWidth !== '') queryparam.videoWidth = state.videoWidth
    if (state.videoHeight !== '') queryparam.videoHeight = state.videoHeight
    if (state.duration !== '') queryparam.duration = state.duration
    if (state.videodetails !== '') queryparam.videodetails = state.videodetails
    if (state.videoFps !== '') queryparam.videoFps = state.videoFps
    if (state.r_frame_rate !== '') queryparam.r_frame_rate = state.r_frame_rate

    if (state.isMusic !== '-1') {
      queryparam.isMusic = state.isMusic
    } else {
      delete queryparam.isMusic
    }
    if (state.isHdr) {
      queryparam.isHdr = state.isHdr
    } else {
      delete queryparam.isHdr
    }

    if (state.fps_typeSelect.length !== 0) {
      const tmpArr = notempty(state.fps_typeSelect)
      state.fps_typeSelect = tmpArr
      queryparam.fps_type = state.fps_typeSelect
    }
    if (state.colorPrimSelect.length !== 0) {
      const tmpArr = notempty(state.colorPrimSelect)
      state.colorPrimSelect = tmpArr
      queryparam.colorPrim = state.colorPrimSelect
    }
    if (state.colorTransferSelect.length !== 0) {
      const tmpArr = notempty(state.colorTransferSelect)
      state.colorTransferSelect = tmpArr
      queryparam.colorTransfer = state.colorTransferSelect
    }

    if (state.lightTypeSelect) queryparam.lightType = state.lightTypeSelect
    if (state.colorTypeSelect) queryparam.colorType = state.colorTypeSelect
    if (state.spaceTypeSelect) queryparam.spaceType = state.spaceTypeSelect
    if (state.travelTypeSelect) queryparam.travelType = state.travelTypeSelect
    if (state.isMovePicture) queryparam.isMovePicture = state.isMovePicture

    // audioCodecSelect
    if (state.audioCodecSelect.length !== 0) {
      const tmpArr = notempty(state.audioCodecSelect)
      state.audioCodecSelect = tmpArr
      queryparam.audioCodec = state.audioCodecSelect
    }

    if (state.audioSampleRateSelect) queryparam.audioSampleRate = state.audioSampleRateSelect
    // audiochannel
    if (state.audiochannel !== '') queryparam.audiochannel = state.audiochannel

    if (state.contrastTypeSelect) queryparam.contrastType = state.contrastTypeSelect
    if (state.badTypeSelect) queryparam.badType = state.badTypeSelect
    if (state.isVolumeSelect) queryparam.isVolume = state.isVolumeSelect
    if (state.videodetailsSelect) queryparam.videodetails = state.videodetailsSelect
    if (state.cloudSelect) queryparam.cloud = state.cloudSelect

    if (state.filekey) queryparam.filekey = state.filekey

    if (state.pix_fmtSelect) queryparam.pix_fmt = state.pix_fmtSelect
    if (state.color_spaceSelect) queryparam.color_space = state.color_spaceSelect
    if (state.color_rangeSelect) queryparam.color_range = state.color_rangeSelect
    if (state.bits_per_raw_sample) queryparam.bits_per_raw_sample = state.bits_per_raw_sample
    if (state.profile) queryparam.profile = state.profile
    if (state.level) queryparam.level = state.level
    if (state.refs) queryparam.refs = state.refs
    if (state.audio_duration) queryparam.audio_duration = state.audio_duration
    if (state.avg_si) queryparam.avg_si = state.avg_si
    if (state.avg_ti) queryparam.avg_ti = state.avg_ti
    if (state.video_bit_rate) queryparam.video_bit_rate = state.video_bit_rate
    if (state.audio_bit_rate) queryparam.audio_bit_rate = state.audio_bit_rate

    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }
    // console.log('ninghuanjun-request--', payload)

    state.dataSource = []
    getAvsetData(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger('无数据！请更改查询条件。')
        } else {
          state.dataLength = res.dataLength
          state.dataSource = res.data
          state.csvURL = res.csv
          if (res.dataLength !== 0) {
            state.dataSource.map(item => {
              item.key = item.id
              return item
            })
          }
        }
      })
    state.pageNum = 1
  }

  function avsetinit() {
    state.sceneTypeList = []
    state.packageTypeList = []
    state.badTypeList = []
    state.fps_typecList = []
    state.videoCodecList = []
    state.audioCodecList = []
    state.picCodecList = []
    state.pix_fmtList = []
    state.colorTransferList = []
    state.colorPrimList = []
    state.videodetailsList = []
    state.cloudList = []
    state.rotationList = []
    state.sceneTypeOptions = [
      { label: '无', value: '无', checked: true },
      { label: '海外', value: '海外' },
    ]

    const request = {
      sourceType: state.activeItem ? state.activeItem : 0,
    }

    const payload = {
      request,
    }

    getAvsetinit(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.sceneTypeList = res.data.sceneType
          state.packageTypeList = res.data.packageType
          state.badTypeList = res.data.badType
          state.fps_typecList = res.data.fps_type
          state.videoCodecList = res.data.videoCodec
          state.audioCodecList = res.data.audioCodec
          state.picCodecList = res.data.picCodec
          state.pix_fmtList = res.data.pix_fmt
          state.colorTransferList = res.data.colorTransfer
          state.colorPrimList = res.data.colorPrim
          state.videodetailsList = res.data.videodetails
          state.cloudList = res.data.cloud
          state.color_spaceList = res.data.color_space
          state.color_rangeList = res.data.color_range
          state.rotationList = res.data.rotation.map(String)
        }
      })
  }

  const avsetQuery = () => {
    avsetinit()
    searchAvset()
  }
  function clearParam() {
    state.sceneTypeSelect = ''
    state.videoFps = ''
    state.r_frame_rate = ''
    state.fps_typeSelect = ''
    state.videoCodecSelect = ''
    state.audioCodecSelect = ''
    state.picCodecSelect = ''
    state.packageTypeSelect = ''
    state.badTypeSelect = ''
    state.colorPrimSelect = ''
    state.colorTransferSelect = ''
    state.pix_fmtSelect = ''
    state.videoDynamicSelect = ''
    state.audioSampleRateSelect = ''
    state.lightTypeSelect = ''
    state.isMovePicture = ''
    state.colorTypeSelect = ''
    state.spaceTypeSelect = ''
    state.travelTypeSelect = ''
    state.contrastTypeSelect = ''
    state.audiochannel = ''
    state.isMusic = '-1'
    state.isHdr = ''
    state.isVolumeSelect = ''
    state.videoDAR = ''
    state.videodetailsSelect = ''
    state.cloudSelect = ''
    state.videoWidth = ''
    state.videoHeight = ''
    state.videodetails = ''
    state.duration = ''
    state.filekey = ''
    state.color_spaceSelect = ''
    state.color_rangeSelect = ''
    state.bits_per_raw_sample = ''
    state.profile = ''
    state.level = ''
    state.refs = ''
    state.audio_duration = ''
    state.rotationSelect = ''
    state.avg_si = ''
    state.avg_ti = ''
    state.video_bit_rate = ''
    state.audio_bit_rate = ''
    avsetQuery()
  }

  function changeSourcetype() {
    clearParam()
    avsetQuery()
  }

  // const test = () => {
  //   getMapLable(state.lightType,1)
  // }

  function jsonDetail(data: any) {
    showjsonDataModal.value = true
    state.jsonData = data
    maskCloseable.value = true
    // state.jsonData = {"data":{"status":1,"result":[{"key":"b","value":{"status":1,"media":{"type":1,"file_id":"capa_ai/main_object_seg/8a2446b8-7889-4c40-adc9-a3fe5b2bce230.jpg","url":"https://ci.xiaohongshu.com/capa_ai/main_object_seg/8a2446b8-7889-4c40-adc9-a3fe5b2bce230.jpg"},"feature":{"int32_array":[0,0,1382,1080]}}}]},"code":0,"success":true,"msg":"成功"}
  }

  function handleeditMaterial(data: any) {
    state.isMEdit = 1
    showNewMaterials.value = true
    state.editID = data.id
    materialsModel.sourceType = data.sourceType.toString()
    materialsModel.sceneType = data.sceneType
    materialsModel.isMusic = data.isMusic
    materialsModel.filekey = data.filekey
    materialsModel.fileurl = data.fileurl
    materialsModel.cloud = data.cloud
    materialsModel.bucket = data.bucket
    materialsModel.videoDAR = data.videoDAR
    materialsModel.videoWidth = data.videoWidth
    materialsModel.videoHeight = data.videoHeight
    materialsModel.videoFps = data.videoFps
    materialsModel.r_frame_rate = data.r_frame_rate
    materialsModel.fps_type = data.fps_type
    materialsModel.videodetails = data.videodetails
    materialsModel.colorPrim = data.colorPrim
    materialsModel.colorTransfer = data.colorTransfer
    materialsModel.videoCodec = data.videoCodec
    materialsModel.audioCodec = data.audioCodec
    materialsModel.picCodec = data.picCodec
    materialsModel.pix_fmt = data.pix_fmt
    materialsModel.videoDynamic = data.videoDynamic
    materialsModel.audioSampleRate = data.audioSampleRate
    materialsModel.packageType = data.packageType
    materialsModel.badType = data.badType
    materialsModel.lightType = data.lightType.toString()
    materialsModel.colorType = data.colorType.toString()
    materialsModel.spaceType = String(data.spaceType)
    materialsModel.travelType = String(data.travelType)
    materialsModel.contrastType = String(data.contrastType)
    materialsModel.isVolume = data.isVolume
    materialsModel.audiochannel = data.audiochannel
    materialsModel.duration = data.duration
    materialsModel.isHdr = data.isHdr
    materialsModel.isMovePicture = String(data.isMovePicture)
    materialsModel.color_space = String(data.color_space)
    materialsModel.color_range = String(data.color_range)
    materialsModel.bits_per_raw_sample = String(data.bits_per_raw_sample)
    materialsModel.profile = String(data.profile)
    materialsModel.level = String(data.level)
    materialsModel.refs = String(data.refs)
    materialsModel.audio_duration = String(data.audio_duration)
    materialsModel.rotation = String(data.rotation)
    materialsModel.avg_si = data.avg_si
    materialsModel.avg_ti = data.avg_ti
    materialsModel.video_bit_rate = String(data.video_bit_rate)
    materialsModel.audio_bit_rate = String(data.audio_bit_rate)
  }

  function deleteMultiMaterial() {
    // console.log('delete')
    if ((state.csvchecked === false && state.selectedList.length === 0) || (state.csvchecked === true && state.csvURL === '')) {
      infoVisible.value = true
    } else {
      showMultiDelCheck.value = true
      infoVisible.value = false
    }
  }

  function handleMultiDelClose() {
    // console.log('close')
    showMultiDelCheck.value = false
  }

  async function handleMultiDelConfirm() {
    handleMultiDelClose()

    // 创建一个包含所有删除操作的数组
    const promises = state.selectedList.map(data => {
      const payload = {
        request: {
          id: data,
        },
      }
      return deleteMaterial(payload)
        .then(res => {
          if (res.status !== 0) {
            toast.danger(`删除失败，id: ${data}`)
          } else {
            toast.success(`删除成功，id: ${data}`)
          }
        })
        .catch(() => {
          toast.danger(`删除失败，id: ${data}`)
        })
    })

    // 等待所有删除请求完成
    await Promise.all(promises)

    // 所有删除请求完成后再执行搜索
    avsetQuery()
  }

  function handleDelConfirm(data: any) {
    const payload = {
      request: {
        id: data.id,
      },
    }
    deleteMaterial(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          avsetQuery()
        }
      })
  }
  function handleMaterialClose() {
    // console.log('删除')
  }

  function copyUrl(data: any) {
    const cInput = document.createElement('input')
    cInput.value = data
    document.body.appendChild(cInput)
    cInput.select() // 选取文本框内容
    document.execCommand('copy')
    toast.success('复制成功')
  }

  function handlePagination() {
    avsetQuery()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    avsetQuery()
  }

  function handleSelectedChange(v) {
    if (v) {
      state.csvchecked = false
    }
  }

  function handleSelectedAll() {
    // console.log('全选', v)
    // console.log('selected ', state.selectedList)
  }

  const changeChecked = () => {
    if (state.csvchecked === true) {
      state.selectedList = []
    }
  }

  function handleClose() {
    infoVisible.value = false
  }

  function handleMClose() {
    state.isMEdit = 0
    showNewMaterials.value = false
    initMaterials()
  }

  function removeUploadedFile() {
    materialFile.value = []
    excelError.value = []
    excelData.value = {
      sceneType: '海外',
      sourceType: '1',
      isMusic: 0,
      cloud: 'cos',
      data: [],
      cnt_success: 0,
      cnt_error: 0,
      cnt_ready: 0,
      cnt_running: 0,
    }
  }

  function uploadCustomRequest({ file, onSuccess }) {
    const fileReader = new FileReader()
    fileReader.onload = e => {
      const buffer = new Uint8Array(e.target.result)
      const workbook = XLSX.read(buffer, { type: 'array' })
      const workSheetName = workbook.SheetNames[0]
      const workSheet = workbook.Sheets[workSheetName]
      const jsonData = XLSX.utils.sheet_to_json(workSheet, { header: 1 })

      // 检查所需列是否存在
      const headers = jsonData[0] // 假设第一行为表头
      const requiredHeaders = requiredColumns.map(c => c.dataIndex)
      const missingColumns = requiredHeaders.filter(c => !headers.includes(c))

      if (missingColumns.length) {
        // 如果存在缺少的列，则报错
        toast.danger(`请检查文件是否包含以下数据: ${missingColumns.join(',')}`)
        removeUploadedFile()
      } else {
        // 定义起始行和结束行（注意行数是基于0的索引）
        const startRow = 1
        // const endRow = 1300
        // 提取和映射所需的数据列
        const data = jsonData.slice(startRow).map(item => {
          const obj = {
            state: 'default',
          }
          requiredColumns.forEach(requiredInfo => {
            const idx = headers.indexOf(requiredInfo.dataIndex)
            obj[requiredInfo.dataIndex] = item[idx] ? item[idx] : ''
          })
          // 如果可选的列不存在，则默认值
          selectedColumns.forEach(selectedInfo => {
            if (headers.includes(selectedInfo.dataIndex)) {
              const idx = headers.indexOf(selectedInfo.dataIndex)
              obj[selectedInfo.dataIndex] = item[idx] ? item[idx] : selectedInfo.default
            } else {
              obj[selectedInfo.dataIndex] = selectedInfo.default
            }
          })
          return obj
        }).filter(obj => {
          // 检查除 'state' 之外的所有属性是否至少有一个非空
          const res = Object.keys(obj).some(key => key !== 'state' && obj[key] !== '')
          return res
        })
        // 将提取的数据存储到响应式数据中
        excelData.value.data = data
        // console.log("上传完成")
        // console.log(excelData.value)

        // 可以将成功读取的标识传递给 onSuccess，或传递读取的数据
        onSuccess(data)
      }
    }

    fileReader.onerror = () => {
      toast.danger('文件读取失败！')
      // console.error('File reading error', err)
    }

    fileReader.readAsArrayBuffer(file)

    // 创建文件预览 URL
    materialFile.value = URL.createObjectURL(file)
  }

  // function submitLocalMaterials() {
  // console.log('submitLocalMaterials', localFile.value.details)

  // // 创建上传任务数组
  // const uploadTasks = videoList.value.map(file => {
  //   return uploadVideo({
  //     file: file.raw,
  //   })
  // })
  // // 使用Promise.all等待所有上传完成
  // Promise.all(uploadTasks)
  //   .then(res => {
  //     if (res.every(item => item.status === 0)) {
  //       toast.success('视频上传成功')
  //       // 清空上传列表或关闭对话框
  //       videoList.value = []
  //       showLocalMaterials.value = false
  //     } else {
  //       toast.danger('视频上传失败，请重试')
  //     }
  //   })
  //   .catch(err => {
  //     console.error('视频上传失败:', err)
  //     toast.danger('视频上传失败，请重试')
  //   })
  // }

  async function submitLocalMaterials() {
    try {
      // Get COS credentials
      const res = await getCosKey({})
      // console.log('cos key: ', res)

      const cos = new COS({
        SecretId: res.data.secret_id,
        SecretKey: res.data.secret_key,
        StartTime: res.data.StartTime,
        ExpiredTime: res.data.ExpiredTime,
        // 配置CORS相关选项
        Domain: `${res.data.bucket}.cos.${res.data.region}.myqcloud.com`,
        ForcePathStyle: false,
        // 设置正确的协议
        Protocol: window.location.protocol.slice(0, -1), // 去掉末尾的冒号
      })

      const prefix = 'vatest/'

      // Create upload promises for all files
      const uploadPromises = videoList.value.map(async file => {
        console.log('file: ', file)

        // 确保文件对象有效
        const fileToUpload = file.raw || file
        if (!fileToUpload) {
          console.error('Invalid file object:', file)
          throw new Error('Invalid file object')
        }

        // 生成唯一的文件名以避免冲突
        const timestamp = Date.now()
        const randomStr = Math.random().toString(36).substring(2, 8)
        const fileName = file.name || 'unknown_file'
        const fileExtension = fileName.split('.').pop()
        const uniqueFileName = `${fileName.split('.')[0]}_${timestamp}_${randomStr}.${fileExtension}`

        try {
          // Upload file to COS
          await cos.uploadFile({
            Bucket: res.data.bucket,
            Region: res.data.region,
            Key: prefix + uniqueFileName,
            Body: fileToUpload,
            SliceSize: 1024 * 1024 * 5, // 5MB chunk size for multipart uploads
            onProgress: progressData => {
              // 更新当前文件的进度属性，触发UI更新
              file.percent = Math.floor((progressData.loaded / progressData.total) * 100)
            },
          })

          // 更新文件名用于后续获取URL
          file.uniqueName = uniqueFileName
        } catch (error) {
          file.percent = 100
          console.error(`Error uploading file ${fileName}:`, error)
          throw error
        }
        // Get URL for the uploaded file
        return new Promise((resolve, reject) => {
          const finalFileName = file.uniqueName || file.name
          cos.getObjectUrl({
            Bucket: res.data.bucket,
            Region: res.data.region,
            Key: prefix + finalFileName,
          }, (err, data) => {
            if (err) {
              console.error('Failed to get object URL:', err)
              return reject(err)
            }

            const downloadUrl = `${data.Url}${data.Url.indexOf('?') > -1 ? '&' : '?'}response-content-disposition=attachment`

            return resolve({
              sourceType: 1,
              cloud: 'cos',
              bucket: res.data.bucket,
              filekey: prefix + finalFileName,
              fileurl: downloadUrl,
              details: localFile.value.details,
            })
          })
        })
      })

      // Wait for all uploads to complete
      const videosInfo = await Promise.all(uploadPromises)

      const payload = {
        request: {
          videosInfo,
        },
      }

      insertVideoInfoToVA(payload).then(response => {
        if (response.status !== 0) {
          toast.danger('视频上传VA失败，请重试')
        } else {
          toast.success('视频上传VA成功')
          localFile.value.details = ''
          videoList.value = []
          showLocalMaterials.value = false
        }
      })
    } catch (error) {
      toast.danger('视频上传失败，请重试')
    }
  }

  function videoCustomRequest({ file }) {
    // console.log('videoCustomRequest', file)
    file.percent = 0
    // 移除手动push操作，因为v-model已经处理了文件添加
    console.log('videoList.value', videoList.value)
    // onSuccess()
    // onProgress()
    // onError()
  }

  function addLocalMaterial() {
    showLocalMaterials.value = true
    videoList.value = []
  }

  function handleLocalClose() {
    showLocalMaterials.value = false
  }

  function addMultiMaterial() {
    showMultiMaterials.value = true
  }

  function handleMultiClose() {
    showMultiMaterials.value = false
    removeUploadedFile()
  }

  function calcExcelCnt(cur, tar) {
    // 减少当前状态计数
    if (cur === 'success') {
      excelData.value.cnt_success -= 1
    } else if (cur === 'error') {
      excelData.value.cnt_error -= 1
    } else if (cur === 'ready') {
      excelData.value.cnt_ready -= 1
    } else if (cur === 'running') {
      excelData.value.cnt_running -= 1
    }

    // 增加目标状态计数
    if (tar === 'success') {
      excelData.value.cnt_success += 1
    } else if (tar === 'error') { // 这里应该是 'tar'
      excelData.value.cnt_error += 1
    } else if (tar === 'running') { // 这里应该是 'tar'
      excelData.value.cnt_running += 1
    } else if (tar === 'ready') { // 这里应该是 'tar'
      excelData.value.cnt_ready += 1
    }

    return tar
  }

  function getRowStyle(row) {
    return { backgroundColor: stateColor[row.state] }
  }

  function exportExcelError() {
    // 定义 Excel 数据
    const data = excelError.value.map(item => ({
      bucket: item.bucket,
      filekey: item.filekey,
      fileurl: item.fileurl,
      details: item.details,
      isHdr: item.isHdr,
      // 添加更多列根据需要
    }))

    // 创建工作簿和工作表
    const worksheet = XLSX.utils.json_to_sheet(data)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Error Data')

    // 生成 Excel 文件的二进制数据
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })

    // 创建 Blob 对象
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'excel_errors.xlsx'
    document.body.appendChild(a)
    a.click()

    // 移除链接
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  // 批量上传
  async function submitMultiMaterials() {
    if (excelData.value.sourceType === 0) {
      toast.danger('请选择素材类型！')
      return
    }
    if (excelData.value.cloud === '') {
      toast.danger('请选择cloud！')
      return
    }
    if (excelData.value.data.length === 0) {
      toast.danger('请上传文件！')
      return
    }

    const batchSize = 100
    excelError.value = []

    async function processItem(item) {
      if (item.state !== 'success') {
        item.state = calcExcelCnt(item.state, 'running')
        // 初始化素材数据
        const material = {
          sceneType: excelData.value.sceneType,
          sourceType: (excelData.value.sourceType === 1 && excelData.value.isMusic === 1) ? 2 : excelData.value.sourceType,
          filekey: item.filekey,
          fileurl: item.fileurl,
          cloud: excelData.value.cloud,
          bucket: item.bucket,
          isMusic: excelData.value.isMusic,
          isHdr: item.isHdr,
        }

        const request = {
          sourceType: material.sourceType,
          filekey: material.filekey,
          fileurl: material.fileurl,
          cloud: material.cloud,
          bucket: material.bucket,
        }
        const payload = { request }

        try {
          let res = await getMaterialInfo(payload)
          if (res.status !== 0) {
            item.state = calcExcelCnt(item.state, 'error')
            excelError.value.push(item)
          } else {
            // 赋值
            material.audioCodec = res.data.audioCodec
            material.audioSampleRate = res.data.audioSampleRate
            material.audiochannel = res.data.audiochannel
            material.colorPrim = res.data.colorPrim
            material.colorTransfer = res.data.colorTransfer
            material.duration = res.data.duration
            material.packageType = res.data.packageType
            material.pix_fmt = res.data.pix_fmt
            material.videoCodec = res.data.videoCodec
            material.videoDAR = res.data.videoDAR
            material.videoFps = res.data.videoFps
            material.videoHeight = res.data.videoHeight
            material.videoWidth = res.data.videoWidth
            material.color_space = res.data.color_space
            material.color_range = res.data.color_range
            material.bits_per_raw_sample = res.data.bits_per_raw_sample
            material.profile = res.data.profile
            material.level = res.data.level
            material.refs = res.data.refs
            material.audio_duration = res.data.audio_duration
            material.r_frame_rate = res.data.r_frame_rate
            material.fps_type = res.data.fps_type
            material.rotation = res.data.rotation
            // material.avg_si = res.data.avg_si
            // material.avg_ti = res.data.avg_ti
            material.video_bit_rate = res.data.video_bit_rate
            material.audio_bit_rate = res.data.audio_bit_rate

            const uploadRequest = {
              sourceType: material.sourceType,
              sceneType: material.sceneType ? material.sceneType : '海外',
              isMusic: material.isMusic ? material.isMusic : 0,
              filekey: material.filekey,
              fileurl: material.fileurl,
              cloud: material.cloud,
              bucket: material.bucket,
              videoDAR: material.videoDAR ? material.videoDAR : 0,
              videoWidth: material.videoWidth ? material.videoWidth : 0,
              videoHeight: material.videoHeight ? material.videoHeight : 0,
              videoFps: material.videoFps ? material.videoFps : 0,
              videodetails: item.details ? item.details : 'unknown',
              colorPrim: material.colorPrim ? material.colorPrim : 'unknown',
              colorTransfer: material.colorTransfer ? material.colorTransfer : 'unknown',
              videoCodec: material.videoCodec ? material.videoCodec : 'unknown',
              audioCodec: material.audioCodec ? material.audioCodec : 'unknown',
              picCodec: material.picCodec ? material.picCodec : 'unknown',
              pix_fmt: material.pix_fmt ? material.pix_fmt : 'unknown',
              videoDynamic: material.videoDynamic ? material.videoDynamic : 0,
              audioSampleRate: material.audioSampleRate ? material.audioSampleRate : 0,
              packageType: material.packageType ? material.packageType : 'unknown',
              badType: material.badType ? material.badType : 'unknown',
              lightType: material.lightType ? material.lightType : 0,
              colorType: material.colorType ? material.colorType : 0,
              spaceType: material.spaceType ? material.spaceType : 0,
              travelType: material.travelType ? material.travelType : 0,
              contrastType: material.contrastType ? material.contrastType : 0,
              isVolume: material.isVolume ? material.isVolume : 'unknown',
              audiochannel: material.audiochannel ? material.audiochannel : 0,
              duration: material.duration ? material.duration : 0,
              isHdr: material.isHdr ? material.isHdr : 0,
              isMovePicture: material.isMovePicture ? material.isMovePicture : 0,
              color_space: material.color_space ? material.color_space : 'unknown',
              color_range: material.color_range ? material.color_range : 'unknown',
              bits_per_raw_sample: material.bits_per_raw_sample ? material.bits_per_raw_sample : 0,
              profile: material.profile ? material.profile : 'unknown',
              level: material.level ? material.level : 0,
              refs: material.refs ? material.refs : 0,
              audio_duration: material.audio_duration ? material.audio_duration : 0,
              r_frame_rate: material.r_frame_rate ? material.r_frame_rate : 0,
              fps_type: material.fps_type ? material.fps_type : 'unknown',
              rotation: material.rotation ? material.rotation : 0,
              avg_si: 0,
              avg_ti: 0,
              video_bit_rate: material.video_bit_rate ? material.video_bit_rate : 0,
              audio_bit_rate: material.audio_bit_rate ? material.audio_bit_rate : 0,
            }

            const uploadPayload = { request: uploadRequest }

            try {
              await formRef.value.validate()
              res = await insertMaterial(uploadPayload)
              // console.log('res: ', res)
              if (res.status !== 0) {
                item.state = calcExcelCnt(item.state, 'ready')
              } else {
                item.state = calcExcelCnt(item.state, 'success')
              }
            } catch (e) {
              item.state = calcExcelCnt(item.state, 'ready')
            }
          }
        } catch (e) {
          item.state = calcExcelCnt(item.state, 'error')
          excelError.value.push(item)
        }
      }
    }

    for (let i = 0; i < excelData.value.data.length; i += batchSize) {
      const batch = excelData.value.data.slice(i, i + batchSize)
      await Promise.all(batch.map(item => processItem(item)))
    }

    if (excelData.value.data.every(item => item.state === 'success')) {
      toast.success('批量添加成功！')
      handleMultiClose()
      initMaterials()
      avsetQuery()
    }
  }

  // 3333
  function getAttribute() {
    if (materialsModel.fileurl === '' || materialsModel.filekey === '' || materialsModel.cloud === '' || materialsModel.bucket === '') {
      toast2.danger({
        content: '请先输入必填字段！',
        strong: true,
      })
    } else {
      const request = {
        sourceType: (materialsModel.sourceType === '1' && materialsModel.isMusic === 1) ? '2' : materialsModel.sourceType,
        filekey: materialsModel.filekey,
        fileurl: materialsModel.fileurl,
        cloud: materialsModel.cloud,
        bucket: materialsModel.bucket,
      }
      const payload = {
        request,
      }
      getMaterialInfo(payload).then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          // 赋值
          materialsModel.audioCodec = res.data.audioCodec
          materialsModel.audioSampleRate = res.data.audioSampleRate
          materialsModel.audiochannel = res.data.audiochannel
          materialsModel.colorPrim = res.data.colorPrim
          materialsModel.colorTransfer = res.data.colorTransfer
          materialsModel.duration = res.data.duration
          materialsModel.packageType = res.data.packageType
          materialsModel.pix_fmt = res.data.pix_fmt
          materialsModel.videoCodec = res.data.videoCodec
          materialsModel.videoDAR = res.data.videoDAR
          materialsModel.videoFps = res.data.videoFps
          materialsModel.videoHeight = res.data.videoHeight
          materialsModel.videoWidth = res.data.videoWidth
          materialsModel.color_space = res.data.color_space
          materialsModel.color_range = res.data.color_range
          materialsModel.bits_per_raw_sample = res.data.bits_per_raw_sample
          materialsModel.profile = res.data.profile
          materialsModel.level = res.data.level
          materialsModel.refs = res.data.refs
          materialsModel.audio_duration = res.data.audio_duration
          materialsModel.r_frame_rate = res.data.r_frame_rate
          materialsModel.fps_type = res.data.fps_type
          materialsModel.rotation = res.data.rotation
          materialsModel.video_bit_rate = res.data.video_bit_rate
          materialsModel.audio_bit_rate = res.data.audio_bit_rate
        }
      })
    }
  }

  const handleAddJobset = () => {
    // console.log('bbb--state.csvchecked----state.selectedList.length----state.csvURL-----infoVisible', state.csvchecked, state.selectedList.length, state.csvURL, infoVisible.value)
    if ((state.csvchecked === false && state.selectedList.length === 0) || (state.csvchecked === true && state.csvURL === '')) {
      infoVisible.value = true
    } else {
      showNewJobModal.value = true
      infoVisible.value = false
      state.hasNotOverseas = state.selectedList.some(id => state.dataSource.some(info => info.id === id && info.sceneType !== '海外'))

      if (state.hasNotOverseas) {
        state.sceneTypeOptions = [
          { label: '无', value: '无', checked: true },
        ]
      } else {
        state.sceneTypeOptions = [
          { label: '无', value: '无', checked: true },
          { label: '海外', value: '海外' },
        ]
      }
    }
  }

  const handleInsertJobset = () => {
    if ((state.csvchecked === false && state.selectedList.length === 0) || (state.csvchecked === true && state.csvURL === '')) {
      infoVisible.value = true
    } else {
      showInsertJobModal.value = true
      infoVisible.value = false
      loading.value = true

      state.hasNotOverseas = state.selectedList.some(id => state.dataSource.some(info => info.id === id && info.sceneType !== '海外'))

      const queryparam = {}
      const payload = {
        request: {
          pageNum: 1,
          pageSize: 300,
          queryparam,
        },
      }
      insertFormNameList.value = []
      getTaskSet(payload)
        .then(res => {
          loading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            console.log(res.data)
            res.data.forEach(data => {
              if ((state.hasNotOverseas === true && data.sceneType !== '海外') || state.hasNotOverseas === false) {
                insertFormNameList.value.push(data.setName)
              }
            })
          }
        })
        .catch(() => {
          toast.danger('任务列表请求失败！')
        })
    }
  }

  const AddMaterials = () => {
    showNewMaterials.value = true
  }

  // 单次上传
  const submitMaterials = () => {
    const request = {
      sourceType: materialsModel.sourceType,
      sceneType: materialsModel.sceneType ? materialsModel.sceneType : '海外',
      isMusic: materialsModel.isMusic,
      filekey: materialsModel.filekey,
      fileurl: materialsModel.fileurl,
      cloud: materialsModel.cloud,
      bucket: materialsModel.bucket,
      videoDAR: materialsModel.videoDAR ? materialsModel.videoDAR : 0,
      videoWidth: materialsModel.videoWidth ? materialsModel.videoWidth : 0,
      videoHeight: materialsModel.videoHeight ? materialsModel.videoHeight : 0,
      videoFps: materialsModel.videoFps ? materialsModel.videoFps : 0,
      videodetails: materialsModel.videodetails ? materialsModel.videodetails : 'unknown',
      colorPrim: materialsModel.colorPrim ? materialsModel.colorPrim : 'unknown',
      colorTransfer: materialsModel.colorTransfer ? materialsModel.colorTransfer : 'unknown',
      videoCodec: materialsModel.videoCodec ? materialsModel.videoCodec : 'unknown',
      audioCodec: materialsModel.audioCodec ? materialsModel.audioCodec : 'unknown',
      picCodec: materialsModel.picCodec ? materialsModel.picCodec : 'unknown',
      pix_fmt: materialsModel.pix_fmt ? materialsModel.pix_fmt : 'unknown',
      videoDynamic: materialsModel.videoDynamic ? materialsModel.videoDynamic : 0,
      audioSampleRate: materialsModel.audioSampleRate ? materialsModel.audioSampleRate : 0,
      packageType: materialsModel.packageType ? materialsModel.packageType : 'unknown',
      badType: materialsModel.badType ? materialsModel.badType : 'unknown',
      lightType: materialsModel.lightType ? materialsModel.lightType : 0,
      colorType: materialsModel.colorType ? materialsModel.colorType : 0,
      spaceType: materialsModel.spaceType ? materialsModel.spaceType : 0,
      travelType: materialsModel.travelType ? materialsModel.travelType : 0,
      contrastType: materialsModel.contrastType ? materialsModel.contrastType : 0,
      isVolume: materialsModel.isVolume ? materialsModel.isVolume : 'unknown',
      audiochannel: materialsModel.audiochannel ? materialsModel.audiochannel : 0,
      duration: materialsModel.duration ? materialsModel.duration : 0,
      isHdr: materialsModel.isHdr ? materialsModel.isHdr : 0,
      isMovePicture: materialsModel.isMovePicture ? materialsModel.isMovePicture : 0,
      color_space: materialsModel.color_space ? materialsModel.color_space : 'unknown',
      color_range: materialsModel.color_range ? materialsModel.color_range : 'unknown',
      bits_per_raw_sample: materialsModel.bits_per_raw_sample ? materialsModel.bits_per_raw_sample : 0,
      profile: materialsModel.profile ? materialsModel.profile : 'unknown',
      level: materialsModel.level ? materialsModel.level : 0,
      refs: materialsModel.refs ? materialsModel.refs : 0,
      audio_duration: materialsModel.audio_duration ? materialsModel.audio_duration : 0,
      r_frame_rate: materialsModel.r_frame_rate ? materialsModel.r_frame_rate : 0,
      fps_type: materialsModel.fps_type ? materialsModel.fps_type : 'unknown',
      rotation: materialsModel.rotation ? materialsModel.rotation : 0,
      avg_si: materialsModel.avg_si ? materialsModel.avg_si : 0,
      avg_ti: materialsModel.avg_ti ? materialsModel.avg_ti : 0,
      video_bit_rate: materialsModel.video_bit_rate ? materialsModel.video_bit_rate : 0,
      audio_bit_rate: materialsModel.audio_bit_rate ? materialsModel.audio_bit_rate : 0,
    }

    if (state.isMEdit === 0) {
      const payload = {
        request,
      }

      // 新增
      formRef.value.validate()
        .then(() => {
          insertMaterial(payload).then(res => {
            if (res.status !== 0) {
              toast.danger(res.message)
            } else {
              showNewMaterials.value = false
              toast.success(res.message)
              avsetQuery()
            }
          })
        })
        .catch(() => {
          // console.log(e, 'error')
        })
    } else {
      // 编辑33333
      request.id = state.editID
      const payload = {
        request,
      }
      formRef.value.validate()
        .then(() => {
          editMaterial(payload).then(res => {
            if (res.status !== 0) {
              toast.danger(res.message)
            } else {
              showNewMaterials.value = false
              toast.success(res.message)
              state.isMEdit = 0
              initMaterials()
              avsetQuery()
            }
          })
        })
        .catch(() => {
          // console.log(e, 'error')
        })
    }
  }

  const handleSubmit = () => {
    const payload = {
      request: {
        userName,
        setName: createForm.value.setName,
        setContent: createForm.value.setContent ? createForm.value.setContent : '',
        setType: createForm.value.setType,
        setEnv: createForm.value.setEnv,
        hasVideoId: createForm.value.hasVideoId,
        sourceType: state.activeItem,
        sceneType: createForm.value.sceneType,
      },
    }
    if (state.csvchecked === true && state.csvURL !== '') payload.request.csv = state.csvURL
    if (state.csvchecked === false && state.selectedList.length !== 0) {
      payload.request.setIdList = state.selectedList
      // console.log(payload.request.setIdList)
    } else {
      toast.danger('请选择原子素材后，再创建！')
      return
    }

    // console.log('ninghuanjun', payload)
    loading.value = true

    avsetCreateTask(payload)
      .then(res => {
        createForm.value = {
          setName: '',
          setContent: '',
          setSource: '',
          setEnv: '',
          hasVideoId: '',
          sceneType: '',
        }
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          showNewJobModal.value = false
          loading.value = false
          // 跳转到任务页面
          router.push({
            name: 'AudioVideoTask',
            params: {},
          })
        }
      })
  }

  const handleInsert = () => {
    const payload = {
      request: {
        setName: insertForm.value.setName,
      },
    }
    if (state.csvchecked === true && state.csvURL !== '') payload.request.csv = state.csvURL
    if (state.csvchecked === false && state.selectedList.length !== 0) {
      payload.request.setIdList = state.selectedList
    } else {
      toast.danger('请选择原子素材后，再添加！')
      return
    }

    loading.value = true

    avsetInsertTask(payload)
      .then(res => {
        insertForm.value = { setName: '' }
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          showInsertJobModal.value = false
          loading.value = false
          // 跳转到任务页面
          router.push({
            name: 'AudioVideoTask',
            params: {},
          })
        }
      })
      .catch(() => {
        toast.danger('插入素材请求失败！')
      })
  }

  onMounted(avsetQuery)

</script>
<style>
.custom-form>div {
  display: flex;
  align-items: center;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  color: #4e4e4e;
  background: #f1f1f1;
  border-bottom: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

td {
  color: #4e4e4e;
  border-bottom: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

.cell-content {
  overflow: hidden;
  /* 确保内容不会溢出容器 */
  white-space: nowrap;
  /* 防止内容换行 */
  text-overflow: ellipsis;
  /* 当内容过长时，展示省略号 */
  max-width: 100%;
  /* 限制内容的最大宽度不超过单元格宽度 */
}

/* 使输入框的样式像普通单元格 */
.editable-cell {
  border: none;
  background-color: transparent;
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
}

.editable-cell:disabled {
  cursor: not-allowed;
  /* 禁用状态的鼠标样式 */
}

/* 移除焦点时的输入框边框 */
.editable-cell:focus {
  outline: none;
}
</style>
