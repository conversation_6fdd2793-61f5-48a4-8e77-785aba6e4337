<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >

    <Text>请选择账号类型：</Text>
    <RadioGroup
      v-model="value"
      :options="options"
      name="form"
    />
    <Text>直播结算到账：{{ value }}</Text>
  </Space>
</template>

<script setup lang="ts">
  import {
    Space, Text, RadioGroup,
  } from '@xhs/delight'
  import { ref } from 'vue'

  const value = ref('')

  const options = [
    {
      value: '普通号直播结束后48h结算',
      label: '普通号',
      revertable: true,
    },
    {
      value: '企业号按月打款',
      label: '企业号',
      revertable: true,
    },
  ]
</script>
