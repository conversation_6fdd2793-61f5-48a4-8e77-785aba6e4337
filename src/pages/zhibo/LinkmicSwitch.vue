<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >

    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:10px"
      >主播id：</Text>
      <Input
        v-model="state.hostId"
        :style="{ width: '90px' }"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="query()"
      >查询</Button>
    </NewBsBox>

    <div v-if="state.hostSwitch == true"><Text>允许主播发起连麦开关：开启</Text></div>
    <div v-if="state.hostSwitch == false"><Text>允许主播发起连麦开关：关闭</Text></div>
    <div v-if="state.viewerSwitch == true"><Text>允许观众发起连麦开关：开启</Text></div>
    <div v-if="state.viewerSwitch == false"><Text>允许观众发起连麦开关：关闭</Text></div>
  </Space>
</template>

<script setup lang="ts">
  import {
    Space, Text, Input, Button,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  import { reactive } from 'vue'
  import {
    Search,
  } from '@xhs/delight/icons'
  import { linkmicSwitch } from '../../services/zhibo'

  const state = reactive({
    hostId: '',
    hostSwitch: null,
    viewerSwitch: null,
  })

  function query() {
    const payload = {
      hostId: state.hostId,
    }

    console.log('ninghuanjun-task--request--', payload)

    linkmicSwitch(payload)
      .then(res => {
        if (res != null) {
          state.hostSwitch = res.hostSwitch
          state.viewerSwitch = res.viewerSwitch
        }
      })
  }
</script>
