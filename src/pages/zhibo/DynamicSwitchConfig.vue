<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >

    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:10px"
      >主播id：</Text>
      <Input
        v-model="state.hostId"
        :style="{ width: '90px' }"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="query()"
      >查询</Button>
    </NewBsBox>

    <div v-if="state.clipShowStatus == 1"><Text>全部直播回放展示</Text></div>
    <div v-if="state.clipShowStatus == 2"><Text>全部直播回放隐藏</Text></div>
    <div v-if="state.dynamicShowStatus == 1"><Text>直播动态入口展示</Text></div>
    <div v-if="state.dynamicShowStatus == 2"><Text>直播动态入口隐藏</Text></div>
  </Space>
</template>

<script setup lang="ts">
  import {
    Space, Text, Input, Button,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  import { reactive } from 'vue'
  import {
    Search,
  } from '@xhs/delight/icons'
  import { dynamicSwitchConfig } from '../../services/zhibo'

  const state = reactive({
    hostId: '',
    clipShowStatus: -1,
    dynamicShowStatus: -1,
  })

  function query() {
    const payload = {
      hostId: state.hostId,
    }

    console.log('ninghuanjun-task--request--', payload)

    dynamicSwitchConfig(payload)
      .then(res => {
        if (res != null) {
          state.clipShowStatus = res.clipShowStatus
          state.dynamicShowStatus = res.dynamicShowStatus
        }
      })
  }
</script>
