<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >

    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:10px"
      >直播间id：</Text>
      <Input
        v-model="state.roomId"
        :style="{ width: '90px' }"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="query()"
      >查询</Button>
    </NewBsBox>

    <div v-if="state.distribute == 1"><Text>直播间所有人可见</Text></div>
    <div v-if="state.distribute == 0"><Text>直播间仅分享可见</Text></div>
  </Space>
</template>

<script setup lang="ts">
  import {
    Space, Text, Input, Button,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  import { reactive } from 'vue'
  import {
    Search,
  } from '@xhs/delight/icons'
  import { distribute } from '../../services/zhibo'

  const state = reactive({
    roomId: '',
    distribute: -1,
  })

  function query() {
    const payload = {
      roomId: state.roomId,
    }

    console.log('ninghuanjun-task--request--', payload)

    distribute(payload)
      .then(res => {
        if (res != null) {
          state.distribute = res.distribute
        }
      })
  }
</script>
