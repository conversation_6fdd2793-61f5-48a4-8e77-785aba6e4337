<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <Text>selected: {{ selected }}</Text>
    <Table
      v-model:selected="selected"
      :columns="columns"
      :data-source="dataSource"
      :row-selection="rowSelection"
      style="max-height: 300px"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
    </Table>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Space, Table, Text } from '@xhs/delight'

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'good',
    },
    {
      title: '标签',
      dataIndex: 'tag',
    },
    {
      title: '简介',
      dataIndex: 'description',
    },
    {
      title: '管理员',
      dataIndex: 'manager',
      fixed: 'right',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },

  ]

  const selected = ref([])

  const rowSelection = {
    getCheckboxProps: v => ({
      checked: v.key === 'Nike Air Max 90 Premium',
      disabled: v.key === 'Nike Air Max 90',
      selectable: v.key !== 'Nike Air Force 1 Mid 07 WB',
    }),
    onSelected: v => console.log(v),
  }

  function handleSelectedChange(v) {
    console.log(v)
  }

  function handleSelectedAll(v) {
    console.log(v)
  }

  const dataSource = [
    'Nike Air Max 90 Premium',
    'Nike Air Max 90',
    'Nike Air Force 1 07 PRM',
    'Nike Air Force 1 Mid 07 WB',
  ]
    .map(
      (good, i) => ({
        key: good,
        good,
        tag: `标签${i}`,
        description: `描述文案描述文案描述文案描述文案描述文案描述文案${i}`,
        manager: `管理员${i}`,
        operation: '编辑',
        ...good === 'Nike Air Force 1 07 PRM' && {
          children: [
            'Nike Air Force 1 07 PRM - 40',
            'Nike Air Force 1 07 PRM - 41',
            'Nike Air Force 1 07 PRM - 42',
            'Nike Air Force 1 07 PRM - 43',
          ]
            .map(
              (g, ii) => ({
                key: g,
                good: g,
                tag: `标签${ii}`,
                description: `描述文案描述文案描述文案描述文案描述文案描述文案${ii}`,
                manager: `管理员${ii}`,
                operation: '下架',
              }),
            ),
        },
      }),
    )
</script>
