<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >任务状态:</Text>
      <Input
        v-model="state.taskstatus"
        :style="{ width: '90px', }"
      />
      <Text
        bold
        style="margin-left:10px"
      >任务名称:</Text>
      <Input
        v-model="state.taskComment"
        :style="{ width: '120px' ,}"
      />
      <Text
        bold
        style="margin-left:10px"
      >执行人:</Text>
      <Input
        v-model="state.userName"
        :style="{ width: '120px' ,}"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchTaskRecord()"
      >查询</Button>
      <Button
        style="float:right;margin:10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>
    <NewBsBox :bs="{ display: 'flex',boxShadow: 'none', ml: 30, flex: 1,alignItems: 'center' }">
      <Button
        :style="{ backgroundColor: '#28BC77', color: 'white',marginLeft: '-50px', }"
        :icon="{ icon: Add }"
        @click="handleAddJob"
      >创建任务</Button>
    </NewBsBox>
  </Space>
  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <Table2
      v-model:expand-row-keys="expandRowKeys"
      :columns="perftaskcolumns"
      :data-source="state.dataSource"
      :loading="loading"
      size="small"
      :expand-on-row-click="true"
      :hover="hover"
      @row-click="(row,column)=>clickDeviceID(row,column)"
    >
      <template #operation="{ rowData }">
        <Link @click="stopTask(rowData)">停止</Link>
      </template>
      <template #expandedRowRender="{ rowData }">
        <Card :style="{ width: 'auto' }">
          <Table2
            v-model:expand-row-keys="devexpandRowKeys"
            :columns="deviceIdColumns"
            :data-source="state.myDeviceMap.get(rowData.id)"
            :loading="loading"
            size="small"
            :expand-on-row-click="true"
            :hover="hover"
            @row-click="(row,column)=>clickOneDeviceID(row,column)"
          >
            <template #capability="{ rowData }">
              <Tag
                v-for="(item, index) in strtolist(rowData.capability)"
                :key="index"
                :style="{ marginRight: '5px', }"
                size="smallText"
                :color="'orange'"
              >{{ item }}</Tag>
            </template>
            <template #operation2="{ rowData }">
              <Link @click="showDevice(rowData)">查看</Link>
            </template>
            <template #expandedRowRender="{ rowData }">
              <Tabs
                :key="rowData.id"
                force-render
                :size="small"
              >
                <TabPane
                  :id="rowData.id + '_CPU'"
                  label="CPU"
                >
                  <Line
                    :key="rowData.id"
                    :chart-data="state.cpuDataMap.get(rowData.id)"
                    :dimensions="cpudimensions"
                    :metrics="cpumetrics"
                    :chart-config="cpuchartConfig"
                    :is-loading="iscpuLoading"
                  />

                </TabPane>
                <TabPane
                  :id="rowData.id + '_MEM'"
                  label="MEM"
                >
                  <Line
                    :key="rowData.id"
                    :chart-data="state.memDataMap.get(rowData.id)"
                    :dimensions="memdimensions"
                    :metrics="memmetrics"
                    :chart-config="memchartConfig"
                    :is-loading="ismemLoading"
                  />
                </TabPane>
                <TabPane
                  :id="rowData.id + '_FPS'"
                  label="FPS"
                >
                  <Line
                    :key="rowData.id"
                    :chart-data="state.fpsDataMap.get(rowData.id)"
                    :dimensions="fpsdimensions"
                    :metrics="fpsmetrics"
                    :chart-config="fpschartConfig"
                    :is-loading="isfpsLoading"
                  />
                </TabPane>
                <TabPane
                  :id="rowData.id + '_GPU'"
                  label="GPU"
                >
                  <Line
                    :key="rowData.id"
                    :chart-data="state.gpuDataMap.get(rowData.id)"
                    :dimensions="gpudimensions"
                    :metrics="gpumetrics"
                    :chart-config="gpuchartConfig"
                    :is-loading="isgpuLoading"
                  />
                </TabPane>
                <TabPane
                  :id="rowData.id + '_TEM'"
                  label="TEM"
                >
                  <Line
                    :key="rowData.id"
                    :chart-data="state.temDataMap.get(rowData.id)"
                    :dimensions="temdimensions"
                    :metrics="temmetrics"
                    :chart-config="temchartConfig"
                    :is-loading="istemLoading"
                  />
                </TabPane>
              </Tabs>
            </template>
          </Table2>
        </Card>
      </template>
    </Table2>
    <NewBsBox :bs="{ margin: 20, }">
      <Pagination
        v-model="state.pageNum"
        :total="state.dataLength"
        align="end"
        @update:modelValue="handlePagination"
        @update:pageSize="handlepageSize"
      />
    </NewBsBox>
  </NewBsBox>
  <Modal
    v-model:visible="showTaskModal"
    title="创建任务"
    :with-footer="false"
    :style="{ width: 'auto' }"
    @cancel="handleTClose"
  >
    <Form
      ref="formRef"
      :label-position="labelPosition"
      :rules="rules"
      :model="taskModel"
      label-width="100px"
    >
      <FormItem label="任务描述">
        <Input
          v-model="taskModel.taskComment"
          placeholder="任务描述"
        />
      </FormItem>
      <FormItem label="性能指标">
        <Space
          direction="vertical"
          align="start"
        >
          <CheckboxGroup
            v-model="capability"
            :options="capabilityOptions"
            name="form"
          />
        </Space>
      </FormItem>
      <FormItem
        name="deviceIdList"
        label="DeviceID"
        :show-optional-text="false"
      >
        <InputLabel
          :data="taskModel.deviceIdList"
          :limit="20"
          :fix-click-show="false"
          @onValidateTag="onValidateTag"
          @change="changeDeviceID"
        />
      </FormItem>
      <FormItem label="采样间隔">
        <InputNumber
          v-model="taskModel.intervalTime"
          :min="3"
          :max="10000"
        />
      </FormItem>
      <FormItem label=" ">
        <Space>
          <Button
            type="primary"
            @click="submitTask"
          >创建</Button>
          <Button
            @click="handleTClose"
          >取消</Button>
        </Space>
      </FormItem>
    </Form>
  </Modal>
</template>
<script setup lang="ts">
  import {
    reactive, ref, onMounted,
  } from 'vue'
  // import { useRouter } from 'vue-router'
  import {
    Space, Table2, Text, Button, toast, Pagination, Input, Tag, Form2 as Form, FormItem2 as FormItem, Modal, CheckboxGroup, InputNumber, Link, Card, Tabs, TabPane,
  } from '@xhs/delight'
  import { NewBsBox } from '@xhs/yam-beer'
  import {
    Search, Clear, Add,
  } from '@xhs/delight/icons'
  import { useStore } from 'vuex'
  // import * as echarts from 'echarts'
  import { Line } from '@xhs/delight-charts'
  import '@xhs/delight-charts/dist/style.css'
  import {
    TaskListDisplay, perfTestStart, perfTestEnd, singleTaskDisplay, getShishiResult,
  } from '../../services/apptools'
  import InputLabel from '../task/components/InputLabel.vue'

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'
  // const router = useRouter()
  // 定义标签验证内容
  enum VALIDATE {
    REG, // 正则表达式验证
    LIMIT, // 标签数量验证
    REPEAT, // 标签重复验证
  }
  // 类型定义
  interface LabelModel {
    name: string
  }

  const showTaskModal = ref(false)
  const formRef = ref()
  const labelPosition = ref('right')
  const expandRowKeys = ref([])
  const hover = ref(false)
  const devexpandRowKeys = ref([])

  const capability = ref(['CPU', 'FPS', 'GPU', 'MEM', 'TEM'])

  const capabilityOptions = [
    {
      value: 'CPU',
      label: 'CPU',
    },
    {
      value: 'FPS',
      label: 'FPS',
    },
    {
      value: 'GPU',
      label: 'GPU',
    },
    {
      value: 'MEM',
      label: 'MEM',
    },
    {
      value: 'TEM',
      label: 'TEM',
    },
  ]
  const cpudimensions = ref(['date'])
  const cpumetrics = ref(['cpu'])
  const iscpuLoading = ref(true)
  setTimeout(() => {
    iscpuLoading.value = false
  }, 1000)
  const cpuchartConfig = ref({
    legend: {
      data: ['cpu'],
      left: 'center',
    },
  })

  const memdimensions = ref(['date'])
  const memmetrics = ref(['mem'])
  const ismemLoading = ref(true)
  setTimeout(() => {
    ismemLoading.value = false
  }, 1000)
  const memchartConfig = ref({
    legend: {
      data: ['mem'],
      left: 'center',
    },
  })

  const fpsdimensions = ref(['date'])
  const fpsmetrics = ref(['fps'])
  const isfpsLoading = ref(true)
  setTimeout(() => {
    isfpsLoading.value = false
  }, 1000)
  const fpschartConfig = ref({
    legend: {
      data: ['fps'],
      left: 'center',
    },
  })

  const gpudimensions = ref(['date'])
  const gpumetrics = ref(['gpu'])
  const isgpuLoading = ref(true)
  setTimeout(() => {
    isgpuLoading.value = false
  }, 1000)
  const gpuchartConfig = ref({
    legend: {
      data: ['gpu'],
      left: 'center',
    },
  })

  const temdimensions = ref(['date'])
  const temmetrics = ref(['tem'])
  const istemLoading = ref(true)
  setTimeout(() => {
    istemLoading.value = false
  }, 1000)
  const temchartConfig = ref({
    legend: {
      data: ['tem'],
      left: 'center',
    },
  })

  const loading = ref(false)
  const state = reactive({
    dataSource: [],
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskstatus: '',
    userName: '',
    taskComment: '',
    devdataSource: [],
    myDeviceMap: new Map(),
    myDevStatMap: new Map(),
    cpuDataMap: new Map(), // 阿翡
    memDataMap: new Map(),
    fpsDataMap: new Map(),
    gpuDataMap: new Map(),
    temDataMap: new Map(),

  })
  const taskModel = reactive({
    taskComment: '',
    deviceIdList: [],
    intervalTime: 5,
  })
  const perftaskcolumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
    },
    {
      title: '任务详情',
      dataIndex: 'taskComment',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
    },
    {
      title: '机型列表',
      dataIndex: 'idList',
    },
    {
      title: '执行人',
      dataIndex: 'userName',
    },
    {
      title: '执行时间',
      dataIndex: 'timenow',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },
  ]
  const deviceIdColumns = [
    {
      title: '机型ID',
      dataIndex: 'id',
    },
    {
      title: 'deviceId',
      dataIndex: 'deviceId',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
    },
    {
      title: 'taskId',
      dataIndex: 'taskId',
    },
    {
      title: '性能指标',
      dataIndex: 'capability',
    },
    {
      title: '采样间隔',
      dataIndex: 'intervalTime',
    },
    {
      title: 'startTime',
      dataIndex: 'startTime',
    },
    {
      title: '执行人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation2',
    },
  ]

  function strtolist(str) {
    const arr = JSON.parse(str.replace(/'/g, '"'))
    return arr
  }

  function getTaskListDisplay() {
    loading.value = true
    const queryparam = {}

    if (state.userName) queryparam.userName = state.userName
    if (state.taskstatus) queryparam.status = state.taskstatus
    if (state.taskComment) queryparam.taskComment = state.taskComment
    // const queryparam = {
    //   userName:"wuzhihao",
    //   status:"OK",
    //   taskComment:""
    // }
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }
    console.log('ninghuanjun-request--', payload)

    state.dataSource = []
    TaskListDisplay(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger('无数据！请更改查询条件。')
        } else {
          state.dataLength = res.dataLength
          state.dataSource = res.data
          if (res.dataLength !== 0) {
            state.dataSource.map(item => {
              item.key = item.id
              return item
            })
          }
        }
      })
  }
  function handlePagination() {
    getTaskListDisplay()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getTaskListDisplay()
  }
  function handleAddJob() {
    showTaskModal.value = true
  }
  function handleTClose() {
    showTaskModal.value = false
  }
  const changeDeviceID = (params: LabelModel[]) => { // 标签值改变
    taskModel.deviceIdList = params.map(item => item.name)
    // console.log('formTask.value.streamType---', taskModel.deviceIdList)
  }
  const onValidateTag = (params: number) => { // 标签验证方法
    if (params === VALIDATE.LIMIT) {
      toast.danger('限制标签数量')
    } else if (params === VALIDATE.REG) {
      toast.danger('判断标签内容是否符合规则')
    } else if (params === VALIDATE.REPEAT) {
      toast.danger('判断标签内容是否重复')
    }
  }

  const submitTask = () => {
    const request = {
      taskComment: taskModel.taskComment,
      capability: Object.values(capability.value),
      deviceIdList: Object.values(taskModel.deviceIdList),
      intervalTime: taskModel.intervalTime,
      userName,
    }
    const payload = {
      request,
    }

    perfTestStart(payload).then(res => {
      if (res.status !== 0) {
        toast.danger(res.message)
      } else {
        showTaskModal.value = false
        toast.success(res.message)
        getTaskListDisplay()
      }
    })
  }
  // stopTask
  function stopTask(data:any) {
    const payload = {
      request: {
        id: data.id,
      },
    }
    perfTestEnd(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          getTaskListDisplay()
        }
      })
  }
  function searchTaskRecord() {
    getTaskListDisplay()
  }

  const clickDeviceID = (row:any, column:any) => {
    const id = column.rowData.id
    const payload = {
      request: {
        idList: JSON.parse(column.rowData.idList),
      },
    }
    singleTaskDisplay(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.devdataSource = res.data
          if (res.dataLength !== 0) {
            state.devdataSource.map(item => {
              item.key = item.id
              return item
            })
          }
          state.myDeviceMap.set(id, state.devdataSource)
        }
      })
  }
  const clickOneDeviceID = (row:any, column:any) => {
    const id = column.rowData.id
    const payload = {
      request: {
        taskId: column.rowData.taskId,
        deviceId: column.rowData.deviceId,
      },
    }

    getShishiResult(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          // mock
          // const resstr = '{"data":{"timelist":[["2023-12-05 14:00:03","889988"],["2023-12-05 14:00:20","593304"],["2023-12-05 14:00:22","367475"],["2023-12-05 14:00:23","944395"],["2023-12-05 14:00:26","385254"],["2023-12-05 14:04:04","268419"]],"cpulist":[12.21,12.21,12.21,12.21,12.21,12.21],"fpslist":[27,27,27,27,27,27],"gpulist":[30.18,30.18,30.18,30.18,30.18,30.18],"memlist":[88.81,88.81,88.81,88.81,88.81,88.81]},"message":"success","status":0}'
          // res = JSON.parse(resstr)

          let cpuresult = []
          let memresult = []
          let fpsresult = []
          let gpuresult = []
          let temresult = []

          if (res.data.cpulist.length !== 0) {
            cpuresult = res.data.timelist.map((item, index) => ({
              date: item[0],
              cpu: res.data.cpulist[index],
            }))
            state.cpuDataMap.set(id, cpuresult)

            memresult = res.data.timelist.map((item, index) => ({
              date: item[0],
              mem: res.data.memlist[index],
            }))
            state.memDataMap.set(id, memresult)

            fpsresult = res.data.timelist.map((item, index) => ({
              date: item[0],
              fps: res.data.fpslist[index],
            }))
            state.fpsDataMap.set(id, fpsresult)

            gpuresult = res.data.timelist.map((item, index) => ({
              date: item[0],
              gpu: res.data.gpulist[index],
            }))
            state.gpuDataMap.set(id, gpuresult)

            temresult = res.data.timelist.map((item, index) => ({
              date: item[0],
              tem: res.data.temlist[index],
            }))
            state.temDataMap.set(id, temresult)

            // console.log('点击table行', state.cpuDataMap.get(id))
          // tabvalue.value = id
          }
        }
      })
  }

  const showDevice = (row:any) => {
    console.log(row)
  }

  onMounted(() => {
    getTaskListDisplay()
  })
</script>

<style lang="stylus" scoped>

</style>
