<!-- 组件功能：按压enter键后，生成自定义标签。还可以同时选择固定标签 -->
<template>
  <!-- 固定标签 把固定标签移到组件中 -->
  <!-- <div class="fixed-layout">
        <div v-for="(item, index) in fixedTags" :key="index" @click="tagClick(index, item)" class="label-box">
            <span class="fixed-label-title">{{ item.name }}</span>
        </div>
    </div> -->

  <div class="layout">
    <!-- 自定义标签样式 -->
    <div
      v-for="(item, index) in tagsArr"
      :key="index"
      class="label-box"
    >
      <span class="label-title">{{ item.name }}</span>
      <i
        class="label-close"
        @click="removeTag(index, item)"
      />
    </div>
    <!-- 输入框 -->
    <input
      ref="inputTagRef"
      v-model="currentVal"
      :placeholder="placeholder"
      class="input-tag"
      type="text"
      @keyup.enter="addTags"
    >
  </div>
</template>

<script setup lang='ts'>
  import {
    ref, reactive, toRefs, toRaw, watch, onMounted,
  } from 'vue'

  // 定义标签验证内容
  enum VALIDATE {
    REG, // 正则表达式验证
    LIMIT, // 标签数量验证
    REPEAT, // 标签重复验证
  }

  // 类型定义
  interface LabelModel {
    name: string
  }

  interface SubModel {
    tagsArr: LabelModel[]
  }

  // 定义类型， widthDefaults不支持外部导入
  interface PropsModel {
    fixClickShow?: boolean // 固定标签点击选中后，是否隐藏 true：隐藏 false: 固定在原位置不动
    reg?: any // 标签内容正则表达式
    limit?: number // 最多能输入几个标签
    placeholder?: string // 提示信息
    fixedTags?: { // 固定标签参数
      name: string // 固定标签的名称
      show: boolean // 是否显示隐藏
    }[]
    data?:any
  }

  // 接收父组件参数
  const props = withDefaults(defineProps<PropsModel>(), { // 设置参数默认值
    fixClickShow: true, // 默认点击固定标签后隐藏
    reg: /^[\u4E00-\u9FA5A-Za-z0-9_]{1,10}$/, // 默认标签只能输入中文、大小写、数字，1-10位的数据
    placeholder: '请输入后回车', // 提示信息
    fixedTags: () => [], // 固定标签数组

  })

  // 参数定义
  const currentVal = ref('') // 输入的标签内容
  const state = reactive<SubModel>({
    tagsArr: [ // 设置默认值，可直接显示在输入框内
    //   {
    //       name: "VIP",
    //   }
    ], // 输入的标签数组
  })

  // 数据解构
  const { tagsArr } = toRefs(state)

  // const emits = defineEmits(['onRemoveTag']);
  const emits = defineEmits<{(event: 'onValidateTag', params: number): void // 数据验证
                             (event: 'change', params: LabelModel[]): void // 标签值改变
  }>()
  // const emit = defineEmits(['change']);

  // 数据监听
  watch([() => state.tagsArr], () => { // 监听输入框内值改变
    // state.tagsArr = newParentArr.length ? newParentArr : [];
    console.log('aaaaaa---list---', state.tagsArr)
    emits('change', toRaw(state.tagsArr))
  }, { deep: true })

  // 方法定义
  //   // 点击固定标签，显示到输入框内
  //   const tagClick = (index: number, item: LabelModel) => {
  //     const result = validateFn(item.name)
  //     if (result) { // 验证通过
  //       if (props.fixClickShow) { // 点击固定标签后隐藏
  //         props.fixedTags[index].show = false
  //       }

  //       const tag = {
  //         name: item.name,
  //       }

  //       state.tagsArr.push(tag)
  //     }
  //   }

  /**
   * 验证数据
   * @param validateName：验证标签内容是否重复
   * @param from: 来源 custom: 手动输入 fixed: 固定标签
   */
  const validateFn = (validateName: string): boolean => {
    if (props.reg) { // 正则验证标签内容
      if (!props.reg.test(validateName)) {
        emits('onValidateTag', VALIDATE.REG)
        return false
      }
    }

    if (props.limit) {
      if (state.tagsArr.length + 1 > props.limit) { // 限制标签个数
        emits('onValidateTag', VALIDATE.LIMIT)
        return false
      }
    }

    // for (const i in state.tagsArr) {
    state.tagsArr.forEach(item => {
      //   if (state.tagsArr[i].name === validateName) { // 判断输入标签是否重复
      if (item.name === validateName) {
        emits('onValidateTag', VALIDATE.REPEAT)
      }
    })
    // }

    return true
  }

  // 自定义输入标签，添加到输入框内
  const addTags = () => {
    const result = validateFn(currentVal.value)
    if (result) {
      //   // 手动输入的标签，判断标签内容和固定标签内容是否一致，一致替换为固定标签的颜色
      //   //   for (const i in props.fixedTags) {
      //   props.fixedTags.forEach(item => {
      //     if (item.name === currentVal.value) {
      //       if (props.fixClickShow) { // 点击固定标签后隐藏
      //         item.show = false
      //       }
      //       break
      //     }
      //   })
      //   //   }

      const tag = {
        name: currentVal.value,
      }

      state.tagsArr.push(tag)
      currentVal.value = ''
    }
  }

  // 删除标签方法
  //   const removeTag = (index: number, item: { name: string }) => {
  // if (props.fixClickShow) { // 点击固定标签后显示
  //   //   for (const i in props.fixedTags) {
  //   //   props.fixedTags.forEach(key => {

  //   //   for (const i in props.fixedTags) {
  //   Object.keys(props.fixedTags).map(i => {
  //     if (props.fixedTags[i].name === item.name) {
  //       props.fixedTags[i].show = true
  //       break
  //     }
  //   })
  //   //   })
  // //   }
  // }
  const removeTag = (index: number) => {
    state.tagsArr.splice(index, 1)
  }
  onMounted(() => {
    if (props.data) {
      props.data.forEach(item => {
        state.tagsArr.push({ name: item })
      })
    }
  })

</script>

<style scoped>
/** 固定标签 */
.fixed-layout {
    width: 330px;
    box-sizing: border-box;
    background-color: white;
    border-radius: 4px;
    font-size: 12px;
    text-align: left;
    padding-left: 5px;
    word-wrap: break-word;
    overflow: hidden;
    margin-bottom: 10px;
}

.fixed-label-title {
    height: 24px;
    line-height: 22px;
    position: relative;
    display: inline-block;
    padding: 0 8px;
    color: #495060;
    font-size: 12px;
    cursor: pointer;
    opacity: 1;
    vertical-align: middle;
    overflow: hidden;
    transition: 0.25s linear;
}

/* 外层div */
.layout {
    width: 330px;
    box-sizing: border-box;
    background-color: white;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    font-size: 12px;
    text-align: left;
    padding-left: 5px;
    word-wrap: break-word;
    overflow: hidden;
}

/* 标签 */
.label-box {
    display: inline-block;
    font-size: 14px;
    margin: 3px 4px 3px 0;
    background-color: #f7f7f7;
    border: 1px solid #e8eaec;
    border-radius: 3px;
}

.label-title {
    height: 24px;
    line-height: 22px;
    max-width: 99%;
    position: relative;
    display: inline-block;
    padding-left: 8px;
    color: #495060;
    font-size: 12px;
    cursor: pointer;
    opacity: 1;
    vertical-align: middle;
    overflow: hidden;
    transition: 0.25s linear;
}

.label-close {
    padding: 0 10px 0 4px;
    opacity: 1;
    -webkit-filter: none;
    filter: none;
    color: #495060;
    cursor:pointer;
}

.label-close:after {
    content: "\00D7";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* line-height: 27px; */
    transition: 0.3s, color 0s;

}

/* input */
.input-tag {
    font-size: 12px;
    border: none;
    box-shadow: none;
    outline: none;
    background-color: transparent;
    padding: 0;
    width: auto;
    width: 300px; /* 设置宽度和外层layout一致。保证了光标在输入框最下面 */
    vertical-align: top;
    height: 32px;
    color: #495060;
    line-height: 32px;
}
</style>
