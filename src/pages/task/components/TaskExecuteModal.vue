<template>
  <Drawer
    v-model:visible="resModalVisible"
    title="任务执行"
    mask
    mask-closeable
    size="calc(100vw - 500px)"
  >

    <NewBsBox :bs="{ ml: 20, mr: 20,}">
      <Form
        ref="formRef"
        v-model="formExcuTask"
        :config="config"
        @submit="handleSubmit"
      >
        <FormItem
          name="userName"
          label="姓名"
        >
          <Input
            :max-length="6"
            max-length-error="最多输入 6 个字符"
            placeholder="用户姓名"
            clearable
            required
            disabled
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="taskId"
          label="任务ID"
        >
          <Input
            :max-length="30"
            max-length-error="最多输入30个字符"
            placeholder=""
            clearable
            required
            disabled
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="taskType"
          label="服务类型"
        >
          <Select
            :options="taskTypeList"
            required
            disabled
          />
        </FormItem>
        <FormItem
          name="env"
          label="执行环境"
          help="选择环境"
          on-error="必填项"
        >
          <RadioGroup
            :options="[{ label: 'sit', value: 'sit' }, { label: 'staging', value: 'staging' },{ label: 'prod', value: 'prod' }]"
            required
          />
        </FormItem>
        <FormItem
          name="setName"
          label="素材集名称"
          help="搜索需要的素材集，直接关联ID和Type"
        >
          <Select
            v-model="state.setNameSelect"
            :options="state.setNameOptions"
            filterable
            :filter="filter"
            :loading="loading"
            remote
            clearable
            required
            :onUpdate:modelValue="fetchSetID"
          >
            <template #empty>
              <div style="padding: var(--size-space-large) 0;">
                <Result title="请输入筛选项进行搜索" />
              </div>
            </template>
          </Select>

        </FormItem>
        <FormItem
          name="setId"
          label="素材集ID"
        >
          <Input
            placeholder=""
            clearable
            required
            disabled
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="setType"
          label="素材集类型"
        >
          <Select
            :options="setTypeList"
            required
            disabled
          />
        </FormItem>
        <FormItem
          name="jsonTips"
          label="接口参数"
        >

          <div><JsonEditorVue
            v-model="jsonData"
            :show-btns="false"
            style="width:600px;height:350px;"
            class="editor"
            @blur="jsonValidate"
          /></div>
          <Input
            v-model="jsonTips"
            fade
            required
            disabled
          />
        </FormItem>

      </Form>
      <!-- <Text type="description">{{ formExcuTask }}</Text> -->
    </NewBsBox>

  </Drawer>
</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref, defineEmits, defineProps, watch,
  } from 'vue'
  import JsonEditorVue from 'json-editor-vue3'

  import {
    toast, Drawer, Form, FormItem, Select, Input, Result, useDebounce, RadioGroup,
    //  Button, Space, Text,Icon,Table, Pagination, Tooltip,
  } from '@xhs/delight'
  import {
    // Notes, Delete,
    //  Search,Clear,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'
  import { useStore } from 'vuex'
  import {
    taskTypeList, setTypeList,
  } from '../../../utils/common'
  import { getTaskSet, getCVTaskSet } from '../../../services/material'
  import { executeTask } from '../../../services/task'

  // 子组件的方法，接受父组件传值
  const props = defineProps({
    taskData: Object,
    isShow: Boolean,
  })

  const loading = ref(false)
  const resModalVisible = ref(false)
  const formRef = ref()

  const store = useStore()
  const { userName } = store.state.user.userInfo
  const jsonTips = ref('请输入正确的请求参数！')

  const formExcuTask = ref(
    {
      userName, // 登陆用户
      // setIdtest:-1
    },
  )
  // const config = ref(
  //   [
  //     {
  //       name: 'taskContent',
  //       label: '任务详情',
  //       help: '填写任务详情',
  //       component: {
  //         is: TextArea,
  //         maxLength: 100,
  //         maxLengthError: '最多输入 100 个字符',
  //         placeholder: '填写任务详情',

  //         clearable: true,
  //         required: true,
  //       },
  //     },
  //   ],
  // )

  const state = reactive({
    dataLength: 0,
    taskResSource: [],
    taskSetDataList: [],
    setNameSelect: '',
    setNameOptions: [] as any[],
    setNameIdMap: {},
    setNameTypeMap: {},

  })

  const jsonData = ref({})
  const jsonValidate = async editor => {
    const res = await editor.validate()

    // res 是错误列表，如果是空数组，则表示检测没有错误

    console.log(res)
  }

  const emit = defineEmits(['handleVisible'])

  function handleSubmit(v) {
    // console.log('ninghuanjun-提交t--')
    // console.log(jsonData.value)
    const payload = {
      request: {
        userName: v.userName,
        taskId: v.taskId,
        taskType: v.taskType,
        setId: v.setId,
        setType: v.setType,
        setName: v.setName,
        taskParam: jsonData.value,
        env: v.env,
      },
    }
    // console.log('ninghuanjun-createtask--request--', payload)

    executeTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          /// ///
          emit('handleVisible', false)
        }
      })
  }

  function fetchJobset(param) {
    // console.log('ninghuanjun----param---', param)
    const queryparam = {}
    if (param !== '') queryparam.setName = param
    const payload = {
      request: {
        queryparam,
      },
    }

    state.taskSetDataList = []
    // 判断来源
    // console.log('ninghuanjun-----来源是---', formExcuTask.value.taskType)
    if (formExcuTask.value.taskType === '3') {
      // 请求cv接口
      getCVTaskSet(payload)
        .then(res => {
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.taskSetDataList = res.data
            // console.log('ninghuanjun-----发请求--', state.taskSetDataList)
            if (state.taskSetDataList.length !== 0) {
              state.setNameOptions = Array.from(state.taskSetDataList, item => item.setName)
              state.taskSetDataList.forEach(item => {
                state.setNameIdMap[item.setName] = item.id
                state.setNameTypeMap[item.setName] = item.setType
              })
            }
          }
          loading.value = false
        })
    }
    if (formExcuTask.value.taskType === '1' || formExcuTask.value.taskType === '2') {
      getTaskSet(payload)
        .then(res => {
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.taskSetDataList = res.data
            if (state.taskSetDataList.length !== 0) {
              state.setNameOptions = Array.from(state.taskSetDataList, item => item.setName)
              state.taskSetDataList.forEach(item => {
                state.setNameIdMap[item.setName] = item.id
                state.setNameTypeMap[item.setName] = item.setType
              })
            }
          }
          loading.value = false
        })
    }
  }

  const filter = useDebounce(
    filterValue => {
      if (filterValue) {
        loading.value = true
        fetchJobset(filterValue)
      }
    },
    { delay: 300 },
  )

  function fetchSetID() {
    // console.log('ninghuanjun----搜索', state.setNameSelect)
    formExcuTask.value.setId = String(state.setNameIdMap[state.setNameSelect as keyof typeof state.setNameIdMap])
    formExcuTask.value.setType = String(state.setNameTypeMap[state.setNameSelect as keyof typeof state.setNameTypeMap])
  }

  watch(() => resModalVisible.value, (val: any) => {
    // console.log('ninghuanjun---tanchuang-3333--', val)
    emit('handleVisible', val)
    formExcuTask.value.taskId = String(props.taskData!.id)
    formExcuTask.value.taskType = String(props.taskData!.taskType)
  }, { deep: true, immediate: true })

  onMounted(() => {
    // console.log('ninghuanjun-mmmmm--')
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
