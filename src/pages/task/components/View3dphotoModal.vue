<template>
  <Drawer
    v-model:visible="viewModalVisible"
    title="基本信息"
    mask
    mask-closeable
    size="calc(100vw - 300px)"
  >

    <NewBsBox :bs="{ display: 'flex', mt: -20, }">
      <BeerPanel :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">
        <BeerTitle :bs="{ mb: 0 }">
          可视化
        </BeerTitle>
        <Divider />
        <NewBsBox :bs="{ ml: 20, mr: 20,}">
          <br>
          <div>
            <vue3videoPlay
              v-bind="options"
              :poster="state.imgSrc"
            />
          </div>
        </NewBsBox>
      </BeerPanel>
      <BeerPanel :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">

        <BeerTitle :bs="{ mb: 0 }">
          接口信息
        </BeerTitle>
        <Divider />
        <br>
        <json-viewer
          :value="state.jsonData"
          copyable
          :expand-depth="7"
          :expanded="true"
          boxed
        />

      </BeerPanel>
    </NewBsBox>

  </Drawer>
</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref, defineEmits, defineProps, watch,
  } from 'vue'
  import {
    NewBsBox, BeerPanel, BeerTitle,
  } from '@xhs/yam-beer'
  import {
    Drawer, Divider,
  } from '@xhs/delight'
  import {
    // Notes, Delete,
    //  Search,Clear,
  } from '@xhs/delight/icons'
  import JsonViewer from 'vue-json-viewer'
  import vue3videoPlay from 'vue3-video-play' // 引入组件
  import 'vue3-video-play/dist/style.css' // 引入css

  const viewModalVisible = ref(false)

  const state = reactive({
    imgView: '',
    imgSrc: '',
    jsonData: {},
  })
  const options = reactive({
    width: '600px', // 播放器高度
    height: '450px', // 播放器高度
    color: '#409eff', // 主题色
    title: '', // 视频名称
    src: '', // 视频源
    muted: false, // 静音
    webFullScreen: false,
    speedRate: ['0.75', '1.0', '1.25', '1.5', '2.0'], // 播放倍速
    autoPlay: false, // 自动播放
    loop: false, // 循环播放
    mirror: false, // 镜像画面
    ligthOff: false, // 关灯模式
    volume: 0.3, // 默认音量大小
    control: true, // 是否显示控制器
  })

  // 子组件的方法，接受父组件传值
  const props = defineProps({
    ResCheckParam: Object,
    photoresdata: Object,
    isShow: Boolean,
  })
  const emit = defineEmits(['PhotohandleVisible'])

  watch(() => viewModalVisible.value, (val: any) => {
    emit('PhotohandleVisible', val)
    if (val === true && props.photoresdata !== undefined) {
      state.imgView = props.photoresdata.imgsee
      state.imgSrc = props.photoresdata.fileurl
      state.jsonData = JSON.parse(props.photoresdata.jsonResult)
      options.src = state.jsonData.data.result[0].value.media.url
      // options.src = 'http://note-video-qc-1251524319.cos.ap-shanghai.myqcloud.com/110/01e2956f2a6be3740103700383ec51cc26_2.mp4'
    }
  }, { deep: true, immediate: true })

  onMounted(() => {
    //    console.log("宁焕",resModalVisible)

  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
