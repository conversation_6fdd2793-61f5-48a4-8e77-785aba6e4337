<template>
  <Drawer
    v-model:visible="resModalVisible"
    title="任务执行记录详情"
    mask
    mask-closeable
    size="calc(100vw - 300px)"
  >

    <NewBsBox :bs="{ ml: 20, mr: 20,}">
      <Table
        v-if="state.taskType!==3"
        :columns="taskResColumns"
        :data-source="state.taskResSource"
        :loading="loading"
        size="small"
        @selectedChange="handleSelectedChange"
        @selectedAll="handleSelectedAll"
      >
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
        <template #rescheckUrl="{ data }">
          <a
            style="font-size:14px"
            :href="data"
            target="_blank"
          >{{ data }}</a>
        </template>
        <template #setContent="{ data }">
          <a
            style="font-size:14px"
            :href="data"
            target="_blank"
          >{{ data }}</a>
        </template>

      </Table>
      <Table
        v-if="state.taskType===3"
        :columns="cvtaskResColumns"
        :data-source="state.taskResSource"
        :loading="loading"
        size="small"
        @selectedChange="handleSelectedChange"
        @selectedAll="handleSelectedAll"
      >
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
        <template #status="{ data }">
          <Tag
            v-if="data === '1'"
            theme="solid"
            color="green"
            size="small"
          >成功</Tag>
          <Tag
            v-if="data === '0'"
            theme="solid"
            color="orange"
            size="small"
          >未知错误</Tag>
          <Tag
            v-if="data === '2'"
            theme="solid"
            color="red"
            size="small"
          >未检测到主体</Tag>
          <Tag
            v-if="data === '3'"
            theme="solid"
            color="red"
            size="small"
          >下载图片失败</Tag>
          <Tag
            v-if="data === '4'"
            theme="solid"
            color="red"
            size="small"
          >上传图片失败</Tag>
          <Tag
            v-if="data === '5'"
            theme="solid"
            color="red"
            size="small"
          >算法处理失败</Tag>
          <Tag
            v-if="data === '6'"
            theme="solid"
            color="orange"
            size="small"
          >其他错误</Tag>
        </template>
        <template #jsonResult="{ data }">
          <span
            style="font-size:14px"
          >{{ data }}</span>
        </template>
        <template #operation="{ rowData }">
          <Button
            size="small"
            :style="{ backgroundColor: '#3366FF', color: 'white' }"
            @click="jsonDetail(rowData)"
          >格式化</Button>
        </template>

      </Table>
    </NewBsBox>
    <NewBsBox :bs="{ margin: 20, }">
      <Pagination
        v-model="state.pageNum"
        :total="state.dataLength"
        align="end"
        @update:modelValue="handlePagination"
        @update:pageSize="handlepageSize"
      />
    </NewBsBox>

    <Modal
      v-model:visible="showjsonDataModal"
      :mask-closeable="logmaskCloseable"
      title="数据"
      :with-footer="false"
      :style="{ width: '800px',height:'800px' }"
    >
      <json-viewer
        :value="state.jsonData"
        copyable
      />
    </Modal>

  </Drawer>
</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref, defineEmits, defineProps, watch,
  } from 'vue'
  import {
    Table, toast, Pagination, Tag, Drawer, Button, Modal,
    // Space, Text,Input,Icon,
  } from '@xhs/delight'
  import {
    // Notes, Delete,
    //  Search,Clear,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'
  import JsonViewer from 'vue-json-viewer'

  import { getTaskResultList } from '../../../services/task'

  const showjsonDataModal = ref(false)
  const logmaskCloseable = ref(false)

  const taskResColumns = [
    {
      title: 'workflowID',
      dataIndex: 'workflowID',
    },
    {
      title: 'correlationID',
      dataIndex: 'correlationID',
    },
    {
      title: 'setContent',
      dataIndex: 'setContent',
    },
    {
      title: 'contentID',
      dataIndex: 'contentID',
    },
    {
      title: 'rescheckUrl',
      dataIndex: 'rescheckUrl',
    },
  ]
  const cvtaskResColumns = [
    {
      title: 'file_id',
      dataIndex: 'file_id',
    },
    {
      title: 'status',
      dataIndex: 'status',
    },
    {
      title: 'jsonResult',
      dataIndex: 'jsonResult',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },
  ]
  const loading = ref(false)
  const resModalVisible = ref(false)

  const state = reactive({
    id: '',
    taskName: '',
    userName: '',
    taskType: 0,
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskResSource: [],
    taskId: '',

  })

  // 子组件的方法，接受父组件传值
  const props = defineProps({
    ResCheckParam: Object,
    resdata: Object,
    isShow: Boolean,
  })
  const emit = defineEmits(['handleVisible'])

  function getTaskResult(params:any) {
    loading.value = true
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        executeId: params.id ? params.id : params.execId,
        csvUrl: params.csvUrl,
      },
    }

    state.taskResSource = []
    // state.taskrecordSource = JSON.parse(restest).data

    getTaskResultList(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.taskResSource = res.data
        }
      })
  }

  function handlePagination() {
    getTaskResult(props.resdata)
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getTaskResult(props.resdata)
  }
  function jsonDetail(data:any) {
    showjsonDataModal.value = true
    state.jsonData = JSON.parse(data.jsonResult)
    logmaskCloseable.value = true
  }

  watch(() => resModalVisible.value, (val: any) => {
    emit('handleVisible', val)
    // console.log("ninghuanjun---弹窗---",val,props,props.resdata)
    if (val === true && props.resdata !== undefined) {
      state.taskType = props.resdata.taskType
      getTaskResult(props.resdata)
    }
  }, { deep: true, immediate: true })

  onMounted(() => {
    //    console.log("宁焕",resModalVisible)

  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
