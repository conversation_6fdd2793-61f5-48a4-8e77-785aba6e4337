<template>
  <Drawer
    v-model:visible="viewModalVisible"
    title="基本信息"
    mask
    mask-closeable
    size="calc(100vw - 300px)"
  >

    <NewBsBox :bs="{ display: 'flex', mt: -20, }">
      <BeerPanel :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">
        <BeerTitle :bs="{ mb: 0 }">
          可视化
        </BeerTitle>
        <Divider />
        <NewBsBox :bs="{ ml: 20, mr: 20,}">
          <div style="margin-top:10px;">
            <img
              width="500"
              :src="state.imgView"
            >

          </div>
        </NewBsBox>
      </BeerPanel>
      <BeerPanel :bs="{ boxShadow: 'none', margin: 0, flex: 1 }">

        <BeerTitle :bs="{ mb: 0 }">
          接口信息
        </BeerTitle>
        <Divider />
        <br>
        <json-viewer
          :value="state.jsonData"
          copyable
          :expand-depth="7"
          :expanded="true"
          boxed
        />

      </BeerPanel>
    </NewBsBox>

  </Drawer>
</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref, defineEmits, defineProps, watch,
  } from 'vue'
  import {
    NewBsBox, BeerPanel, BeerTitle,
  } from '@xhs/yam-beer'
  import {
    Drawer, Divider,
  } from '@xhs/delight'
  import {
    // Notes, Delete,
    //  Search,Clear,
  } from '@xhs/delight/icons'
  import JsonViewer from 'vue-json-viewer'

  const viewModalVisible = ref(false)

  const state = reactive({
    imgView: '',
    imgSrc: '',
    jsonData: {},

  })

  // 子组件的方法，接受父组件传值
  const props = defineProps({
    ResCheckParam: Object,
    resdata: Object,
    isShow: Boolean,
  })
  const emit = defineEmits(['handleVisible'])

  watch(() => viewModalVisible.value, (val: any) => {
    emit('handleVisible', val)
    // console.log('ninghuanjun---弹窗---', val, props, props.resdata)
    if (val === true && props.resdata !== undefined) {
      state.imgView = props.resdata.imgsee === '--' ? props.resdata.fileurl : props.resdata.imgsee
      state.imgSrc = props.resdata.fileurl
      state.jsonData = JSON.parse(props.resdata.jsonResult)

    //   getTaskResult(props.resdata)
    }
  }, { deep: true, immediate: true })

  onMounted(() => {
    console.log('a')
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
