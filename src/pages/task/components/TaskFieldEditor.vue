<template>
  <div
    class="task-field-editor"
    :style="containerStyle"
  >
    <!-- 字段列表标题 -->
    <div class="section-title">
      <span class="counter">{{ fields.length }} 个字段</span>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="scrollable-content">
      <!-- 字段列表 -->
      <div class="field-list">
        <div
          v-for="(field, index) in fields"
          :key="field.id"
          class="field-item"
          draggable="true"
          @dragstart="startDrag(index)"
          @dragover.prevent
          @drop="handleDrop(index)"
        >
          <div class="field-input-container">
            <label>任务名称</label>
            <input
              v-model="field.taskName"
              class="field-input"
              placeholder="输入任务名称"
              disabled
              @change="emitUpdate()"
            >
          </div>
          <div class="field-input-container">
            <label>字段名称</label>
            <input
              v-model="field.fieldName"
              class="field-input"
              placeholder="输入字段名称"
              @change="emitUpdate()"
            >
          </div>
          <div class="field-input-container">
            <label>JSON路径</label>
            <input
              v-model="field.path"
              class="field-input path-input"
              placeholder="输入JSON路径"
              disabled
              @change="emitUpdate()"
            >
          </div>
          <div class="field-input-container">
            <label>字段类型</label>
            <select
              v-model="field.rawType"
              class="field-select"
              @change="onTypeChange(field)"
            >
              <option value="">字符</option>
              <option
                v-if="field.path.includes('taskId')"
                value="get_transcode_log"
              >获取转码日志</option>
              <option
                v-if="field.path.includes('downloadUrl')"
                value="collect_video_url"
              >生成质量报告</option>
              <option
                v-if="field.path.includes('streamType')"
                value="check_player"
              >播放器检测</option>
              <option
                v-if="field.path.includes('subWorkflowId')"
                value="collect_subworkflow_id"
              >收集子工作流</option>
            </select>
            <!-- 只有选择collect_subworkflow_id才展示输入框 -->
            <select
              v-if="field.rawType === 'collect_subworkflow_id'"
              v-model="customSuffixArr[field.id]"
              class="field-select"
              style="margin-top:5px;"
              @change="onCustomInput(field)"
            >
              <option value="">请选择用例</option>
              <option
                v-for="(item, idx) in subworkflowIdOptions"
                :key="idx"
                :value="item.value"
              >
                {{ item.label }}
              </option>
            </select>
          </div>
          <button
            class="remove-button"
            title="删除字段"
            @click="removeField(index)"
          >
            <svg
              viewBox="0 0 24 24"
              width="16"
              height="16"
            >
              <path
                fill="currentColor"
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
              />
            </svg>
          </button>
        </div>

        <!-- 空状态提示 -->
        <div
          v-if="fields.length === 0"
          class="empty-state"
        >
          <svg
            viewBox="0 0 24 24"
            width="48"
            height="48"
          >
            <path
              fill="#ccc"
              d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V9H11V7M11,11H13V17H11V11Z"
            />
          </svg>
          <p>暂无字段配置，请点击获取字段按钮</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, computed, watch } from 'vue'
  import { getTaskList } from '../../../services/task'

  export default {
    name: 'TaskFieldEditor',
    props: {
      tasksJsonPath: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: [String, Number],
        default: '100%',
      },
      height: {
        type: [String, Number],
        default: '100%',
      },
      maxWidth: {
        type: [String, Number],
        default: '100%',
      },
      maxHeight: {
        type: [String, Number],
        default: '100%',
      },
      minWidth: {
        type: [String, Number],
        default: '400px',
      },
      minHeight: {
        type: [String, Number],
        default: '300px',
      },
    },
    emits: ['update:tasksJsonPath', 'change'],
    setup(props, { emit }) {
      // 计算容器样式
      const containerStyle = computed(() => {
        const style = {
          width: typeof props.width === 'number' ? `${props.width}px` : props.width,
          height: typeof props.height === 'number' ? `${props.height}px` : props.height,
          maxWidth: typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth,
          maxHeight: typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight,
          minWidth: typeof props.minWidth === 'number' ? `${props.minWidth}px` : props.minWidth,
          minHeight: typeof props.minHeight === 'number' ? `${props.minHeight}px` : props.minHeight,
        }
        return style
      })

      // 其余逻辑保持不变...
      const newTaskName = ref('')
      const newFieldName = ref('')
      const newFieldPath = ref('')
      const newFieldType = ref('')
      const fields = ref([])
      let nextId = 1

      // 在 setup 里加这些
      const customSuffixArr = ref({})

      const subworkflowIdOptions = ref([])

      // type reducer（外部数据=携带后缀，内部select用rawType，输入框用 customSuffixArr）
      function splitType(type) {
        if (!type) return { raw: '', suffix: '' }
        if (type.startsWith('collect_subworkflow_id')) {
          const arr = type.split('#')
          return { raw: arr[0], suffix: arr.slice(1).join('#') ? String(arr.slice(1).join('#')) : '' }
        }
        return { raw: type, suffix: '' }
      }

      // 初始化时【转换字段结构】存入附加变量
      const convertToFields = data => {
        const result = []
        if (!data || typeof data !== 'object') return result
        Object.keys(data).forEach(taskName => {
          Object.keys(data[taskName]).forEach(fieldName => {
            const value = data[taskName][fieldName]
            // 处理可能包含类型的情况（路径,类型）
            const [path, type] = typeof value === 'string'
              ? value.split(',')
              : [value.path || value, value.type || '']
            const { raw, suffix } = splitType((type || '').trim())
            const id = nextId
            result.push({
              id,
              taskName,
              fieldName,
              path: path.trim(),
              type: ((type || '').trim()),
              rawType: raw,
            })
            // suffix 单独存 customSuffixArr
            customSuffixArr.value[id] = suffix
            nextId += 1
          })
        })
        return result
      }

      function getSubworkflowIdOptions() {
        const payload = {
          request: {
            pageNum: 1,
            pageSize: 1000,
            queryparam: {
              taskMode: '0',
              taskType: '7',
            },
          },
        }
        getTaskList(payload)
          .then(res => {
            if (res.status === 0) {
              subworkflowIdOptions.value = res.data.map(item => ({
                value: item.id.toString(),
                label: item.taskName,
              }))
            }
          })
      }

      // 数据更新: 拼接真实type
      function updateFieldType(field) {
        console.log(customSuffixArr.value)
        if (field.rawType === 'collect_subworkflow_id') {
          const suffix = (customSuffixArr.value[field.id] || '').trim()
          if (suffix) {
            field.type = `${field.rawType}#${suffix}`
          } else {
            field.type = field.rawType
          }
        } else {
          field.type = field.rawType
        }
      }

      // 初始化数据
      const initData = () => {
        fields.value = convertToFields(props.tasksJsonPath)
        nextId = fields.value.length > 0 ? Math.max(...fields.value.map(f => f.id)) + 1 : 1
        getSubworkflowIdOptions()
      }

      watch(() => props.tasksJsonPath, initData, { immediate: true, deep: true })

      // 转换回原始嵌套格式
      const convertToNested = items => {
        const result = {}
        items.forEach(item => {
          if (!result[item.taskName]) {
            result[item.taskName] = {}
          }
          // 如果有类型，则存储为 "路径,类型" 格式
          result[item.taskName][item.fieldName] = item.type
            ? `${item.path},${item.type}`
            : item.path
        })
        return result
      }

      // 检查是否可以添加新字段
      const canAdd = computed(() => newTaskName.value && newFieldName.value && newFieldPath.value && !fields.value.some(f => f.taskName === newTaskName.value && f.fieldName === newFieldName.value))

      // 触发更新
      const emitUpdate = () => {
        const nestedData = convertToNested(fields.value)
        console.log('nestedData', nestedData)
        emit('update:tasksJsonPath', nestedData)
        emit('change', nestedData)
      }

      // select change
      function onTypeChange(field) {
        // 选中collect_subworkflow_id才处理输入框
        if (field.rawType === 'collect_subworkflow_id') {
          // 如果是拼过的type, 还原到suffix
          const { suffix } = splitType(field.type)
          customSuffixArr.value[field.id] = suffix
        } else {
          customSuffixArr.value[field.id] = ''
        }
        // 需要同步data/type
        updateFieldType(field)
        emitUpdate()
      }

      // 输入框
      function onCustomInput(field) {
        updateFieldType(field)
        emitUpdate()
      }

      // 删除字段
      const removeField = index => {
        const removedField = fields.value[index]
        fields.value.splice(index, 1)
        emitUpdate()
        emit('field-removed', removedField)
      }

      const draggedIndex = ref(null)
      const startDrag = index => {
        draggedIndex.value = index
      }
      const handleDrop = dropIndex => {
        if (draggedIndex.value === null) return
        const itemToMove = fields.value[draggedIndex.value]
        fields.value.splice(draggedIndex.value, 1)
        fields.value.splice(dropIndex, 0, itemToMove)
        draggedIndex.value = null
        emitUpdate()
      }

      return {
        containerStyle,
        newTaskName,
        newFieldName,
        newFieldPath,
        newFieldType,
        fields,
        canAdd,
        removeField,
        emitUpdate,
        startDrag,
        handleDrop,
        // 新增
        customSuffixArr,
        onTypeChange,
        onCustomInput,
        subworkflowIdOptions,
      }
    },
  }
</script>

<style scoped>
.task-field-editor {
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 16px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  margin-left: 16px;
  margin-right: 16px;
}

.counter {
  font-size: 14px;
  color: #666;
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
}

.field-list {
  margin: 12px 0;
  border: 1px solid #eaeaea;
  border-radius: 6px;
  background-color: #fafafa;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #999;
}

.empty-state p {
  margin-top: 12px;
  font-size: 14px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  cursor: move;
  transition: all 0.2s ease;
}

.field-item:hover {
  background-color: #f8f9fa;
}

.field-item:last-child {
  border-bottom: none;
}

.field-input-container {
  flex: 1;
  min-width: 0;
}

.field-input-container label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.field-input {
  width: 80%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
  background-color: #fff;
}

.field-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.path-input {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: transparent;
  color: #ff6b6b;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 18px;
}

.remove-button:hover {
  background-color: rgba(255, 107, 107, 0.1);
}

.add-form {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.add-form h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  align-items: flex-end;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.form-input {
  width: 80%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  height: 36px;
}

.add-button:hover {
  background-color: #3a7bc8;
}

.add-button.disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.field-select,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
  background-color: #fff;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  cursor: pointer;
  /* 确保与输入框对齐 */
  margin: 0;
  line-height: 1.2;
  box-sizing: border-box;
}

/* 选项样式 */
.field-select option,
.form-select option {
  background-color: #fff;
  color: #ffffff;
  padding: 8px;
}

/* 下拉菜单样式 (部分浏览器支持) */
.field-select optgroup,
.form-select optgroup {
  background-color: #f8f9fa;
  color: #ffffff;
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .field-item {
    flex-wrap: wrap;
  }

  .field-input-container {
    min-width: 120px;
  }

  .field-select,
  .form-select {
    width: 100%;
  }
}

/* 拖动效果 */
.field-item.drag-over {
  background-color: #f0f7ff;
  border: 1px dashed #4a90e2;
}
</style>
