<template>
  <NewBsBox
    ref="graph"
    class="workflow-graph"
    :style="{ position: 'relative', width: width + 'px', height: height + 'px', background: bgColor, overflow: 'hidden', cursor: dragging ? 'grabbing' : 'grab' }"
    @mousedown="onMouseDown"
    @wheel.prevent="onWheel"
    @touchstart.passive="onTouchStart"
  >
    <!-- 所有内容一起变换 -->
    <NewBsBox
      class="transform-container"
      :style="{
        position: 'absolute',
        top: 0, left: 0, width: '100%', height: '100%',
        transform: `translate(${viewOffset.x}px, ${viewOffset.y}px) scale(${scale})`,
        transformOrigin: '0 0'
      }"
    >
      <!-- 连线 -->
      <svg
        :width="width"
        :height="height"
        class="graph-svg"
        style="position:absolute;top:0;left:0;z-index:0;pointer-events:none;"
      >
        <template
          v-for="(conn, i) in connections"
          :key="i"
        >
          <path
            :d="getPath(conn)"
            class="graph-path"
            :stroke="conn.color || '#333'"
            stroke-width="2"
            fill="none"
          />
        </template>
        <defs>
          <marker
            id="arrow"
            markerWidth="10"
            markerHeight="10"
            refX="8"
            refY="3"
            orient="auto"
            markerUnits="strokeWidth"
          >
            <path
              d="M0,0 L0,6 L9,3 z"
              fill="#333"
            />
          </marker>
        </defs>
      </svg>
      <!-- 卡片们 -->
      <NewBsBox
        v-for="(card, i) in cards"
        :key="card.id"
        class="graph-card"
        :style="getCardStyle(card)"
        @click="onCardClick(card, i)"
      >

        <svg
          v-if="card.shape === 'circle'"
          :width="card.width"
          :height="card.height"
          style="display: block"
        >
          <circle
            :cx="card.width / 2"
            :cy="card.height / 2"
            :r="Math.min(card.width, card.height) / 2 - 2"
            :fill="card.bgColor || '#fff'"
            :stroke="card.borderColor || '#999'"
            stroke-width="2"
          />
          <text
            x="50%"
            y="50%"
            dominant-baseline="middle"
            text-anchor="middle"
            :style="{
              fontSize: (card.fontSize || 14) + 'px',
              fill: card.color || '#333'
            }"
          >
            {{ card.label }}
          </text>
        </svg>

        <!-- Diamond -->
        <svg
          v-else-if="card.shape === 'diamond'"
          :width="card.width"
          :height="card.height"
          style="display:block"
        >
          <polygon
            :points="getDiamondPoints(card)"
            :fill="card.bgColor || '#fffde4'"
            :stroke="card.borderColor || '#f9be4a'"
            stroke-width="2"
          />
          <text
            x="50%"
            y="50%"
            dominant-baseline="middle"
            text-anchor="middle"
            :style="{ fontSize: (card.fontSize || 14) + 'px', fill: card.color || '#333' }"
          >
            {{ card.label }}
          </text>
        </svg>

        <!-- Rect(Default) -->
        <div
          v-else
          class="card-rect"
          :style="{
            width: card.width + 'px', height: card.height + 'px',
            background: card.bgColor || '#e4f5ff',
            border: '2px solid ' + (card.borderColor || '#53a8fc'),
            borderRadius: (card.radius || 5) + 'px',
            color: card.color || '#333',
            fontSize: (card.fontSize || 14) + 'px',
            display: 'flex', alignItems: 'center', justifyContent: 'center'
          }"
        >
          {{ card.label }}
        </div>
      </NewBsBox>
    </NewBsBox>
  </NewBsBox>
</template>

<script setup>
  import {
    ref,
  } from 'vue'
  import { NewBsBox } from '@xhs/yam-beer'

  // 和之前一样
  const props = defineProps({
    width: { type: Number, default: 800 },
    height: { type: Number, default: 600 },
    bgColor: { type: String, default: '#f8fafd' },
    cards: { type: Array, required: true },
    connections: { type: Array, required: true },
  })

  // const cardsStyle = {
  // 	DEFAULT: {
  // 		shape: 'circle',
  // 		width: 100,
  // 		height: 50,
  // 		bgColor: '#e4ffec',
  // 		borderColor: '#222222',
  // 		color: '#1b5833',
  // 	},
  // 	SIMPLE: {
  // 		shape: 'rect',
  // 		width: 100,
  // 		height: 50,
  // 		bgColor: '#e4ffec',
  // 		borderColor: '#222222',
  // 		color: '#1b5833',
  // 	},
  // 	SWITCH: {
  // 		shape: 'diamond',
  // 		width: 100,
  // 		height: 50,
  // 		bgColor: '#e4ffec',
  // 		borderColor: '#222222',
  // 		color: '#1b5833',
  // 	},
  // 	SUB_WORKFLOW: {
  // 		shape: 'rect',
  // 		width: 100,
  // 		height: 50,
  // 		bgColor: '#e4ffec',
  // 		borderColor: '#000000',
  // 		color: '#1b5833',
  // 	},
  // }

  // 拖动、缩放相关
  const scale = ref(1)
  const minScale = 0.5; const
    maxScale = 1.5
  const viewOffset = ref({ x: 0, y: 0 })
  const dragging = ref(false)
  let startDrag = { x: 0, y: 0 }
  let startOffset = { x: 0, y: 0 }

  const emit = defineEmits(['card-click'])

  function onCardClick(card, i) {
    emit('card-click', card, i)
  }

  function onMouseMove(e) {
    if (!dragging.value) return
    const dx = e.clientX - startDrag.x
    const dy = e.clientY - startDrag.y
    viewOffset.value = {
      x: startOffset.x + dx,
      y: startOffset.y + dy,
    }
  }

  function onMouseUp() {
    dragging.value = false
    window.removeEventListener('mousemove', onMouseMove)
    window.removeEventListener('mouseup', onMouseUp)
  }

  function onMouseDown(e) {
    dragging.value = true
    startDrag = { x: e.clientX, y: e.clientY }
    startOffset = { ...viewOffset.value }
    window.addEventListener('mousemove', onMouseMove)
    window.addEventListener('mouseup', onMouseUp)
  }

  function onWheel(e) {
    // 鼠标滚轮事件
    // 缩放围绕鼠标在容器内点！
    const rect = e.currentTarget.getBoundingClientRect()
    const mx = e.clientX - rect.left; const
      my = e.clientY - rect.top
    const s0 = scale.value
    const ds = e.deltaY < 0 ? 1.1 : 0.9
    const s1 = Math.max(minScale, Math.min(maxScale, s0 * ds))
    if (s1 === s0) return

    // 鼠标点不动原理（维持鼠标处世界坐标不变）:推导得
    viewOffset.value = {
      x: mx - (mx - viewOffset.value.x) * (s1 / s0),
      y: my - (my - viewOffset.value.y) * (s1 / s0),
    }
    scale.value = s1
  }
  // Touch支持
  let lastTouch = null

  function onTouchMove(e) {
    e.preventDefault()
    if (!dragging.value) return
    const dx = e.touches[0].clientX - startDrag.x
    const dy = e.touches[0].clientY - startDrag.y
    viewOffset.value = {
      x: startOffset.x + dx,
      y: startOffset.y + dy,
    }
  }
  function onTouchEnd() {
    dragging.value = false
    window.removeEventListener('touchmove', onTouchMove)
    window.removeEventListener('touchend', onTouchEnd)
  }

  // Pinch and pan (双指) 支持缩放
  function getTouchesDist(touches) {
    const [a, b] = touches
    return Math.sqrt((a.clientX - b.clientX) ** 2 + (a.clientY - b.clientY) ** 2)
  }
  function getTouchesCenter(touches) {
    const [a, b] = touches
    return { x: (a.clientX + b.clientX) / 2, y: (a.clientY + b.clientY) / 2 }
  }
  function onTouchPinch(e) {
    if (e.touches.length !== 2) return
    e.preventDefault()
    const d = getTouchesDist(e.touches)
    const s0 = lastTouch.scale
    const ds = d / lastTouch.d
    const s1 = Math.max(minScale, Math.min(maxScale, s0 * ds))
    // 缩放围绕双指中心点
    const rect = e.currentTarget.getBoundingClientRect()
    const c = getTouchesCenter(e.touches)
    const mx = c.x - rect.left; const
      my = c.y - rect.top
    // 同 wheel
    viewOffset.value = {
      x: mx - (mx - viewOffset.value.x) * (s1 / s0),
      y: my - (my - viewOffset.value.y) * (s1 / s0),
    }
    scale.value = s1
  }
  function onTouchPinchEnd() {
    window.removeEventListener('touchmove', onTouchPinch)
    window.removeEventListener('touchend', onTouchPinchEnd)
  }

  function onTouchStart(e) {
    if (e.touches.length === 1) {
      dragging.value = true
      startDrag = { x: e.touches[0].clientX, y: e.touches[0].clientY }
      startOffset = { ...viewOffset.value }
      window.addEventListener('touchmove', onTouchMove, { passive: false })
      window.addEventListener('touchend', onTouchEnd)
    } else if (e.touches.length === 2) {
      // 双指缩放
      lastTouch = {
        d: getTouchesDist(e.touches),
        scale: scale.value,
        center: getTouchesCenter(e.touches),
      }
      window.addEventListener('touchmove', onTouchPinch, { passive: false })
      window.addEventListener('touchend', onTouchPinchEnd)
    }
  }
  // 原有内容...
  function getCardStyle(card) {
    return {
      position: 'absolute',
      left: `${card.x}px`,
      top: `${card.y}px`,
      width: `${card.width}px`,
      height: `${card.height}px`,
      zIndex: 1,
      background: 'transparent',
    }
  }
  function getDiamondPoints(card) {
    const w = card.width; const
      h = card.height
    return `${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${h / 2}`
  }
  function getCardCenter(card) {
    return {
      x: card.x + card.width / 2,
      y: card.y + card.height / 2,
    }
  }
  function getPath(conn) {
    const from = props.cards.find(c => c.id === conn.from)
    const to = props.cards.find(c => c.id === conn.to)
    if (!from || !to) return ''
    const A = getCardCenter(from); const
      B = getCardCenter(to)
    const dx = Math.abs(B.x - A.x) / 2
    return `M${A.x},${A.y} C${A.x + dx},${A.y} ${B.x - dx},${B.y} ${B.x},${B.y}`
  }
</script>

<style scoped>
.workflow-graph {
	border: 1px solid #e1e1e1;
	box-shadow: 0 2px 4px #ddd;
	border-radius: 10px;
	user-select: none;
}

.graph-card {
	user-select: none;
	pointer-events: auto;
	box-sizing: border-box;
}

.graph-card:hover {
	cursor: pointer;
}

.card-rect {
	box-shadow: 0 0 4px #a4cdf8;
}

.graph-path {
	transition: d 0.2s;
}
</style>
