<template>
  <Spinner
    :spinning="formRef?.status === 'waiting'"
    tip="校验中"
    size="large"
  >
    <Space
      direction="vertical"
      block
      style="padding: calc(var(--size-space-step-default) * 10);margin-left: 100px;"
      align="start"
    >
      <!-- <div v-if="state.taskModeTab==='1aaaa'"> -->
      <Form
        v-if="state.isRouterAlive"
        ref="formRef"
        v-model="formTask"
        @submit="handleSubmit"
      >
        <FormItem
          name="userName"
          label="姓名"
          help="填写用户姓名"
        >
          <Input
            v-model="formTask.userName"
            :max-length="20"
            max-length-error="最多输入 20 个字符"
            placeholder="用户姓名"
            clearable
            required
            disabled
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="taskName"
          label="任务名称"
          help="填写任务名称"
        >
          <Input
            v-model="formTask.taskName"
            placeholder="填写任务名称"
            clearable
            required
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          name="taskType"
          label="服务类型"
          help="选择服务类型"
        >
          <Select
            v-model="formTask.taskType"
            :options="taskTypeList"
            required
            :onUpdate:modelValue="getTemplateJson"
          >
          </Select>
        </FormItem>

        <FormItem
          v-if="formTask.taskType !== '7'"
          name="taskMode"
          label="任务模式"
          help="选择任务模式"
        >
          <RadioGroup
            v-model="formTask.taskMode"
            :options="[{ label: '简单模式', value: '0' }]"
            required
          />
          <!-- { label: '专家模式', value: '1' } -->
        </FormItem>
        <FormItem
          v-if="formTask.taskMode === '0' && formTask.taskType !== '7'"
          name="auto_enable"
          label="自动使能"
          help="自动使能"
          on-error="必填项"
        >
          <RadioGroup
            v-model="formTask.auto_enable"
            :options="autoEnableOption"
            required
          />
        </FormItem>
        <FormItem
          v-if="formTask.taskMode === '0' && formTask.taskType !== '7'"
          name="sceneType"
          label="场景类型"
          help="场景类型"
          on-error="必填项"
        >
          <RadioGroup
            v-model="formTask.sceneType"
            :options="[{ label: '国内', value: '无' }, { label: '海外', value: '海外' }]"
            required
          />
        </FormItem>
        <FormItem
          v-if="formTask.taskMode === '0' || formTask.taskType === '7'"
          name="env"
          label="执行环境"
          help="选择环境"
          on-error="必填项"
        >
          <RadioGroup
            v-model="formTask.env"
            :options="[{ label: 'sit', value: 'sit' }, { label: 'prod', value: 'prod' }]"
            required
            @change="clearSet"
          />
        </FormItem>
        <FormItem
          v-if="formTask.taskMode === '0' || formTask.taskType === '7'"
          name="clusterType"
          label="集群"
          help="选择集群"
          on-error="必填项"
        >
          <RadioGroup
            v-model="formTask.clusterType"
            :options="[{ label: 's0', value: 's0' }, { label: 's1_online', value: 's1_online' }, { label: 's1_offline', value: 's1_offline' }]"
            required
          />
        </FormItem>

        <FormItem
          v-if="formTask.taskMode === '0' && formTask.taskType !== '7'"
          name="setName"
          label="素材集名称"
          help="搜索需要的素材集，直接关联ID和Type"
        >
          <Select
            v-model="state.setNameSelect"
            :options="state.setNameOptions"
            filterable
            :filter="filter"
            :loading="loading"
            multi-line
            multiple
            clearable
            remote
            required
            @change="fetchSetID"
          >
            <template #empty>
              <div style="padding: var(--size-space-large) 0;">
                <Result title="请输入筛选项进行搜索" />
              </div>
            </template>
          </Select>
        </FormItem>
        <FormItem
          v-if="formTask.taskMode === '0' && (formTask.taskType === '8' || formTask.taskType === '2') && formTask.taskType !== '7'"
          name="check_streamTypes"
          label="质检码流"
          :show-optional-text="false"
        >
          <InputLabel
            :data="formTask.check_streamTypes"
            :limit="20"
            :fix-click-show="false"
            @onValidateTag="onValidateTag"
            @change="changecheck_streamTypes"
          />
        </FormItem>

        <FormItem
          v-if="formTask.taskType === '7'"
          name="workflowName"
          label="工作流名称"
        >
          <Input
            v-model="formTask.workflowName"
            placeholder="填写工作流名称"
            clearable
            required
            required-error="必填项"
          />
        </FormItem>
        <FormItem
          v-if="formTask.taskType === '7'"
          name="version"
          label="版本"
        >
          <Input
            v-model="formTask.version"
            placeholder="填写版本"
            clearable
            required
          />
        </FormItem>
        <FormItem
          v-if="formTask.taskType !== '7'"
          name="taskContent"
          label="任务详情"
        >
          <TextArea
            v-model="formTask.taskContent"
            :max-length="100"
            placeholder="填写任务详情"
            clearable
            required
          />

        </FormItem>
        <FormItem
          v-if="formTask.taskMode === '0' && typeof formTask.taskType !== 'undefined' && formTask.taskType !== null && formTask.taskType !== '' && formTask.taskType !== '7'"
          name="jsonTips"
          label="接口参数"
          :show-optional-text="false"
        >

          <div>
            <JsonEditorVue
              v-model="jsonData"
              :show-btns="false"
              style="width:600px;height:350px;"
              class="editor"
              @blur="jsonValidate"
            />
          </div>

        </FormItem>

        <FormItem
          v-if="formTask.taskType === '7'"
          name=""
          label=" "
          :show-optional-text="false"
        >
          <Space>
            <Button
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white', marginLeft: '250px' }"
              @click="showDrawer()"
            >收数配置</Button>
          </Space>
        </FormItem>
      </Form>

      <Drawer
        v-model:visible="paramDrawer.visible"
        size="70%"
        mask
        mask-closeable
        @close="closeDrawer()"
      >
        <template #title>
          <BeerTitle :bs="{ mb: 16 }"> {{ paramDrawer.title }} </BeerTitle>
        </template>
        <Spinner :spinning="paramDrawer.loading">
          <NewBsBox :bs="{ display: 'flex', flexWrap: 'wrap', gap: '10px' }">
            <!-- Left side - Input section -->
            <div :style="{ width: '45%' }">
              <Text
                bold
                style="font-size: 16px; margin-left:16px; margin-right:10px"
              >已添加的母板工作流:</Text>
              <!-- <BeerDivider :style="{ margin: '16px' }" /> -->
              <div
                class="scroll-container"
                :class="{ 'scroll-enabled': paramDrawer.workflowIds.length > 5 }"
              >
                <div
                  v-for="workflowId in paramDrawer.workflowIds"
                  :key="workflowId"
                  class="workflow-item"
                >
                  <Text style="font-size: 14px; margin-left:16px; margin-right:10px">
                    {{ workflowId }}
                  </Text>
                </div>
              </div>
            </div>
            <div :style="{ float: 'right', width: '50%', marginBottom: '10px' }">
              <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
                <Text
                  bold
                  style="font-size: 16px; margin-left:16px; margin-right:10px"
                >母板工作流:</Text>
                <Input
                  v-model="paramDrawer.workflowId"
                  label="母板工作流"
                  :style="{ width: '350px' }"
                  clearable
                  placeholder="请输入工作流Id，点击Enter添加"
                  @enter="addWorkflowId()"
                />
              </NewBsBox>
              <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
                <Tooltip>
                  <Button
                    style="margin-top:30px;margin-right:10px"
                    type="primary"
                    :icon="{ icon: Search }"
                    @click="queryTaskField()"
                  >获取字段</Button>
                  <template #content>
                    默认查询当前页面展示的工作流Id
                  </template>
                </Tooltip>
                <Button
                  style="margin-top:30px;margin-right:10px"
                  type="secondary"
                  :icon="{ icon: Redo }"
                  @click="resetDrawer()"
                >重置</Button>
              </NewBsBox>
              <!-- <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
                <Text
                  bold
                  style="font-size: 14px; margin-top:30px; margin-right:10px"
                >收数数量:</Text>
                <InputNumber
                  v-model="paramDrawer.limit"
                  :style="{ width: '80px', marginTop: '30px', marginRight: '10px' }"
                  :precision="0"
                  placeholder="默认为1000"
                />
                <Button
                  style="background-color: #11aa55; color: white; margin-top:30px;margin-right:10px"
                  :icon="{ icon: EditOne }"
                  @click="gatherResult()"
                >开始收数</Button>
              </NewBsBox> -->
            </div>
          </NewBsBox>
          <br>
          <div style="display: flex; max-height: 60%;">
            <!-- <div style="flex: 1;">
          <Text
            bold
            style="font-size: 16px; margin-left: 16px;"
          >
            工作流程图
          </Text>
          <BeerDivider :style="{ margin: '16px' }" />
          <WorkflowGraph
            :style="{ width: 'auto', height: '90%', margin: '16px' }"
            :cards="cards"
            :connections="connections"
            @card-click="cardClicked"
          />
        </div> -->
            <div style="flex: 1; ">
              <Text
                bold
                style="font-size: 16px; margin-left: 16px;"
              >
                任务详情
              </Text>
              <BeerDivider :style="{ margin: '16px' }" />
              <VirtualTree
                :key="treeKey"
                v-model:checked-keys="treeDataChecked"
                :style="{ height: '350px', width: '100%', margin: '16px' }"
                :tree-data="treeData"
                checkable
                expand-on-click-node
                @check="selectNode"
              />
            </div>
            <div style="flex: 1;">
              <Text
                bold
                style="font-size: 16px; margin-left: 16px;"
              >
                任务字段
              </Text>
              <BeerDivider :style="{ margin: '16px' }" />
              <TaskFieldEditor
                v-model:tasksJsonPath="tasksJsonPath"
                :height="350"
                :width="600"
                @field-removed="removeField"
              />
            </div>
          </div>
        </Spinner>
        <template #footer>
          <BeerDivider :style="{ margin: '16px' }" />

          <NewBsBox :bs="{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }">
            <Button
              type="danger"
              @click="setDefaultTaskField"
            >设置默认字段</Button>
            <Button
              type="primary"
              @click="closeDrawer"
            >确认</Button>
          </NewBsBox>
        </template>
      </Drawer>

      <!-- </div> -->
      <!-- <Text type="description">{{ formTask.taskMode }}---111--{{ state.setNameSelect }}----{{ formTask.setId }}</Text> -->

    </Space>
  </Spinner>
</template>

<script setup lang="ts">
  import axios from 'axios'
  import {
    ref, reactive, onMounted, nextTick,
  } from 'vue'
  import { useStore } from 'vuex'
  import { NewBsBox, BeerTitle, BeerDivider } from '@xhs/yam-beer'
  import {
    Spinner, Space, Input, TextArea, Button, Select, Form, FormItem, toast, RadioGroup, useDebounce, Result, Drawer, Text, VirtualTree, Tooltip,
  } from '@xhs/delight'
  import JsonEditorVue from 'json-editor-vue3'
  import {
    Search, Redo,
  } from '@xhs/delight/icons'
  import { useRouter } from 'vue-router'
  import {
    createTask, templateMake, updateTask, getTaskReportDefaultTaskField, setTaskReportDefaultTaskField,
  } from '../../services/task'
  import {
    taskTypeList, isIndexOfArray, AvtaskType, CvtaskType, autoEnableOption,
  } from '../../utils/common'
  import { getTaskSet, getCVTaskSet } from '../../services/material'
  import InputLabel from './components/InputLabel.vue'
  import TaskFieldEditor from './components/TaskFieldEditor.vue'

  // const route = useRoute()

  // 定义标签验证内容
  enum VALIDATE {
    REG, // 正则表达式验证
    LIMIT, // 标签数量验证
    REPEAT, // 标签重复验证
  }
  // 类型定义
  interface LabelModel {
    name: string
  }

  const paramDrawer = ref({
    visible: false,
    loading: false,
    title: '',
    workflowId: '',
    workflowIds: [],
    tasksJsonPath: {},
    limit: 1000,
  })

  const treeKey = ref(0)
  const treeData = ref([])
  const treeDataChecked = ref([])
  const treeDataCheckedLast = ref([])
  const tasksJsonPath = ref({})
  const tasksJsonPathKeys = ref([])

  const loading = ref(false)

  const jsonData = ref({})
  const jsonValidate = async editor => {
    const res = await editor.validate()

    // res 是错误列表，如果是空数组，则表示检测没有错误

    console.log(res)
  }

  const state = reactive({
    setNameSelect: [],
    setNameOptions: [] as any[],
    taskSetDataList: [],
    setNameIdMap: {},
    setNameTypeMap: {},
    check_streamTypesList: [],

    editFlag: false,
    taskModeTab: '0',
    isRouterAlive: false,
    taskId: '',
    setIdList: [] as any[],

    loading: false,
    workflowId: '',
    workflowIds: [] as any[],
    limit: 1000,
  })

  // const formRef = ref()

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'
  const formTask = ref(
    {
      userName, // 登陆用户
      taskName: '',
      taskType: '1',
      taskMode: '0',
      auto_enable: '0',
      sceneType: '无',
      setId: '-1',
      setIds: '',
      setType: '',
      setName: '',
      taskContent: '',
      env: 'sit',
      clusterType: 's1_online',
      workflowName: '',
      version: '',
      check_streamTypes: [],
    },
  )

  const router = useRouter()

  function generateTreeData(data, currentPath = '$') {
    const result = []
    Object.keys(data).forEach(key => {
      const value = data[key]
      if (typeof value === 'object' && value !== null) {
        if (/^[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?$/.test(key)) {
          result.push({
            key: `${currentPath}[${key}]`,
            title: `${key}`,
            children: generateTreeData(value, `${currentPath}[${key}]`),
          })
        } else {
          result.push({
            key: `${currentPath}.${key}`,
            title: `${key}`,
            children: generateTreeData(value, `${currentPath}.${key}`),
          })
        }
      } else if (typeof value !== 'object') {
        if (/^[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?$/.test(key)) {
          result.push({
            key: `${currentPath}[${key}]`,
            title: `${key}`,
            children: undefined,
          })
        } else {
          result.push({
            key: `${currentPath}.${key}`,
            title: `${key}`,
            children: undefined,
          })
        }
      }
    })
    return result
  }

  async function queryTaskField() {
    paramDrawer.value.loading = true
    try {
      const request = {
        env: formTask.value.env ? formTask.value.env : 'sit',
        clusterType: formTask.value.clusterType ? formTask.value.clusterType : 's1_online',
      }
      if (localStorage.getItem('editFlag') === 'edit') {
        request.taskId = state.taskId
      }
      const payload = {
        request,
      }
      const resData = await getTaskReportDefaultTaskField(payload).then(res => res.data)
      const resUrl = resData.url
      // Create array of promises for all API calls
      const promises = paramDrawer.value.workflowIds.map(key => {
        const redprocessUrl = `${resUrl}${key}`
        return axios.get(redprocessUrl)
      })

      // Wait for all API calls to complete
      const responses = await Promise.all(promises)

      // Create a new array for tree data
      const newTreeData = []
      const newTreeDataChecked = []

      // Process all responses
      responses.forEach(res => {
        Object.keys(res.data.tasks).forEach(key => {
          if (!newTreeData.map(item => item.title).includes(res.data.tasks[key].referenceTaskName)) {
            newTreeData.push({
              key: `$.tasks[?(@.referenceTaskName=="${res.data.tasks[key].referenceTaskName}")]`,
              title: res.data.tasks[key].referenceTaskName,
              children: generateTreeData(res.data.tasks[key], `$.tasks[?(@.referenceTaskName=="${res.data.tasks[key].referenceTaskName}")]`),
            })
          }
        })
      })

      // Replace the entire array at once to ensure reactivity
      treeData.value = [...newTreeData]

      const newTasksJsonPath = resData.tasksJsonPath
      Object.keys(newTasksJsonPath).forEach(referenceTaskName => {
        if (treeData.value.map(item => item.title).includes(referenceTaskName)) {
          Object.values(newTasksJsonPath[referenceTaskName]).forEach(value => {
            newTreeDataChecked.push(value.split(',')[0])
          })
        }
      })
      paramDrawer.value.tasksJsonPath = { ...newTasksJsonPath }
      // console.log('paramDrawer.value.tasksJsonPath', paramDrawer.value.tasksJsonPath)
      treeDataChecked.value = [...newTreeDataChecked]
      // console.log('treeData', treeData.value)
    } catch (error) {
      console.error('Error loading workflow data:', error)
    } finally {
      paramDrawer.value.loading = false
    }
  }

  function isSubstring(sub: string, strList: string[]) {
    return strList.some(str => str !== sub && str.includes(sub))
  }

  function getTaskJsonPath(jsonPath: string, type: string) {
    if (type === 'add') {
      let isExist = false
      Object.keys(paramDrawer.value.tasksJsonPath).forEach(referenceTaskName => {
        Object.keys(paramDrawer.value.tasksJsonPath[referenceTaskName]).forEach(key => {
          if (paramDrawer.value.tasksJsonPath[referenceTaskName][key].split(',')[0] === jsonPath) {
            if (!Object.prototype.hasOwnProperty.call(tasksJsonPath.value, referenceTaskName)) {
              tasksJsonPath.value[referenceTaskName] = {}
            }
            tasksJsonPath.value[referenceTaskName][key] = paramDrawer.value.tasksJsonPath[referenceTaskName][key]
            tasksJsonPathKeys.value.push(key)
            isExist = true
          }
        })
      })
      if (isExist) {
        return
      }
      // console.log(jsonPath, 'is not in paramDrawer.value.tasksJsonPath')
      // 修复正则表达式 - 正确匹配 referenceTaskName=="xxx" 模式
      const match = jsonPath.match(/referenceTaskName=="([^"]*)"/)
      if (!match) {
        console.error(`Could not extract referenceTaskName from ${jsonPath}`)
        return // 跳过此项
      }
      const referenceTaskName = match[1]
      const key = jsonPath.split('.')[jsonPath.split('.').length - 1]
      if (!Object.prototype.hasOwnProperty.call(tasksJsonPath.value, referenceTaskName)) {
        tasksJsonPath.value[referenceTaskName] = {}
      }
      if (tasksJsonPathKeys.value.includes(key)) {
        let idx = 1
        let prefix = jsonPath.split('.')[jsonPath.split('.').length - 1 - idx]
        while (tasksJsonPathKeys.value.includes(`${prefix}.${key}`)) {
          idx += 1
          if (jsonPath.split('.').length - 1 - idx < 0) {
            break
          }
          prefix = jsonPath.split('.')[jsonPath.split('.').length - 1 - idx]
        }
        if (jsonPath.split('.').length - 1 - idx >= 0) {
          tasksJsonPath.value[referenceTaskName][`${prefix}.${key}`] = jsonPath
          tasksJsonPathKeys.value.push(`${prefix}.${key}`)
        }
      } else {
        tasksJsonPath.value[referenceTaskName][key] = jsonPath
        tasksJsonPathKeys.value.push(key)
      }
    } else if (type === 'remove') {
      Object.keys(tasksJsonPath.value).forEach(referenceTaskName => {
        Object.keys(tasksJsonPath.value[referenceTaskName]).forEach(key => {
          if (tasksJsonPath.value[referenceTaskName][key].split(',')[0] === jsonPath) {
            // 删除指定key
            delete tasksJsonPath.value[referenceTaskName][key]
            // 从key数组中移除
            tasksJsonPathKeys.value.splice(tasksJsonPathKeys.value.indexOf(key), 1)
            // 如果 referenceTaskName 里已经没有别的key了，把这个referenceTaskName也移除
            if (Object.keys(tasksJsonPath.value[referenceTaskName]).length === 0) {
              delete tasksJsonPath.value[referenceTaskName]
            }
          }
        })
      })
    }
  }

  function resetDrawer() {
    // paramDrawer.value.workflowId = ''
    // paramDrawer.value.workflowIds = []
    paramDrawer.value.limit = 1000
  }

  function showDrawer() {
    resetDrawer()
    paramDrawer.value.visible = true
    paramDrawer.value.title = '收数配置'
    if (paramDrawer.value.workflowIds.length > 0) {
      queryTaskField()
    }
  }

  function addWorkflowId() {
    if (paramDrawer.value.workflowId) {
      paramDrawer.value.workflowIds.push(paramDrawer.value.workflowId)
      paramDrawer.value.workflowId = ''
    }
  }

  function closeDrawer() {
    paramDrawer.value.visible = false
    console.log('closeDrawer')
  }

  function selectNode() {
    const addList: string[] = []
    const removeList: string[] = []
    let tmp: string[] = []
    treeDataChecked.value.forEach(item => {
      if (!treeDataCheckedLast.value.includes(item)) {
        addList.push(item)
        tmp.push(item)
      }
    })
    tmp.forEach(item => {
      if (isSubstring(item, tmp)) {
        addList.splice(addList.indexOf(item), 1)
      }
    })
    tmp = []
    treeDataCheckedLast.value.forEach(item => {
      if (!treeDataChecked.value.includes(item)) {
        removeList.push(item)
        tmp.push(item)
      }
    })
    tmp.forEach(item => {
      if (isSubstring(item, tmp)) {
        removeList.splice(removeList.indexOf(item), 1)
      }
    })
    // console.log('addList', addList)
    // console.log('removeList', removeList)
    addList.forEach(item => {
      getTaskJsonPath(item, 'add')
    })
    removeList.forEach(item => {
      getTaskJsonPath(item, 'remove')
    })
    // console.log('tasksJsonPath', tasksJsonPath.value)
    // console.log('paramDrawer.value.tasksJsonPath', paramDrawer.value.tasksJsonPath)
    treeDataCheckedLast.value = treeDataChecked.value
  }

  function removeField(removedField: any) {
    const removedKey = removedField.path
    treeDataChecked.value = treeDataChecked.value.filter(k => k !== removedKey)
    treeKey.value += 1
  }

  function setDefaultTaskField() {
    paramDrawer.value.loading = true
    const payload = {
      request: {
        tasksJsonPath: tasksJsonPath.value,
      },
    }
    paramDrawer.value.loading = false
    setTaskReportDefaultTaskField(payload).then(res => {
      if (res.status !== 0) {
        toast.danger(res.message)
      } else {
        toast.success('设置默认任务字段成功')
      }
    })
  }

  function notify() {
    toast.danger('请先选择服务类型！')
  }

  function clearSet() {
    state.setNameSelect = []
    state.setNameOptions = []
    state.taskSetDataList = []
    state.setNameIdMap = {}
    state.setNameTypeMap = {}
    state.setIdList = []
  }

  function fetchJobset(param) {
    // console.log('ninghuanjun----creattask---param---', param)
    const queryparam = {}
    if (param !== '') queryparam.setName = param
    const payload = {
      request: {
        queryparam,
      },
    }

    state.taskSetDataList = []
    // 判断来源
    // console.log('ninghuanjun-----来源是---', formTask.value.taskType)
    if (typeof formTask.value.taskType === 'undefined' || formTask.value.taskType == null || formTask.value.taskType === '') {
      notify()
      return
    }
    /// ///////////获取素材任务////////
    if (isIndexOfArray(CvtaskType, String(formTask.value.taskType))) {
      // 请求cv接口
      getCVTaskSet(payload)
        .then(res => {
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.taskSetDataList = res.data
            // console.log('ninghuanjun-----发请求--', state.taskSetDataList)
            if (state.taskSetDataList.length !== 0) {
              state.setNameOptions = []
              state.setIdList = []
              // state.setNameOptions = Array.from(state.taskSetDataList, item => item.setName)
              state.taskSetDataList.forEach(item => {
                state.setNameIdMap[item.setName] = item.id
                state.setNameTypeMap[item.setName] = item.setType
                state.setNameOptions.push({
                  label: item.setName,
                  value: item.setName,
                })
                state.setIdList.push({
                  label: item.id.toString(),
                  value: item.id.toString(),
                })
              })
            }
          }
          loading.value = false
        })
    }

    if (isIndexOfArray(AvtaskType, String(formTask.value.taskType))) {
      getTaskSet(payload)
        .then(res => {
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.taskSetDataList = res.data
            if (state.taskSetDataList.length !== 0) {
              state.setNameOptions = []
              state.setIdList = []
              console.log('查询素材集', state.taskSetDataList)

              state.taskSetDataList.forEach(item => {
                state.setNameIdMap[item.setName] = item.id
                state.setNameTypeMap[item.setName] = item.setType
                if (formTask.value.sceneType === '无') {
                  if (formTask.value.env === 'sit' && item.setEnv === 'sit') {
                    state.setNameOptions.push({
                      label: item.setName,
                      value: item.setName,
                    })
                    state.setIdList.push({
                      label: item.id.toString(),
                      value: item.id.toString(),
                    })
                  } else if (formTask.value.env === 'prod' && (item.setEnv === 'online' || item.setEnv === 'prod')) {
                    state.setNameOptions.push({
                      label: item.setName,
                      value: item.setName,
                    })
                    state.setIdList.push({
                      label: item.id.toString(),
                      value: item.id.toString(),
                    })
                  }
                } else if (formTask.value.sceneType === '海外' && item.sceneType === '海外') {
                  state.setNameOptions.push({
                    label: item.setName,
                    value: item.setName,
                  })
                  state.setIdList.push({
                    label: item.id.toString(),
                    value: item.id.toString(),
                  })
                }
              })
            }
          }
          loading.value = false

          // console.log('编辑进来后的option', state.setNameOptions)
        })
    }
  }

  const filter = useDebounce(
    filterValue => {
      if (filterValue) {
        loading.value = true
        fetchJobset(filterValue)
      }
    },
    { delay: 300 },
  )

  // 更新setIds
  function updateSetIds() {
    // console.log('updateSetIds', state.setNameSelect)
    return state.setNameSelect
      .map(name => state.setNameIdMap[name as keyof typeof state.setNameIdMap])
      .filter(id => id !== undefined) // 确保 id 存在
      .join(',') // 用逗号分隔,
  }
  // 更新setType
  function updateSetType() {
    const types = state.setNameSelect
      .map(name => state.setNameTypeMap[name as keyof typeof state.setNameTypeMap])
      .filter(type => type !== undefined) // 确保 type 存在
    
    if (types.length === 0) {
      return '1'
    }
    
    // 检查是否所有类型都一致
    const uniqueTypes = [...new Set(types)]
    if (uniqueTypes.length > 1) {
      toast.warning(`选中的素材类型不一致：${uniqueTypes.join(', ')}，将使用第一个类型：${types[0]}`)
    }
    
    return types[0] // 取第一个值
  }

  function fetchSetID() {
    nextTick(() => {
      formTask.value.setIds = updateSetIds()
      formTask.value.setType = updateSetType()
    })
    console.log('formTask.value.setType', formTask.value.setType)
    console.log('formTask.value.setIds', formTask.value.setIds)
    // formTask.value.setType = String(state.setNameTypeMap[state.setNameSelect as keyof typeof state.setNameTypeMap])
    // formTask.value.setType = String(state.setNameTypeMap[state.setNameSelect as keyof typeof state.setNameTypeMap])
    // console.log('ninghuanjun----setName选中', state.setNameSelect, typeof (formTask.value.setId), JSON.stringify(state.setIdList))
  }

  function handleSubmit(v) {
    console.log('提交', v.taskType)

    if (v.taskType === '7') {
      v.taskMode = '0'
      v.auto_enable = '0'
      v.sceneType = '无'
      v.taskContent = paramDrawer.value.workflowIds.join(',')

      jsonData.value.name = v.workflowName
      jsonData.value.version = v.version
    }

    console.log('提交', jsonData.value)

    const paraJson = {
      check_streamTypes: v.check_streamTypes,
      requestinner: jsonData.value,
    }

    const taskPara = {}

    if (v.taskMode === '1') {
      Object.assign(taskPara, {})
    } else {
      Object.assign(taskPara, {
        env: v.env,
        clusterType: v.clusterType,
        setName: v.setName ? v.setName.filter(name => name !== '').join(',') : '',
        setId: v.setId,
        setIds: updateSetIds(),
        setType: updateSetType(),
        paraJson,
      })
    }

    const payload = {
      request: {
        userName: v.userName,
        taskName: v.taskName,
        taskType: v.taskType,
        taskContent: v.taskContent,
        taskMode: v.taskMode,
        auto_enable: v.auto_enable,
        sceneType: v.sceneType,
        taskPara,
        tasksJsonPath: tasksJsonPath.value ? tasksJsonPath.value : '{}',
      },
    }

    // 创建
    console.log('检查创建标志:', !state.editFlag)
    if (!state.editFlag) {
      console.log('进入创建逻辑')
      createTask(payload)
        .then(res => {
          console.log('创建请求响应:', res)
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            toast.success(res.message)
            // 根据来源页面决定跳转路径
            const returnToPage = localStorage.getItem('returnToPage')
            if (returnToPage === 'EditMergeTask') {
              const mergeTaskId = localStorage.getItem('mergeTaskId')
              // 返回到EditMergeTask页面
              router.push({
                name: 'EditMergeTask',
                params: {
                  id: mergeTaskId,
                },
              })
              localStorage.removeItem('returnToPage') // 清除标识
              localStorage.removeItem('mergeTaskId') // 清除合并任务id
            } else {
              // 默认跳转到任务列表页面
              router.push({
                name: 'TaskList',
                params: {
                  taskMode: v.taskMode,
                },
              })
            }
          }
          jsonData.value = {}
        })
    }
    // 编辑
    console.log('检查编辑标志:', state.editFlag)
    if (state.editFlag) {
      console.log('进入编辑逻辑')
      payload.request.taskId = state.taskId

      // console.log('ninghuanjun-1212--request--', payload)
      // return

      updateTask(payload).then(res => {
        console.log('编辑请求响应:', res)
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          // 根据来源页面决定跳转路径
          const returnToPage = localStorage.getItem('returnToPage')
          console.log('编辑完成后检查returnToPage:', returnToPage)
          if (returnToPage === 'EditMergeTask') {
            console.log('跳转到EditMergeTask页面')
            const mergeTaskId = localStorage.getItem('mergeTaskId')
            console.log('获取到mergeTaskId:', mergeTaskId)
            // 返回到EditMergeTask页面
            router.push({
              name: 'EditMergeTask',
              params: {
                id: mergeTaskId,
              },
            })
            localStorage.removeItem('returnToPage') // 清除标识
            localStorage.removeItem('mergeTaskId') // 清除合并任务id
          } else {
            console.log('跳转到TaskList页面')
            // 默认跳转到任务列表页面
            router.push({
              name: 'TaskList',
              params: {
                taskMode: v.taskMode,
              },
            })
          }
        }
        jsonData.value = {}
      })
    }

    // 删除缓存
    if (localStorage.getItem('createTaskParamData')) {
      localStorage.removeItem('createTaskParamData')
    }
    // 注意：returnToPage 和 mergeTaskId 在跳转后会被清除，这里不需要清除
  }

  const changecheck_streamTypes = (params: LabelModel[]) => { // 标签值改变
    formTask.value.check_streamTypes = params.map(item => item.name)
    console.log('formTask.value.check_streamTypes---', formTask.value.check_streamTypes)
  }

  const onValidateTag = (params: number) => { // 标签验证方法
    if (params === VALIDATE.LIMIT) {
      toast.danger('码流数量超出限制')
    } else if (params === VALIDATE.REG) {
      toast.danger('码流内容不符合规则')
    } else if (params === VALIDATE.REPEAT) {
      toast.danger('码流存在重复')
    }
  }

  function getTemplateJson() {
    // console.log('选择111---', formTask.value.taskType)
    const payload = {
      request: {
        taskType: formTask.value.taskType,
      },
    }
    // console.log('判断标签内容是否重复---', payload)
    // if(formTask.value.taskType ){
    if (typeof formTask.value.taskType !== 'undefined' && formTask.value.taskType !== null && formTask.value.taskType !== '') {
      templateMake(payload)
        .then(res => {
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            jsonData.value = res.data
          }
        })
    }
  }

  // TaskCreate
  onMounted(() => {
    console.log('edit接收---taskcreate--', localStorage.getItem('createTaskParamData'))
    if (localStorage.getItem('createTaskParamData') && localStorage.getItem('editFlag') === 'edit') {
      state.editFlag = true
      state.isRouterAlive = true

      // 编辑数据回显
      Object.assign(formTask.value, {})
      const createTaskParamData = JSON.parse(localStorage.getItem('createTaskParamData'))
      state.taskId = createTaskParamData.id

      formTask.value.taskName = createTaskParamData.taskName.toString()
      formTask.value.taskMode = createTaskParamData.taskMode.toString()
      formTask.value.taskType = createTaskParamData.taskType.toString()
      formTask.value.taskContent = createTaskParamData.taskContent.toString()
      formTask.value.env = createTaskParamData.env.toString()
      formTask.value.clusterType = createTaskParamData.clusterType.toString()
      formTask.value.auto_enable = createTaskParamData.auto_enable.toString()
      formTask.value.sceneType = createTaskParamData.sceneType.toString()

      state.setNameSelect = createTaskParamData.setName.toString()
      formTask.value.setType = createTaskParamData.setType.toString()
      formTask.value.setIds = createTaskParamData.setIds.toString()
      // fetchJobset(state.setNameSelect) // select回显
      formTask.value.setId = createTaskParamData.setId.toString()
      if (createTaskParamData.paraJson.check_streamTypes) formTask.value.check_streamTypes = createTaskParamData.paraJson.check_streamTypes
      jsonData.value = createTaskParamData.paraJson.requestinner
      paramDrawer.value.workflowIds = createTaskParamData.taskContent.split(',')
      formTask.value.workflowName = jsonData.value.name.toString()
      formTask.value.version = jsonData.value.version.toString()

      console.log('edit接收-----', formTask.value.setType, typeof formTask.value.setType)
    } else if (localStorage.getItem('createTaskParamData') && localStorage.getItem('editFlag') === 'copy') {
      state.editFlag = false
      state.isRouterAlive = true
      Object.assign(formTask.value, {})
      const createTaskParamData = JSON.parse(localStorage.getItem('createTaskParamData'))
      // state.taskId = createTaskParamData.id

      formTask.value.taskName = `${createTaskParamData.taskName.toString()} Copy`
      formTask.value.taskMode = createTaskParamData.taskMode.toString()
      formTask.value.taskType = createTaskParamData.taskType.toString()
      formTask.value.taskContent = createTaskParamData.taskContent.toString()
      formTask.value.env = createTaskParamData.env.toString()
      formTask.value.clusterType = createTaskParamData.clusterType.toString()
      formTask.value.auto_enable = createTaskParamData.auto_enable.toString()
      formTask.value.sceneType = createTaskParamData.sceneType.toString()

      state.setNameSelect = createTaskParamData.setName.toString()
      formTask.value.setType = createTaskParamData.setType.toString()
      formTask.value.setIds = createTaskParamData.setIds.toString()
      // fetchJobset(state.setNameSelect) // select回显
      formTask.value.setId = createTaskParamData.setId.toString()
      if (createTaskParamData.paraJson.check_streamTypes) formTask.value.check_streamTypes = createTaskParamData.paraJson.check_streamTypes
      jsonData.value = createTaskParamData.paraJson.requestinner
      paramDrawer.value.workflowIds = createTaskParamData.taskContent.split(',')
      formTask.value.workflowName = jsonData.value.name.toString()
      formTask.value.version = jsonData.value.version.toString()

      console.log('copy接收-----', formTask.value.setType, typeof formTask.value.setType)
    } else {
      state.isRouterAlive = true
      getTemplateJson()
    }

    // localStorage.getItem('createTaskParamData')
    // if (JSON.stringify(route.params) !== '{}') {
    //   state.editFlag = true
    //   state.isRouterAlive = true

    //   // 编辑数据回显
    //   Object.assign(formTask.value, {})

    //   formTask.value.taskName = route.params.taskName.toString()
    //   formTask.value.taskMode = route.params.taskMode.toString()
    //   formTask.value.taskType = route.params.taskType.toString()
    //   formTask.value.taskContent = route.params.taskContent.toString()
    //   formTask.value.env = route.params.env.toString()
    //   // formTask.value.setName = route.params.setName
    //   state.setNameSelect = route.params.setName.toString()
    //   fetchJobset(state.setNameSelect) // select回显
    //   formTask.value.setId = route.params.setId.toString()
    //   formTask.value.setType = route.params.setType.toString()
    //   if (route.params.check_streamTypes) formTask.value.check_streamTypes = JSON.parse(route.params.check_streamTypes)
    //   jsonData.value = JSON.parse(route.params.requestinner)

    //   console.log('edit接收-----', formTask.value.taskName, formTask.value.taskMode, formTask.value.taskType, formTask.value.taskContent)
    // } else {
    //   state.isRouterAlive = true
    // }
  })

</script>
