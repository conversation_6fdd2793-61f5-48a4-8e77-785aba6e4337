<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >任务ID:</Text>
      <Input
        v-model="state.juheTaskId"
        :style="{ width: '90px', }"
      />
      <Text
        bold
        style="margin-left:10px"
      >任务名称:</Text>
      <Input
        v-model="state.taskName"
        :style="{ width: '120px' ,}"
      />
      <Text
        bold
        style="margin-left:10px"
      >执行人:</Text>
      <Input
        v-model="state.userName"
        :style="{ width: '120px' ,}"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchMergeTaskRecord()"
      >查询</Button>
      <Button
        style="float:right;margin:10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <Table
      :columns="mergetaskColumns"
      :data-source="state.taskrecordSource"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #resultCsv="{ data }">
        <Tooltip>
          <Tag
            color="blue"
            size="small"
            @click="copyUrl(data)"
          >resultCsv</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #status="{ data }">
        <Tag
          v-if="data === 2"
          theme="solid"
          color="green"
          size="small"
        >成功</Tag>
        <Tag
          v-if="data === -1"
          theme="solid"
          color="orange"
          size="small"
        >失败</Tag>
        <Tag
          v-if="data === 1"
          theme="solid"
          color="blue"
          size="small"
        >运行中</Tag>

      </template>

      <template #operation="{ rowData }">
        <Button
          v-if="rowData.status!==-1"
          type="primary"
          size="small"
          style="backgroundColor:#2d8cf0;color:white;float:right;margin:0px;"

          @click="mergetaskLog(rowData)"
        >报告</Button>

      </template>
    </Table>
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <TaskRecordModal
    :is-show="modalVisible"
    :visible="modalVisible"
    :resdata="resdata"
    @handleVisible="handleVisible"
  />

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import {
    Space, Table, Text, Button, toast, Pagination, Input, Tag, Tooltip,
  } from '@xhs/delight'
  import {
    Search, Clear,
    //  Delete,Notes,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'
  import { useRouter } from 'vue-router'
  import { getMergeTaskRecordList } from '../../services/task'
  import TaskRecordModal from './components/TaskRecordModal.vue'

  const router = useRouter()
  // const route = useRoute()

  const mergetaskColumns = [
    {
      title: '执行id',
      dataIndex: 'id',
    },
    {
      title: '任务id',
      dataIndex: 'juheTaskId',
    },
    {
      title: 'resultCsv',
      dataIndex: 'resultCsv',
    },
    {
      title: '任务名称',
      dataIndex: 'juheTaskName',
    },
    {
      title: '执行时间',
      dataIndex: 'dateTime',
    },
    {
      title: '执行状态',
      dataIndex: 'status',
    },
    {
      title: '成功数',
      dataIndex: 'successNum',
    },
    {
      title: '失败数',
      dataIndex: 'failedNum',
    },
    {
      title: '运行中数',
      dataIndex: 'runningNum',
    },
    {
      title: '执行人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]
  const loading = ref(false)
  const modalVisible = ref(false)
  const resdata = ref({})

  const state = reactive({
    juheTaskId: '',
    taskName: '',
    userName: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskrecordSource: [],
    filtersetName: '',

  })

  // 父组件函数
  const handleVisible = e => {
    modalVisible.value = e
  }

  function searchMergeTaskRecord() {
    loading.value = true
    const queryparam = {}
    if (state.juheTaskId !== '') queryparam.juheTaskId = state.juheTaskId
    if (state.taskName !== '') queryparam.taskName = state.taskName
    if (state.userName !== '') queryparam.userName = state.userName
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }

    state.taskrecordSource = []

    getMergeTaskRecordList(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.taskrecordSource = res.data
        }
        localStorage.removeItem('taskId')
      })
  }

  function handlePagination() {
    searchMergeTaskRecord()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchMergeTaskRecord()
  }

  function copyUrl(data:any) {
    const cInput = document.createElement('input')
    cInput.value = data
    document.body.appendChild(cInput)
    cInput.select() // 选取文本框内容
    document.execCommand('copy')
    toast.success('复制成功')
  }
  function clearParam() {
    state.taskName = ''
    state.userName = ''
    state.taskId = ''
    searchMergeTaskRecord()
  }

  function mergetaskLog(data) {
    localStorage.setItem('execParamData', JSON.stringify(data))
    // 跳转到日志详情页面
    router.push({
      name: 'MergeTaskReport',
      params: {
        id: data.id,
        ParamData: JSON.stringify(data),
      },
    })
  }

  onMounted(() => {
    if (localStorage.getItem('taskId')) {
      state.taskId = String(localStorage.getItem('taskId'))
    } else {
      state.taskId = ''
    }

    console.log('state.taskId', state.taskId, String(localStorage.getItem('taskId')))
    searchMergeTaskRecord()
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
.link-style {
  text-decoration: none;
}
</style>
