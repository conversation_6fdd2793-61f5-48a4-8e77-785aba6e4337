<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <Result
      v-if="state.isEmpty===false"
      status="empty"
      title="暂无内容"
      sub-title="请稍后再试。"
    />
    <div v-if="state.isEmpty===true">

      <NewBsBox :bs="{ ml: 20, mr: 20,}">
        <NewBsBox :bs="{ mr: 10 }">
          <BeerTitle :bs="{ mb: 16 }"> 【{{ state.id }}】报告详情 </BeerTitle>
          <BeerDivider />
          <br>
          <div style="display:flex;justify-content: center; align-items:center; text-align:center;line-height:100px ">
            <div
              id="resChart"
              style="margin: 0px;width: 500px;height: 500px;"
            />

          </div>
        </NewBsBox>
      </NewBsBox>

      <NewBsBox :bs="{ ml: 20, mr: 20,}">
        <NewBsBox
          :bs="{
            padding: 0,
            display: 'flex',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
          }"
        >
          <NewBsBox :bs="{ mr: 10 }">
            <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'flex-start'}">
              <BeerTitle :bs="{ mb: 16,mr: 10 }"> 【{{ state.id }}】执行日志详情 </BeerTitle>
              <Select
                v-model="state.ResStatus"
                :options="mergestatusOptions"
                clearable
                :style="{ width: '200px' }"
              />
              <Button
                style="margin-left:10px"
                type="primary"
                :icon="{ icon: Search }"
                @click="filterRes()"
              >查询</Button>
            </NewBsBox>
          </NewBsBox>
        </NewBsBox>
        <BeerDivider />
        <br>
        <Table
          :columns="taskResColumns"
          :data-source="state.taskResSource"
          :loading="loading"
          size="small"
          @selectedChange="handleSelectedChange"
          @selectedAll="handleSelectedAll"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
          <template #csvResult="{ data }">
            <a
              style="font-size:14px"
              :href="data"
              target="_blank"
            >{{ data }}</a>
          </template>

          <template #status="{ data }">
            <Tag
              v-if="data === '运行中'"
              theme="solid"
              color="blue"
              size="small"
            >运行中</Tag>
            <Tag
              v-if="data === '成功'"
              theme="solid"
              color="green"
              size="small"
            >成功</Tag>
            <Tag
              v-if="data === '失败'"
              theme="solid"
              color="red"
              size="small"
            >失败</Tag>
          </template>
          <template #operation="{ rowData }">
            <Button
              size="small"
              :style="{ backgroundColor: '#3366FF', color: 'white' }"
              @click="jumpSubTaskDetail(rowData)"
            >详情</Button>
          </template>

        </Table>
      </NewBsBox>
      <NewBsBox :bs="{ margin: 20, }">
        <Pagination
          v-model="state.pageNum"
          :total="state.dataLength"
          align="end"
          @update:modelValue="handlePagination"
          @update:pageSize="handlepageSize"
        />
      </NewBsBox>

    </div>
    <Modal
      v-model:visible="showjsonDataModal"
      :mask-closeable="logmaskCloseable"
      title="数据"
      :with-footer="false"
      :style="{ width: '800px',height:'800px' }"
    >
      <json-viewer
        :value="state.jsonData"
        copyable
      />
    </Modal>

    <ViewResultModal
      :is-show="modalVisible"
      :visible="modalVisible"
      :resdata="resdata"
      @handleVisible="handleVisible"
    />

  </Space>
</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref,
  } from 'vue'
  import {
    Table, toast, Pagination, Tag, Button, Modal, Result, Select, Text,
  } from '@xhs/delight'
  import {
    Search,
  } from '@xhs/delight/icons'
  import { NewBsBox, BeerTitle, BeerDivider } from '@xhs/yam-beer'
  import JsonViewer from 'vue-json-viewer'

  import { useRouter } from 'vue-router'
  import * as echarts from 'echarts'
  import {
    getMergeTaskReportList,
  } from '../../services/task'
  import { mergestatusOptions } from '../../utils/common'

  const router = useRouter()
  // const store = useStore()
  // const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

  const showjsonDataModal = ref(false)
  const logmaskCloseable = ref(false)
  const modalVisible = ref(false)

  const resdata = ref({})

  const loading = ref(false)

  const taskResColumns = [
    {
      title: '执行id',
      dataIndex: 'excuteid',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: 'status',
      dataIndex: 'status',
    },
    {
      title: 'csvResult',
      dataIndex: 'csvResult',
      minWidth: 500,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },
  ]

  const reportdata = reactive({
    resultsumList: [] as any[],
    resultsum: {
      total: 0,
      success: 0,
    },
  })

  const state = reactive({
    ResStatus: '',
    id: '',
    taskName: '',
    userName: '',
    taskType: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskResSource: [],
    taskId: '',
    ReportdataLength: 0,
    ParamData: {},
    viewidList: [],
    viewImg: '',
    curIndex: -1,
    viewImgCheckids: [],
    viewImgList: [],
    srcimages: [],
    isEmpty: true,

    resOption: {
      title: {
        text: '执行情况',
        subtext: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'right',
      },
      series: [
        {
          name: '成功率',
          type: 'pie',
          radius: '50%',
          data: reportdata.resultsumList,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    },

  })

  function getMergeTaskReport(params:any) {
    loading.value = true
    const query = {}
    if (state.ResStatus !== '' || typeof state.ResStatus === 'undefined') {
      query.status = state.ResStatus
    }

    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        execId: params.id ? params.id : params.execId,
        csvResult: params.resultCsv,
        query,
      },
    }

    state.taskResSource = []

    getMergeTaskReportList(payload)
      .then(res => {
        loading.value = false

        if (res.status === 0) {
          state.isEmpty = true
          state.dataLength = res.dataLength
          state.taskResSource = res.data
          reportdata.resultsum = res.resultsum
          reportdata.resultsumList = [
            { name: '成功', value: res.resultsum.success, itemStyle: { color: '#91CC75' } },
            { name: '失败', value: res.resultsum.failed, itemStyle: { color: '#EE6666' } },
            { name: '运行中', value: res.resultsum.running, itemStyle: { color: '#5470C6' } },
          ]
          state.resOption.series[0].data = reportdata.resultsumList
          state.resOption.title.subtext = `总计:${reportdata.resultsum.total} | 成功:${reportdata.resultsum.success} | 失败:${reportdata.resultsum.failed} | 运行中:${reportdata.resultsum.running}`

          const chartDom = document.getElementById('resChart')!
          const myChart = echarts.init(chartDom)
          myChart.clear()
          myChart.setOption(state.resOption)
        } else {
          toast.danger(res.message)
          if (state.ResStatus === '') {
            state.isEmpty = false
          }
        }
      })
  }

  // 跳转到子任务页面
  function jumpSubTaskDetail(rawdata) {
    console.log(rawdata)
    localStorage.setItem('subTaskDetail', JSON.stringify(rawdata))
    router.push({
      name: 'TaskRecordDetail',
      params: {
        id: rawdata.excuteid,
        ParamData: JSON.stringify(rawdata),
      },
    })
  }

  function handlePagination() {
    getMergeTaskReport(state.ParamData)
  }

  // 过滤结果整不同状态
  function filterRes() {
    /// //
    state.pageNum = 1
    getMergeTaskReport(state.ParamData)
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getMergeTaskReport(state.ParamData)
  }
  // 父组件函数
  const handleVisible = e => {
    modalVisible.value = e
  }

  onMounted(() => {
    const url = window.location.href
    const arr = url.split('/')
    state.id = arr[arr.length - 1]

    if (localStorage.getItem('execParamData')) {
      const localid = JSON.parse(localStorage.getItem('execParamData')).id.toString()
      if (state.id !== localid) {
        // 回到运行记录页
        router.push({
          name: 'MergeTaskRecord',
        })
      } else {
        state.ParamData = JSON.parse(localStorage.getItem('execParamData'))
        state.id = state.ParamData.id
        getMergeTaskReport(state.ParamData)
      }
    } else {
      // 回到运行记录页
      router.push({
        name: 'MergeTaskRecord',
      })
    }
  })

</script>

<style lang="stylus" scoped>
.imgcss{
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
}

</style>
