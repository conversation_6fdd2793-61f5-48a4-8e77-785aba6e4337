<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
      <Text
        bold
        style="margin-left:-15px;margin-right:10px"
      >任务ID:</Text>
      <Input
        v-model="state.taskId"
        :style="{ width: '90px', }"
      />
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >任务名称:</Text>
      <Input
        v-model="state.taskName"
        :style="{ width: '120px', }"
      />
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >执行状态:</Text>
      <Select
        v-model="state.taskStatusSelected"
        :options="taskRecordStatus"
        :style="{ width: '120px' }"
        clearable
      />
      <Text
        bold
        style="margin-left:10px;margin-right:10px"
      >执行人:</Text>
      <Input
        v-model="state.userName"
        :style="{ width: '120px', }"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchTaskRecord()"
      >查询</Button>
      <Button
        style="float:right;margin:10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ ml: 20, mr: 20, }">
    <Table
      :columns="taskRecordColumns"
      :data-source="state.taskrecordSource"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #taskType="{ data }">
        <Tag
          color="blue"
          size="small"
        >{{ getMapLable(taskTypeList, data) }}</Tag>
      </template>
      <template #csvUrl="{ data }">
        <Tooltip>
          <Tag
            color="blue"
            size="small"
            @click="copyUrl(data)"
          >csvUrl</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #status="{ data }">
        <!-- 将 v-if 提升至外层容器或用计算属性过滤数据 -->
        <template
          v-for="item in taskRecordStatusOptions"
          :key="item.value"
        >
          <Tag
            v-if="data === item.value"
            theme="solid"
            :color="item.color"
            size="small"
          >{{ item.label }}</Tag>
        </template>
      </template>
      <template #env="{ data }">
        <Tag
          color="green"
          size="small"
        >{{ data }}</Tag>

      </template>
      <template #setType="{ data }">
        <Tag
          color="teal"
          size="small"
        >{{ getMapLable(setTypeList, data) }}</Tag>
      </template>

      <template #operation="{ rowData }">
        <Space
          type="flex"
          justify="end"
          :size="8"
          style="width: 100%;"
        >
          <Button
            v-if="rowData.videoResultUrl"
            type="primary"
            size="small"
            style="background-color:#28BC37; color:white; padding-left:5px; padding-right:5px;"
            @click="videoResult(rowData)"
          >结果</Button>
          <Button
            v-if="rowData.status === '成功' || +rowData.status !== 0"
            type="primary"
            size="small"
            style="background-color:#28BC77; color:white; padding-left:5px; padding-right:5px;"
            @click="jsonDetail(rowData)"
          >详情</Button>
          <Button
            v-if="rowData.status === '成功' || +rowData.status !== 0"
            type="primary"
            size="small"
            style="background-color:#2d8cf0; color:white; padding-left:5px; padding-right:5px;"
            @click="taskLog(rowData)"
          >报告</Button>
        </Space>
      </template>

    </Table>
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      :page-size-options="pageSizeOptions"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <TaskRecordModal
    :is-show="modalVisible"
    :visible="modalVisible"
    :resdata="resdata"
    @handleVisible="handleVisible"
  />

  <Modal
    v-model:visible="showjsonDataModal"
    :mask-closeable="maskCloseable"
    title="数据"
    :with-footer="false"
    :style="{ width: '800px', height: '800px' }"
  >
    <json-viewer
      :value="state.jsonData"
      copyable
    />
  </Modal>

</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import {
    Space, Table, Text, Button, toast, Pagination, Input, Tag, Tooltip, Modal, Select,
  } from '@xhs/delight'
  import {
    Search, Clear,
    //  Delete,Notes,
  } from '@xhs/delight/icons'
  import JsonViewer from 'vue-json-viewer'
  import { NewBsBox } from '@xhs/yam-beer'
  // import { useRouter } from 'vue-router'
  import {
    getTaskRecordList, getTaskRecordResult, getVideoResult,
  } from '../../services/task'
  import {
    taskTypeList, getMapLable, setTypeList, taskRecordStatusOptions, pageSizeOptions,
  } from '../../utils/common'
  import TaskRecordModal from './components/TaskRecordModal.vue'

  // const router = useRouter()
  // const route = useRoute()

  const taskRecordStatus = [
    {
      label: '等待',
      value: '等待',
    },
    {
      label: '执行中',
      value: '执行中',
    },
    {
      label: '查询中',
      value: '查询中',
    },
    {
      label: '执行成功',
      value: '执行成功',
    },
    {
      label: '执行失败',
      value: '执行失败',
    },
    {
      label: '收数中',
      value: '收数中',
    },
    {
      label: '收数成功',
      value: '收数成功',
    },
    {
      label: '收数失败',
      value: '收数失败',
    },
  ]

  const taskRecordColumns = [
    {
      title: '执行id',
      dataIndex: 'id',
      minWidth: 70,
    },
    {
      title: '任务id',
      dataIndex: 'taskId',
      minWidth: 70,
    },
    {
      title: '素材id',
      dataIndex: 'setIds',
      minWidth: 70,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
    },
    {
      title: '素材集名称',
      dataIndex: 'setName',
    },
    {
      title: 'setType',
      dataIndex: 'setType',
      minWidth: 80,
    },
    {
      title: '执行时间',
      dataIndex: 'currentTime',
    },
    {
      title: '执行状态',
      dataIndex: 'status',
    },
    {
      title: 'csvUrl',
      dataIndex: 'csvUrl',
    },
    {
      title: '环境',
      dataIndex: 'env',
      minWidth: 60,
    },
    {
      title: '执行人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]
  const loading = ref(false)
  const modalVisible = ref(false)
  const resdata = ref({})

  const state = reactive({
    taskId: '',
    taskName: '',
    userName: '',
    taskStatusSelected: '',
    taskType: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskrecordSource: [],
    filtersetName: '',
    jsonData: {},

  })

  // 父组件函数
  const handleVisible = e => {
    modalVisible.value = e
  }

  const showjsonDataModal = ref(false)
  const maskCloseable = ref(false)

  function getXlsxUrls(data: any) {
    const payload = {
      request: {
        xlsxNames: data,
      },
    }
    return getTaskRecordResult(payload) // 添加 return 以便返回 Promise
      .then(res => res.data)
  }

  async function jsonDetail(data: any) {
    showjsonDataModal.value = true
    try {
      const urls = await getXlsxUrls(data.xlsxNames) // 使用 await 等待 Promise 完成
      data.xlsxNames = urls
      console.log(urls)
    } catch (error) {
      console.error('Failed to get XLSX URLs:', error)
    }
    state.jsonData = data
    maskCloseable.value = true
  }

  async function getVideoResultUrl(execId) {
    const payload = {
      request: {
        execId,
      },
    }
    return getVideoResult(payload)
      .then(res => {
        if (res.status === 0) {
          return res.data.result_url
        }
        return ''
      })
  }

  function searchTaskRecord() {
    loading.value = true
    const queryparam = {}
    if (state.taskId !== '') queryparam.taskId = state.taskId
    if (state.taskName !== '') queryparam.taskName = state.taskName
    if (state.userName !== '') queryparam.userName = state.userName
    if (state.taskStatusSelected !== '') {
      queryparam.status = []
      taskRecordStatusOptions.forEach(item => {
        if (item.label === state.taskStatusSelected) {
          queryparam.status.push(item.value)
        }
      })
    }
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        queryparam,
      },
    }

    state.taskrecordSource = []

    getTaskRecordList(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else { // 首先处理基本数据
          // console.log('res.data', res.data)
          state.dataLength = res.dataLength
          state.taskrecordSource = res.data.map(item => {
            item.setIds = item.setIds === '' ? item.setId : item.setIds
            return item
          })

          // 创建Promise数组
          const promises = state.taskrecordSource.map(async item => {
            const url = await getVideoResultUrl(item.id)
            item.videoResultUrl = url
            return item
          })

          // 等待所有Promise完成后更新状态
          Promise.all(promises).then(() => {
            console.log('state.taskrecordSource', state.taskrecordSource)
          })
        }
        localStorage.removeItem('taskId')
      })
  }

  function handlePagination() {
    searchTaskRecord()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchTaskRecord()
  }

  function copyUrl(data: any) {
    const cInput = document.createElement('input')
    cInput.value = data
    document.body.appendChild(cInput)
    cInput.select() // 选取文本框内容
    document.execCommand('copy')
    toast.success('复制成功')
  }
  function clearParam() {
    state.taskName = ''
    state.userName = ''
    state.taskId = ''
    searchTaskRecord()
  }

  function videoResult(data) {
    window.open(data.videoResultUrl, '_blank')
  }

  function taskLog(data) {
    localStorage.setItem('execParamData', JSON.stringify(data))
    // 根据本地环境区分使用不同地址
    // 调试环境变量
    console.log('import.meta.env:', import.meta.env)
    console.log('VITE_ENV:', (import.meta.env as any)?.VITE_ENV)
    console.log('MODE:', (import.meta.env as any)?.MODE)
    console.log('NODE_ENV:', (import.meta.env as any)?.NODE_ENV)
    
    // 尝试多种方式获取环境信息
    const env = (import.meta.env as any)?.VITE_ENV || 
                (import.meta.env as any)?.MODE || 
                (import.meta.env as any)?.NODE_ENV || 
                (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.port === '3000') ? 'dev' : 'prod'
    
    console.log('最终使用的环境:', env)
    let url = ''
    if (env === 'dev' || env === 'development') {
      url = `http://127.0.0.1:1388/task/taskrecorddetail/${encodeURIComponent(data.id)}`
    } else {
      url = `https://va.devops.sit.xiaohongshu.com/task/taskrecorddetail/${encodeURIComponent(data.id)}`
    }
    console.log('最终URL:', url)
    window.open(url, '_blank')
  }

  //   function deleteTask(data) {
  //     console.log(data)
  //   }

  onMounted(() => {
    if (localStorage.getItem('taskId')) {
      state.taskId = String(localStorage.getItem('taskId'))
    } else {
      state.taskId = ''
    }
    searchTaskRecord()
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
.link-style {
  text-decoration: none;
}
</style>
