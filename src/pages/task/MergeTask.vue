<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >任务名称:</Text>
      <Input
        v-model="state.taskMergeName"
        clearable
        :style="{ width: '120px' }"
      />
      <Text
        bold
        style="margin-left:10px"
      >创建人:</Text>
      <Input
        v-model="state.userName"
        :style="{ width: '120px' ,}"
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchMergeTask()"
      >查询</Button>
      <Button
        style="float:right;margin:10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>

    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <Table
      :columns="taskColumns"
      :data-source="state.taskdataSource"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #testCaseCount="{ rowData }">
        <Tooltip
          v-if="rowData.subTaskDetailList && rowData.subTaskDetailList.length > 0"
          theme="light"
        >
          <template #content>
            <div style="max-width: 300px; color: #333;">
              <div style="font-weight: 600; margin-bottom: 8px; color: #1890ff;">
                用例列表 ({{ rowData.subTaskDetailList.length }}个)
              </div>
              <div v-for="(task, index) in rowData.subTaskDetailList" :key="task.id" style="margin-bottom: 4px; padding: 4px 0; border-bottom: 1px solid #f0f0f0;">
                <div style="font-weight: 500; color: #333;">{{ index + 1 }}. {{ task.taskName }}</div>
                <div style="font-size: 12px; color: #666; margin-top: 2px;">ID: {{ task.id }}</div>
              </div>
            </div>
          </template>
          <Text style="color: #1890ff; font-weight: 600; cursor: pointer; text-decoration: underline;">
            {{ rowData.subTaskDetailList.length }}
          </Text>
        </Tooltip>
        <Text v-else style="color: #999;">
          0
        </Text>
      </template>

      <template #operation="{ rowData }">
        <Button
          size="small"
          style="backgroundColor:#28BC77;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
          type="primary"
          @click="showExecuteConfirm(rowData)"
        >一键执行</Button>
        <Button
          size="small"
          style="backgroundColor:#999966;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
          type="primary"
          @click="editMergeTask(rowData)"
        >编辑</Button>
        <Button
          size="small"
          style="backgroundColor:#FF9800;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
          type="primary"
          @click="selectMaterialSet(rowData)"
        >选择素材集</Button>
      </template>
    </Table>
  </NewBsBox>

  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <!-- 执行确认弹窗 -->
  <Modal
    v-model:visible="executeConfirmVisible"
    title="执行"
    type="info"
    :mask-closeable="true"
    @confirm="confirmExecute"
    @cancel="cancelExecute"
  >
    <Text>确认执行该用例集吗？</Text>
  </Modal>

  <!-- 素材集选择模态框 -->
  <Modal
    v-model:visible="materialSetModalVisible"
    title="选择素材集"
    :mask-closeable="false"
    :style="{ width: '800px' }"
  >
    <div style="padding: 20px 0;">
      <div style="margin-bottom: 16px;">
        <Text bold>任务名称：</Text>
        <Text>{{ currentEditTask?.taskName || '' }}</Text>
      </div>

      <!-- 聚合任务子任务素材集配置 -->
      <Text bold style="margin-bottom: 12px; display: block;">子任务素材集配置：</Text>
      <div
        v-for="subTask in subTaskMaterialSets"
        :key="subTask.id"
        style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 16px; margin-bottom: 12px; background-color: #fafafa;"
      >
        <div style="margin-bottom: 12px;">
          <Text bold>{{ subTask.taskName }}</Text>
        </div>
        <div style="margin-bottom: 8px;">
          <Text style="font-size: 12px; color: #666;">当前素材集：{{ subTask.currentMaterialSet }}</Text>
        </div>
        <div>
          <Text style="font-size: 12px; color: #666; margin-bottom: 4px; display: block;">选择新素材集：</Text>
          <Select
            v-model="subTask.selectedMaterialSets"
            :options="materialSetOptions"
            :loading="materialSetLoading"
            multiple
            filterable
            remote
            :filter="filterMaterialSets"
            placeholder="请搜索并选择素材集"
            style="width: 100%;"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; gap: 8px;">
        <Button @click="cancelMaterialSetEdit">取消</Button>
        <Button type="primary" @click="confirmMaterialSetEdit">确认替换</Button>
      </div>
    </template>
  </Modal>

</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref,
  } from 'vue'
  import {
    Space, Table, Text, Button, toast, Pagination, Input, Modal, Tooltip, Select, useDebounce,
  } from '@xhs/delight'
  import {
    Search, Clear,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'

  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import { getMergeTaskList, executeMergeTask, updateTask, getTaskList } from '../../services/task'
  import { getTaskSet, getCVTaskSet } from '../../services/material'
  import { isIndexOfArray, AvtaskType, CvtaskType } from '../../utils/common'

  //   import {
  //     taskTypeList, getMapLable,
  //   } from '../../utils/common'
  import {} from './components/TaskExecuteModal.vue'

  const router = useRouter()
  //   const route = useRoute()

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

  const loading = ref(false)
  const executeConfirmVisible = ref(false)
  const currentExecuteTask = ref(null)

  // 素材集选择相关状态
  const materialSetModalVisible = ref(false)
  const currentEditTask = ref(null)
  const materialSetOptions = ref([])
  const materialSetLoading = ref(false)
  const subTaskMaterialSets = ref([])

  const state = reactive({
    id: '',
    taskMergeName: '',
    userName: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskdataSource: [],
  })

  const taskColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '任务内容',
      dataIndex: 'content',
    },
    {
      title: '用例数量',
      dataIndex: 'testCaseCount',
    },
    {
      title: '创建时间',
      dataIndex: 'dateTime',
    },
    {
      title: '创建人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]

  function searchMergeTask() {
    loading.value = true
    const queryparam: any = {}
    const payload: any = {}
    if (state.taskMergeName !== '') queryparam.taskName = state.taskMergeName
    if (state.userName !== '') queryparam.userName = state.userName
    Object.assign(payload, { request: { pageNum: state.pageNum, pageSize: state.pageSize, queryparam } })

    state.taskdataSource = []

    getMergeTaskList(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.taskdataSource = res.data
        }
      })
  }

  function clearParam() {
    state.taskMergeName = ''
    searchMergeTask()
  }

  function handlePagination() {
    searchMergeTask()
  }

  function handlepageSize(e: any) {
    state.pageSize = e
    state.pageNum = 1
    searchMergeTask()
  }

  function mergerunTask(data: any) {
    const payload = {
      request: {
        userName,
        taskId: data.id,
      },
    }
    executeMergeTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
        }
      })
  }
  function editMergeTask(data) {
    localStorage.setItem('editMergeTaskData', JSON.stringify(data))
    // 跳转到编辑用例集页面
    router.push({
      name: 'EditMergeTask',
      params: {
        id: data.id,
        ParamData: JSON.stringify(data),
      },
    })
  }

  function showExecuteConfirm(data) {
    currentExecuteTask.value = data
    executeConfirmVisible.value = true
  }

  function confirmExecute() {
    if (currentExecuteTask.value) {
      mergerunTask(currentExecuteTask.value)
    }
    executeConfirmVisible.value = false
    currentExecuteTask.value = null
  }

  function cancelExecute() {
    executeConfirmVisible.value = false
    currentExecuteTask.value = null
  }

  // 素材集选择相关函数
  async function selectMaterialSet(data) {
    console.log('选择素材集按钮被点击-', data)

    currentEditTask.value = data
    materialSetOptions.value = []

    // 初始化子任务素材集选择状态
    await initSubTaskMaterialSets(data.subTaskDetailList)

    materialSetModalVisible.value = true

    // 自动触发一次搜索，加载可用的素材集选项
    searchMaterialSets('')
  }



  // 初始化子任务素材集选择状态
  async function initSubTaskMaterialSets(subTaskList) {
    try {
      // 首先获取子任务的完整信息以获取当前素材集
      const taskIds = subTaskList.map(task => task.id)
      const taskListPayload = {
        request: {
          pageNum: 1,
          pageSize: 1000,
          queryparam: {}
        }
      }

      console.log('获取子任务完整信息以获取当前素材集...')
      const taskListRes = await getTaskList(taskListPayload)

      if (taskListRes.status !== 0) {
        console.error('获取子任务信息失败:', taskListRes.message)
        // 如果获取失败，使用默认值
        subTaskMaterialSets.value = subTaskList.map(subTask => ({
          id: subTask.id,
          taskName: subTask.taskName,
          currentMaterialSet: '-',
          selectedMaterialSets: []
        }))
        return
      }

      // 筛选出需要的子任务信息
      const fullTaskInfos = taskListRes.data.filter((task: any) => taskIds.includes(task.id))

      // 初始化子任务素材集状态，包含当前素材集信息
      subTaskMaterialSets.value = subTaskList.map(subTask => {
        const fullTaskInfo = fullTaskInfos.find((task: any) => task.id === subTask.id)
        const currentMaterialSet = fullTaskInfo?.setName || '-'

        console.log(`子任务 ${subTask.taskName} 当前素材集:`, currentMaterialSet)

        return {
          id: subTask.id,
          taskName: subTask.taskName,
          currentMaterialSet: currentMaterialSet,
          selectedMaterialSets: []
        }
      })

      console.log('初始化子任务素材集状态:', subTaskMaterialSets.value)
    } catch (error) {
      console.error('初始化子任务素材集状态失败:', error)
      // 如果出错，使用默认值
      subTaskMaterialSets.value = subTaskList.map(subTask => ({
        id: subTask.id,
        taskName: subTask.taskName,
        currentMaterialSet: '-',
        selectedMaterialSets: []
      }))
    }
  }

  // 使用防抖的过滤函数来实现远程搜索
  const filterMaterialSets = useDebounce(
    (filterValue: string) => {
      console.log('防抖过滤函数被调用，搜索关键词:', filterValue)
      if (filterValue !== undefined) {
        materialSetLoading.value = true
        searchMaterialSets(filterValue || '')
      }
    },
    { delay: 300 }
  )

  async function searchMaterialSets(query) {
    if (!currentEditTask.value) return

    materialSetLoading.value = true
    try {
      console.log('搜索素材集:', query)

      // 如果没有搜索关键词，获取所有素材集
      const queryparam: any = {}
      if (query && query.trim() !== '') {
        queryparam.setName = query.trim()
      }

      const payload = {
        request: {
          pageNum: 1,
          pageSize: 1000, // 获取更多结果用于搜索
          queryparam,
        },
      }

      console.log('搜索素材集API请求:', payload)

      // 调用真实的API获取素材集列表
      const res = await getTaskSet(payload)

      console.log('搜索素材集API响应:', res)
      console.log('返回的素材集数量:', res.data?.length)

      if (res.status !== 0) {
        toast.danger(res.message || '获取素材集失败')
        materialSetOptions.value = []
      } else {
        const taskSetDataList = res.data || []

        console.log(`搜索关键词: "${query}", 返回数据量: ${taskSetDataList.length}`)

        // 直接使用后端返回的数据，后端已经处理了搜索过滤
        materialSetOptions.value = taskSetDataList.map((item: any) => ({
          label: item.setName,
          value: item.setName,
          id: item.id,
          setType: item.setType,
          setEnv: item.setEnv,
          sceneType: item.sceneType,
        }))
        console.log('处理后的素材集选项:', materialSetOptions.value)
      }

    } catch (error) {
      console.error('搜索素材集失败:', error)
      toast.danger('搜索素材集失败，请重试')
      materialSetOptions.value = []
    } finally {
      materialSetLoading.value = false
    }
  }

  function cancelMaterialSetEdit() {
    materialSetModalVisible.value = false
    currentEditTask.value = null
    materialSetOptions.value = []
    subTaskMaterialSets.value = []
  }

  async function confirmMaterialSetEdit() {
    if (!currentEditTask.value) {
      toast.danger('任务信息丢失，请重试')
      return
    }

    // 处理聚合任务的子任务素材集更新
    await updateSubTaskMaterialSets()
  }



  // 更新子任务的素材集
  async function updateSubTaskMaterialSets() {
    try {
      // 检查是否有子任务选择了新的素材集
      const tasksToUpdate = subTaskMaterialSets.value.filter(subTask =>
        subTask.selectedMaterialSets && subTask.selectedMaterialSets.length > 0
      )

      if (tasksToUpdate.length === 0) {
        toast.warning('请为至少一个子任务选择新的素材集')
        return
      }

      console.log('需要更新的子任务:', tasksToUpdate)

      // 首先获取所有子任务的完整信息
      const taskIds = tasksToUpdate.map(task => task.id)
      const taskListPayload = {
        request: {
          pageNum: 1,
          pageSize: 1000,
          queryparam: {}
        }
      }

      console.log('获取子任务完整信息...')
      const taskListRes = await getTaskList(taskListPayload)

      if (taskListRes.status !== 0) {
        toast.danger('获取子任务信息失败')
        return
      }

      // 筛选出需要更新的任务的完整信息
      const fullTaskInfos = taskListRes.data.filter((task: any) => taskIds.includes(task.id))

      if (fullTaskInfos.length === 0) {
        toast.danger('未找到子任务的完整信息')
        return
      }

      console.log('获取到的子任务完整信息:', fullTaskInfos)

      let successCount = 0
      let failCount = 0

      // 逐个更新子任务的素材集
      for (const subTask of tasksToUpdate) {
        try {
          // 找到对应的完整任务信息
          const fullTaskInfo = fullTaskInfos.find((task: any) => task.id === subTask.id)

          if (!fullTaskInfo) {
            console.error(`未找到子任务 ${subTask.taskName} 的完整信息`)
            failCount++
            continue
          }

          // 获取选中的素材集信息
          const selectedOptions = materialSetOptions.value.filter((option: any) =>
            subTask.selectedMaterialSets.includes(option.value)
          )

          // 构建setIds和setName
          const setIds = selectedOptions.map((option: any) => option.id).join(',')
          const setNames = subTask.selectedMaterialSets.join(',')
          const setType = selectedOptions.length > 0 ? selectedOptions[0].setType : '1'

          console.log(`更新子任务 ${subTask.taskName} 的素材集:`, {
            taskId: subTask.id,
            setIds,
            setNames,
            setType,
            originalTask: fullTaskInfo
          })

          // 解析原始的paraJson
          let originalParaJson = fullTaskInfo.paraJson
          if (typeof originalParaJson === 'string') {
            originalParaJson = JSON.parse(originalParaJson)
          }

          // 构建更新子任务的payload，保持原有配置，只更新素材集
          const payload = {
            request: {
              userName,
              taskName: fullTaskInfo.taskName, // 使用原始任务名
              taskType: fullTaskInfo.taskType, // 保持原始任务类型
              taskContent: fullTaskInfo.content || fullTaskInfo.taskName, // 使用原始内容
              taskMode: fullTaskInfo.taskMode, // 保持原始任务模式！！！这是关键
              auto_enable: fullTaskInfo.auto_enable, // 保持原始自动启用设置
              sceneType: fullTaskInfo.sceneType, // 保持原始场景类型
              taskPara: {
                env: fullTaskInfo.env, // 保持原始环境
                clusterType: fullTaskInfo.clusterType, // 保持原始集群类型
                setName: setNames, // 更新素材集名称
                setId: setIds.split(',')[0], // 更新素材集ID
                setIds: setIds, // 更新素材集IDs
                setType: setType, // 更新素材集类型
                paraJson: originalParaJson, // 保持原始参数配置
              },
              tasksJsonPath: fullTaskInfo.tasksJsonPath || '{}', // 保持原始任务JSON路径
              taskId: subTask.id,
            },
          }

          console.log(`子任务 ${subTask.taskName} 更新请求:`, payload)

          // 调用更新任务的API
          const res = await updateTask(payload)

          if (res.status === 0) {
            successCount++
            console.log(`子任务 ${subTask.taskName} 更新成功`)
          } else {
            failCount++
            console.error(`子任务 ${subTask.taskName} 更新失败:`, res.message)
          }

        } catch (error) {
          failCount++
          console.error(`子任务 ${subTask.taskName} 更新异常:`, error)
        }
      }

      // 显示更新结果
      if (successCount > 0 && failCount === 0) {
        toast.success(`成功更新 ${successCount} 个子任务的素材集`)
      } else if (successCount > 0 && failCount > 0) {
        toast.warning(`成功更新 ${successCount} 个子任务，${failCount} 个子任务更新失败`)
      } else {
        toast.danger('所有子任务更新失败')
      }

      // 关闭模态框
      cancelMaterialSetEdit()

      // 刷新任务列表
      searchMergeTask()

    } catch (error) {
      console.error('更新子任务素材集失败:', error)
      toast.danger('更新子任务素材集失败，请重试')
    }
  }

  onMounted(() => {
    searchMergeTask()
  })

</script>

<style lang="stylus" scoped>

</style>


