<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }">
      <!-- <Text
        bold
        style="margin-left:-15px"
      >任务ID:</Text>
      <Input
        v-model="state.taskId"
        :style="{ width: '90px', }"
      /> -->
      <Text
        bold
        style="margin-left:10px"
      >任务名称:</Text>
      <Input
        v-model="state.taskName"
        :style="{ width: '120px', }"
      />
      <Text
        bold
        style="margin-left:10px"
      >任务类型:</Text>
      <Select
        v-model="state.taskType"
        :options="taskTypeList"
        :style="{ width: '120px' }"
        clearable
      />
      <Text
        bold
        style="margin-left:10px"
      >自动使能:</Text>
      <Select
        v-model="state.autoEnable"
        :options="autoEnableOption"
        :style="{ width: '120px' }"
        clearable
      />
      <Button
        style="float:right;margin:10px"
        type="primary"
        :icon="{ icon: Search }"
        @click="searchTask()"
      >查询</Button>
      <Button
        style="float:right;margin:10px"
        type="secondary"
        :icon="{ icon: Clear }"
        @click="clearParam"
      >重置</Button>
    </NewBsBox>

    <NewBsBox
      :bs="{ display: 'flex', boxShadow: 'none', ml: 30, alignItems: 'center', justifyContent: 'space-between', width: '100%', marginRight: '20px' }"
    >
      <!-- <Text style="margin-right:10px">查询结果共: {{ state.dataLength }}条</Text> -->
      <!-- <Checkbox
        v-model:checked="state.taskchecked"
        style="margin-right:20px"
        value="全部查询结果"
        label="全部查询结果"
        description="勾选后，通过csv创建任务素材集"
        :onUpdate:checked="changeChecked"
      /> -->
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center' }">
        <Button
          :style="{ backgroundColor: '#AA7700', color: 'white', marginLeft: '-50px', }"
          :icon="{ icon: AdjacentItem }"
          @click="handleAddMergeJob"
        >组合用例集</Button>
        <Button
          :style="{ backgroundColor: '#28BC77', color: 'white', height: '32px', marginLeft: '15px', }"
          type="primary"
          :icon="{ icon: Add }"
          @click="createTask()"
        >新建用例</Button>
        <!-- <Text>当前选中：{{ state.selectedList }}</Text>
    <br>-->
        <!-- <Text>当前选中all：{{ state.selectedAllList }}</Text> -->
      </NewBsBox>
      <NewBsBox :bs="{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }">
        <Button
          type="secondary"
          :icon="{ icon: Notes }"
          style="margin-right: 10px;"
          @click="handleAutoTasks('1')"
        >一键启动</Button>
        <Button
          type="secondary"
          :icon="{ icon: Notes }"
          style="margin-right: 30px;"
          @click="handleAutoTasks('2')"
        >一键暂停</Button>
      </NewBsBox>
    </NewBsBox>

  </Space>

  <Modal
    v-model:visible="showDebugModal"
    title="执行数量"
    :with-footer="false"
    :style="{ width: 'auto' }"
  >
    <Form @submit="handleDebugSubmit">
      <FormItem
        name="debugNum"
        label="数量"
        help="输入执行case的数量"
      >
        <InputNumber
          :model-value="1"
          required
          placeholder="输入执行case的数量"
          clearable
          :min="1"
        />
      </FormItem>
      <FormItem
        name="debugInterval"
        label="时间"
        help="case执行的时间间隔(秒)"
      >
        <InputNumber
          :model-value="0"
          required
          placeholder="默认0秒"
          clearable
          :min="0"
        />
      </FormItem>
    </Form>
  </Modal>

  <NewBsBox :bs="{ ml: 20, mr: 20, mt: -20, }">

    <Tabs
      v-model="state.taskMode"
      :onUpdate:modelValue="changeTab"
    >
      <TabPane
        id="0"
        label="简单模式"
      >
        <NewBsBox :bs="{ ml: 20, mr: 20, mt: 20, }">
          <Table
            v-model:selected="state.selectedList"
            :columns="simpletaskColumns"
            :data-source="state.simpletaskdataSource"
            :row-selection="rowSelection"
            :loading="simpleloading"
            :on-sort="handleTaskSort"
            size="small"
            @selectedChange="simplehandleSelectedChange"
            @selectedAll="simplehandleSelectedAll"
          >
            <template #td="{ data }">
              <Text ellipsis>{{ data }}</Text>
            </template>
            <template #taskType="{ data }">
              <Tag
                color="blue"
                size="small"
              >{{ getMapLable(taskTypeList, data) }}</Tag>
            </template>
            <template #auto_enable="{ data }">
              <Tag
                color="red"
                size="small"
              >{{ getMapLable(autoEnableOption, data) }}</Tag>
            </template>
            <template #env="{ data }">
              <Tag
                color="green"
                size="small"
              >{{ data }}</Tag>
            </template>
            <template #taskContent="{ data }">
              <Tooltip>
                <Text>{{ data }}</Text>
                <template #content>
                  {{ data }}
                </template>
              </Tooltip>
            </template>

            <template #operation="{ rowData }">
              <Button
                size="small"
                style="background-color:indianred;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
                type="primary"
                @click="debugSingleTask(rowData)"
              >调试</Button>
              <Popconfirm
                title="执行"
                description="确认该用例执行吗？"
                arrow
                outside-closeable
                :size="180"
                :closeable="false"
                @confirm="simplerunTask(rowData)"
              >
                <Space>
                  <Button
                    size="small"
                    style="background-color:#28BC77;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
                    type="primary"
                  >执行</Button>
                </Space>
              </Popconfirm>
              <!-- <span style="color:gray">|</span> -->
              <Button
                size="small"
                style="float:right;margin:-4px;padding-left:5px;padding-right:5px;"
                type="primary"
                @click="jumpTaskRecord(rowData)"
              >记录</Button>
              <Button
                size="small"
                style="background-color:#999966;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
                type="primary"
                @click="editTask(rowData)"
              >编辑</Button>
              <Button
                size="small"
                style="background-color:#1166CC;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
                type="primary"
                @click="copyTask(rowData)"
              >复制</Button>
              <Popconfirm
                title="删除"
                description="确认删除该任务吗？"
                @confirm="deleteSimpleTask(rowData)"
                @cancel="handleDeleteTaskClose"
              >
                <Button
                  size="small"
                  style="background-color:#f03860;color:white;float:right;margin:-4px;padding-left:5px;padding-right:5px;"
                  type="primary"
                >删除</Button>
              </Popconfirm>
            </template>
          </Table>
        </NewBsBox>
        <NewBsBox :bs="{ margin: 20, }">
          <Pagination
            v-model="state.simplepageNum"
            :total="state.simpledataLength"
            :page-size-options="pageSizeOptions"
            align="end"
            @update:modelValue="simplehandlePagination"
            @update:pageSize="simplehandlepageSize"
          />
        </NewBsBox>
      </TabPane>
      <TabPane
        id="1"
        label="专家模式"
      >

        <NewBsBox :bs="{ ml: 20, mr: 20, mt: 20, }">
          <Table
            :columns="taskColumns"
            :data-source="state.taskdataSource"
            :loading="loading"
            size="small"
            @selectedChange="handleSelectedChange"
            @selectedAll="handleSelectedAll"
          >
            <template #td="{ data }">
              <Text ellipsis>{{ data }}</Text>
            </template>
            <template #taskType="{ data }">
              <Tag
                color="blue"
                size="small"
              >{{ getMapLable(taskTypeList, data) }}</Tag>
            </template>
            <template #env="{ data }">
              <Tag
                color="green"
                size="small"
              >{{ data }}</Tag>

            </template>

            <template #operation="{ rowData }">
              <Tooltip>
                <Icon
                  class="small-hand"
                  :icon="Play"
                  color="success"
                  size="large"
                  @click="runTask(rowData)"
                />
                <template #content>
                  执行
                </template>
              </Tooltip>
              <span style="color:gray">|</span>
              <Tooltip>
                <Icon
                  class="small-hand"
                  :icon="Notes"
                  color="info"
                  size="large"
                  @click="jumpTaskRecord(rowData)"
                />
                <template #content>
                  执行记录
                </template>
              </Tooltip>
              <!-- <Tooltip>
          <Icon
            class="small-hand"
            :icon="Delete"
            color="danger"
            size="large"
            @click="deleteTask(rowData)"
          />
          <template #content>
            删除
          </template>
        </Tooltip> -->
            </template>
          </Table>
        </NewBsBox>
        <NewBsBox :bs="{ margin: 20, }">
          <Pagination
            v-model="state.pageNum"
            :total="state.dataLength"
            :page-size-options="pageSizeOptions"
            align="end"
            @update:modelValue="handlePagination"
            @update:pageSize="handlepageSize"
          />
        </NewBsBox>
      </TabPane>
    </Tabs>
  </NewBsBox>
  <TaskExecuteModal
    :is-show="modalVisible"
    :visible="modalVisible"
    :task-data="taskData"
    @handleVisible="handleVisible"
  />

  <Modal
    v-model:visible="infoVisible"
    type="danger"
    title="错误"
    @confirm="handleClose"
    @cancel="handleClose"
  >
    <Text>
      请勾选子任务后，再创建！
    </Text>
  </Modal>

  <Modal
    v-model:visible="showMergeJobModal"
    title="用例集"
    :with-footer="false"
    :style="{ width: 'auto' }"
  >
    <Form
      v-model="createForm"
      :style="{ marginTop: '20px' }"
      size="default"
      @submit="handleMergeSubmit"
    >
      <FormItem
        name="mergeTaskName"
        label="用例集名称"
      >
        <Input required />
      </FormItem>

      <FormItem
        name="mergeTaskContent"
        label="用例集详情"
      >
        <TextArea />
      </FormItem>

      <FormItem
        name="mergeTaskList"
        label="用例列表"
        :show-optional-text="false"
      >
        <List :options="state.selectedAllList">

          <template #description="{ id, taskName }">
            <Text
              :max-lines="2"
              ellipsis
            >
              【{{ id }}】 {{ taskName }}
            </Text>
          </template>

        </List>
      </FormItem>
    </Form>
  </Modal>

</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref,
  } from 'vue'
  import {
    Space, Table, Text, Button, toast, Pagination, Input, Tag, Popconfirm, Icon, Tooltip, Tabs, TabPane, Modal, Form, FormItem, TextArea, List,
    Select, InputNumber,
    // Select,
  } from '@xhs/delight'
  import {
    Search, Play, Add, Clear, Notes, AdjacentItem,
    // Delete,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'

  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    getTaskList, getTaskInOrderList, executeTask, CreateMergeTask, updateTask, deleteTask,
  } from '../../services/task'
  import {
    taskTypeList, getMapLable, autoEnableOption, pageSizeOptions,
  } from '../../utils/common'
  import TaskExecuteModal from './components/TaskExecuteModal.vue'

  const router = useRouter()
  const route = useRoute()

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

  const modalVisible = ref(false)
  const showMergeJobModal = ref(false)
  const taskData = ref({})
  const currentTask = ({})
  const showDebugModal = ref(false)

  const taskColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
    },
    {
      title: '任务内容',
      dataIndex: 'taskContent',
    },
    {
      title: '素材集名称',
      dataIndex: 'setName',
    },
    {
      title: '环境',
      dataIndex: 'env',
    },
    {
      title: '创建人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]

  const simpletaskColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
      minWidth: 70,
      sorter: true,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      sorter: true,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      sorter: true,
    },
    {
      title: '自动使能',
      dataIndex: 'auto_enable',
      minWidth: 80,
      sorter: true,
    },
    {
      title: '版本',
      dataIndex: 'version',
      sorter: true,
    },
    {
      title: '任务内容',
      dataIndex: 'taskContent',
      minWidth: 300,
      sorter: true,
    },
    {
      title: '素材集名称',
      dataIndex: 'setName',
      sorter: true,
    },
    {
      title: '环境',
      dataIndex: 'env',
      sorter: true,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]

  const loading = ref(false)
  const simpleloading = ref(false)
  const infoVisible = ref(false)
  const createForm = ref({ mergeTaskName: '', mergeTaskContent: '' })

  const state = reactive({
    id: '',
    taskName: '',
    taskType: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskdataSource: [],
    filtersetName: '',
    simplepageNum: 1,
    simplepageSize: 10,
    simpledataLength: 0,
    simpletaskdataSource: [],
    taskMode: '0',
    selectedList: [],
    selectedAllList: [],
    autoEnable: '',

  })

  // 父组件函数
  const handleVisible = e => {
    modalVisible.value = e
  }

  const handleMergeSubmit = () => {
    const payload = {
      request: {
        userName, // 写死，接登入后改
        taskName: createForm.value.mergeTaskName,
        content: createForm.value.mergeTaskContent ? createForm.value.mergeTaskContent : '',
      },
    }

    if (state.selectedList.length !== 0) {
      payload.request.subTaskList = state.selectedList
      payload.request.subTaskDetailList = state.selectedAllList
    }

    CreateMergeTask(payload)
      .then(res => {
        createForm.value = { mergeTaskName: '', mergeTaskContent: '' }
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          showMergeJobModal.value = false
          // 跳转到任务页面
          router.push({
            name: 'MergeTask',
            params: {},
          })
        }
      })
  }

  const rowSelection = {
    // getCheckboxProps?: (v: DataSource) => CheckboxContent & { selectable?: boolean }, onSelect?: (v: DataSource) => void;
    // getCheckboxProps: v => ({}),
    getCheckboxProps: v => ({
      selectable: v.id !== 0,
    }),
    onSelect: v => {
      const result = state.selectedAllList.some(item => {
        if (item.id === v.id) {
          return true
        }
        return false
      })
      if (!result) {
        state.selectedAllList.push({ id: v.id, taskName: v.taskName })
      }
    },
  }

  function simplehandleSelectedChange(v) {
    console.log('ninghuan jun', v, state.selectedList, state.selectedAllList)
    const tmparr = []
    state.selectedAllList.forEach((item: any) => {
      console.log('item', item)
      if (v.indexOf(item.id) !== -1) {
        tmparr.push(item)
      }
    })
    state.selectedAllList = tmparr
  }

  // function simplehandleSelectedAll() { }

  function launchAutoTasks(v) {
    const payload = {
      request: {
        taskId: v.id,
        userName: v.userName,
        taskName: v.taskName,
        taskType: v.taskType,
        taskContent: v.taskContent,
        taskMode: v.taskMode,
        auto_enable: '1',
        sceneType: v.sceneType,
        taskPara: {
          setId: v.setId,
          sedIds: v.setIds,
          setName: v.setName,
          setType: v.setType,
          env: v.env,
          clusterType: v.clusterType,
          paraJson: v.paraJson,
        },
      },
    }
    // console.log(payload)
    return payload
  }

  function pauseAutoTasks(v) {
    const payload = {
      request: {
        taskId: v.id,
        userName: v.userName,
        taskName: v.taskName,
        taskType: v.taskType,
        taskContent: v.taskContent,
        taskMode: v.taskMode,
        auto_enable: '2',
        sceneType: v.sceneType,
        taskPara: {
          setId: v.setId,
          sedIds: v.setIds,
          setName: v.setName,
          setType: v.setType,
          env: v.env,
          clusterType: v.clusterType,
          paraJson: v.paraJson,
        },
      },
    }
    // console.log(payload)
    return payload
  }

  function handleClose() {
    infoVisible.value = false
  }
  const handleAddMergeJob = () => {
    if (state.selectedList.length === 0) {
      infoVisible.value = true
    } else {
      showMergeJobModal.value = true
      infoVisible.value = false
    }
  }

  function searchTask() {
    loading.value = true
    const queryparam = {}
    const payload = {}
    if (state.taskName !== '') queryparam.taskName = state.taskName
    if (state.taskMode !== '') queryparam.taskMode = state.taskMode
    if (state.taskType !== '') queryparam.taskType = state.taskType
    if (state.taskMode === '0') {
      if (state.autoEnable !== '') queryparam.autoEnable = state.autoEnable
      Object.assign(payload, { request: { pageNum: state.simplepageNum, pageSize: state.simplepageSize, queryparam } })
    }
    if (state.taskMode === '1') {
      Object.assign(payload, { request: { pageNum: state.pageNum, pageSize: state.pageSize, queryparam } })
    }

    state.taskdataSource = []
    state.simpletaskdataSource = []

    getTaskList(payload)
      .then(res => {
        if (state.taskMode === '0') {
          simpleloading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.simpledataLength = res.dataLength
            // 这里处理每一项，从返回的嵌套信息提取 version 字段
            state.simpletaskdataSource = res.data.map(item => {
              return {
                ...item,
                version: item.paraJson?.requestinner?.version || '无' // 提取 version
              }
            })
            if (res.dataLength !== 0) {
              state.simpletaskdataSource.map(item => {
                item.key = item.id
                return item
              })
            }
          }
        }
        if (state.taskMode === '1') {
          loading.value = false
          if (res.status !== 0) {
            toast.danger(res.message)
          } else {
            state.dataLength = res.dataLength
            state.taskdataSource = res.data

          }
        }
      })
  }

  function handleAutoTasks(handletype) {
    simpleloading.value = true
    const queryparam = {}
    const payload = {}
    queryparam.taskName = '巡检任务'
    queryparam.taskMode = '0'
    if (handletype === '1') queryparam.autoEnable = '2'
    if (handletype === '2') queryparam.autoEnable = '1'
    Object.assign(payload, { request: { pageNum: 1, pageSize: 1000, queryparam } })

    getTaskList(payload).then(res => {
      if (res.status !== 0) {
        toast.info('请勿重复操作')
      } else {
        const tasks = res.data
        const promises = tasks.map(task => {
          if (handletype === '1') {
            const params = launchAutoTasks(task) // 获取每个任务的参数
            return updateTask(params) // 发起请求
          }
          if (handletype === '2') {
            const params = pauseAutoTasks(task) // 获取每个任务的参数
            return updateTask(params) // 发起请求
          }
          return Promise.resolve()
        })
        return Promise.all(promises)
      }
      return res
    }).then(
      () => {
        simpleloading.value = false
        if (handletype === '1') {
          state.autoEnable = '1'
          toast.success('启动成功')
        }
        if (handletype === '2') {
          state.autoEnable = '2'
          toast.success('暂停成功')
        }
        searchTask()
      },
    ).catch(error => {
      // 处理错误
      console.error('Error:', error)
    })
  }

  function handleTaskSort(v) {
    if (v === undefined) {
      searchTask()
    } else {
      console.log('v', v)
      const sortOrder = v.sortOrder === 'asc' ? 'asc' : 'desc'
      const dataIndex = v.dataIndex
      loading.value = true
      const queryparam = {
        dataIndex,
        sortOrder,
      }
      const payload = {}
      if (state.taskName !== '') queryparam.taskName = state.taskName
      if (state.taskMode !== '') queryparam.taskMode = state.taskMode
      if (state.taskMode === '0') {
        if (state.autoEnable !== '') queryparam.autoEnable = state.autoEnable
        Object.assign(payload, { request: { pageNum: state.simplepageNum, pageSize: state.simplepageSize, queryparam } })
      }
      if (state.taskMode === '1') {
        Object.assign(payload, { request: { pageNum: state.pageNum, pageSize: state.pageSize, queryparam } })
      }

      state.taskdataSource = []
      state.simpletaskdataSource = []

      getTaskInOrderList(payload)
        .then(res => {
          if (state.taskMode === '0') {
            simpleloading.value = false
            if (res.status !== 0) {
              toast.danger(res.message)
            } else {
              state.simpledataLength = res.dataLength
              state.simpletaskdataSource = res.data
              if (res.dataLength !== 0) {
                state.simpletaskdataSource.map(item => {
                  item.key = item.id
                  return item
                })
              }
            }
          }
        })
    }
  }

  function simplehandlePagination() {
    searchTask()
  }

  function simplehandlepageSize(e) {
    state.simplepageSize = e
    state.simplepageNum = 1
    searchTask()
  }

  function handlePagination() {
    searchTask()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchTask()
  }
  function clearParam() {
    state.taskName = ''
    searchTask()
  }

  function debugSingleTask(data) {
    showDebugModal.value = true
    console.log('debugSingleTask', data)
    currentTask.value = data
    console.log('currentTask', currentTask.value)
  }

  function handleDebugSubmit(v) {
    showDebugModal.value = false
    // 将debugNum转成整数
    console.log('debugNum', v.debugNum)
    const payload = {
      request: {
        userName,
        taskId: currentTask.value.id,
        taskType: currentTask.value.taskType,
        setId: currentTask.value.setId,
        setIds: currentTask.value.setIds,
        setType: currentTask.value.setType,
        setName: currentTask.value.setName,
        taskParam: currentTask.value.paraJson,
        sceneType: currentTask.value.sceneType,
        env: currentTask.value.env,
        clusterType: currentTask.value.clusterType,
        debugFlag: true,
        debugNum: Number(v.debugNum),
        debugInterval: Number(v.debugInterval),
      },
    }
    console.log('debugSingleTask—payload', payload)
    executeTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
        }
      })
  }

  // function singlerunTask(data) {
  //   const payload = {
  //     request: {
  //       userName,
  //       taskId: data.id,
  //       taskType: data.taskType,
  //       setId: data.setId,
  //       setIds: data.setIds,
  //       setType: data.setType,
  //       setName: data.setName,
  //       taskParam: data.paraJson,
  //       env: data.env,
  //       clusterType: data.clusterType,
  //       debugFlag: true,
  //     },
  //   }
  //   executeTask(payload)
  //     .then(res => {
  //       if (res.status !== 0) {
  //         toast.danger(res.message)
  //       } else {
  //         toast.success(res.message)
  //       }
  //     })
  // }

  function simplerunTask(data) {
    const payload = {
      request: {
        userName,
        taskId: data.id,
        taskType: data.taskType,
        setId: data.setId,
        setIds: data.setIds,
        setType: data.setType,
        setName: data.setName,
        taskParam: data.paraJson,
        sceneType: data.sceneType,
        env: data.env,
        clusterType: data.clusterType,
      },
    }
    // console.log('简单模式运行', store.state.Auth.userInfo.email,userName, payload)
    // return

    executeTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
        }
      })
  }

  function editTask(data) {
    console.log('ninghuanjun--编辑-', data)
    localStorage.setItem('editFlag', 'edit')
    localStorage.setItem('createTaskParamData', JSON.stringify(data))

    router.push({
      name: 'TaskCreate',
      params: {
        taskId: data.id,
        // taskName: data.taskName,
        // taskType: String(data.taskType),
        // taskMode: String(data.taskMode),
        // taskContent: data.taskContent,
        // env: data.env,
        // setName: data.setName,
        // setId: String(data.setId),
        // setType: String(data.setType),
        // streamType: JSON.stringify(data.paraJson.streamType),
        // requestinner: JSON.stringify(data.paraJson.requestinner),
      },
    })
  }

  function copyTask(data) {
    console.log('ninghuanjun--复制-', data)
    localStorage.setItem('editFlag', 'copy')
    localStorage.setItem('createTaskParamData', JSON.stringify(data))

    router.push({
      name: 'TaskCreate',
      params: {
      },
    })
  }

  function handleDeleteTaskClose() {
    console.log('删除')
  }

  function deleteSimpleTask(data) {
    const payload = {
      request: {
        taskId: data.id,
      },
    }
    deleteTask(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          searchTask()
        }
      })
  }

  function runTask(data) {
    modalVisible.value = true
    // data传给子组件
    taskData.value = data
    console.log('ninghuanjun--runtaskParam-', taskData.value)
  }

  function jumpTaskRecord(data) {
    localStorage.setItem('taskId', data.id)
    // 根据本地环境区分使用不同地址
    // 调试环境变量
    console.log('import.meta.env:', import.meta.env)
    console.log('VITE_ENV:', (import.meta.env as any)?.VITE_ENV)
    console.log('MODE:', (import.meta.env as any)?.MODE)
    console.log('NODE_ENV:', (import.meta.env as any)?.NODE_ENV)
    
    // 尝试多种方式获取环境信息
    const env = (import.meta.env as any)?.VITE_ENV || 
                (import.meta.env as any)?.MODE || 
                (import.meta.env as any)?.NODE_ENV || 
                (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.port === '3000') ? 'dev' : 'prod'
    
    console.log('最终使用的环境:', env)
    let url = ''
    if (env === 'dev' || env === 'development') {
      url = `http://127.0.0.1:1388/task/taskrecord`
    } else {
      url = `https://va.devops.sit.xiaohongshu.com/task/taskrecord`
    }
    console.log('最终URL:', url)
    window.open(url, '_blank')
  }

  function createTask() {
    // console.log('创建-弹窗')
    localStorage.setItem('editFlag', 'create')
    router.push({
      name: 'TaskCreate',
    })
  }
  function changeTab() {
    state.selectedList = []
    state.selectedAllList = []
    searchTask()
  }

  onMounted(() => {
    if (route.params.taskMode) {
      state.taskMode = route.params.taskMode
    }

    searchTask()
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
.btn-css {
   padding-left: 5px;
   padding-right: 5px;
}
</style>
