<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center'}">
      <Text
        bold
        style="margin-left:-15px"
      >任务ID:</Text>
      <Input
        v-model="state.taskId"
        :style="{ width: '90px', }"
      />
    </NewBsBox>
  </Space>

  <NewBsBox :bs="{ ml: 20, mr: 20,}">
    <Table
      :columns="taskRecordColumns"
      :data-source="state.taskrecordSource"
      :loading="loading"
      size="small"
      @selectedChange="handleSelectedChange"
      @selectedAll="handleSelectedAll"
    >
      <template #td="{ data }">
        <Text ellipsis>{{ data }}</Text>
      </template>
      <template #taskType="{ data }">
        <Tag
          color="blue"
          size="small"
        >{{ getMapLable(taskTypeList,data) }}</Tag>
      </template>
      <template #csvUrl="{ data }">
        <Tooltip>
          <Tag
            color="blue"
            size="small"
            @click="copyUrl(data)"
          >csvUrl</Tag>
          <template #content>
            {{ data }}
          </template>
        </Tooltip>
      </template>
      <template #status="{ data }">
        <Tag
          v-if="data === '成功'"
          theme="solid"
          color="green"
          size="small"
        >成功</Tag>
        <Tag
          v-if="data === '失败'"
          theme="solid"
          color="orange"
          size="small"
        >失败</Tag>

      </template>
      <template #taskEnv="{ data }">
        <Tag
          color="green"
          size="small"
        >{{ data }}</Tag>

      </template>
      <template #operation="{ rowData }">
        <Tooltip>
          <Icon
            class="small-hand"
            :icon="DocDetail"
            color="warning"
            size="large"
            @click="taskLog(rowData)"
          />
          <template #content>
            日志
          </template>
        </Tooltip>
        <!-- <span style="color:gray">|</span>
        <Tooltip>
          <Icon
            class="small-hand"
            :icon="Delete"
            color="danger"
            size="large"
            @click="deleteTask(rowData)"
          />
          <template #content>
            删除
          </template>
        </Tooltip> -->
      </template>
    </Table>
  </NewBsBox>
  <NewBsBox :bs="{ margin: 20, }">
    <Pagination
      v-model="state.pageNum"
      :total="state.dataLength"
      align="end"
      @update:modelValue="handlePagination"
      @update:pageSize="handlepageSize"
    />
  </NewBsBox>

  <TaskRecordModal
    :is-show="modalVisible"
    :visible="modalVisible"
    :resdata="resdata"
    @handleVisible="handleVisible"
  />
</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import {
    Space, Table, Text, toast, Pagination, Input, Tag, Icon, Tooltip,
    //  Button,
  } from '@xhs/delight'
  import {
    DocDetail,
    //  Search,Clear, Delete,
  } from '@xhs/delight/icons'
  import { NewBsBox } from '@xhs/yam-beer'
  import { useRoute, useRouter } from 'vue-router'
  import { getTaskRecordListId } from '../../services/task'
  import {
    taskTypeList, getMapLable,
  } from '../../utils/common'
  import TaskRecordModal from './components/TaskRecordModal.vue'

  const router = useRouter()

  const route = useRoute()

  const taskRecordColumns = [
    {
      title: '执行id',
      dataIndex: 'id',
    },
    {
      title: '任务id',
      dataIndex: 'taskId',
    },
    {
      title: '素材id',
      dataIndex: 'setId',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
    },
    {
      title: '素材集名称',
      dataIndex: 'setName',
    },
    {
      title: 'setType',
      dataIndex: 'setType',
    },
    {
      title: '执行时间',
      dataIndex: 'currentTime',
    },
    {
      title: '执行状态',
      dataIndex: 'status',
    },
    {
      title: 'csvUrl',
      dataIndex: 'csvUrl',
    },
    {
      title: '环境',
      dataIndex: 'taskEnv',
    },
    {
      title: '执行人',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },

  ]
  const loading = ref(false)
  const modalVisible = ref(false)
  const resdata = ref({})

  const state = reactive({
    id: '',
    taskName: '',
    userName: '',
    taskType: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskrecordSource: [],
    taskId: '',

  })

  // 父组件函数
  const handleVisible = e => {
    modalVisible.value = e
  }

  function searchTaskRecordId(taskId:any) {
    loading.value = true
    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        id: taskId,
      },
    }
    console.log('ninghuanjun-record--request--', payload)

    // const restest = '{"data":[{"id":1,"userName":"wuzhihao","taskName":"test1","taskType":"音视频","taskEnv":"sit","taskStatus":"1"},{"id":6,"userName":"wuzhihao","taskName":"发的看","taskType":"音视频","taskEnv":"prod","taskStatus":"2"},{"id":7,"userName":"wuzhihao","taskName":"春节快乐","taskType":"音视频","taskEnv":"sit","taskStatus":"3"},{"id":9,"userName":"wuzhihao","taskName":"test1","taskType":"音视频","taskEnv":"staging","taskStatus":"1"}],"message":"success","status":0,"dataLength":4}'

    state.taskrecordSource = []
    // state.taskrecordSource = JSON.parse(restest).data

    getTaskRecordListId(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.taskrecordSource = res.data
        }
      })
  }

  function handlePagination() {
    searchTaskRecordId(state.taskId)
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    searchTaskRecordId(state.taskId)
  }

  function copyUrl(data:any) {
    const cInput = document.createElement('input')
    cInput.value = data
    document.body.appendChild(cInput)
    cInput.select() // 选取文本框内容
    document.execCommand('copy')
    toast.success('复制成功')
  }

  function taskLog(data) {
    // modalVisible.value = true
    // // data传给子组件
    // resdata.value = data
    // 跳转到日志详情页面
    router.push({
      name: 'TaskRecordDetail',
      params: data,
    })
  }

  //   function deleteTask(data) {
  //     console.log(data)
  //   }

  onMounted(() => {
    state.taskId = route.params.id
    searchTaskRecordId(state.taskId)
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
