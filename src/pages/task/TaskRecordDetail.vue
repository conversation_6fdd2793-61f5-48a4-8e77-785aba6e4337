<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <Result
      v-if="state.isEmpty === false"
      status="empty"
      title="暂无内容"
      sub-title="请稍后再试。"
    />
    <div v-if="state.isEmpty === true">

      <NewBsBox :bs="{ ml: 20, mr: 20, }">
        <NewBsBox :bs="{ mr: 10 }">
          <BeerTitle :bs="{ mb: 16 }"> 【{{ state.id }}】报告详情 </BeerTitle>
          <BeerDivider />
          <br>
          <div style="display:flex;justify-content: center; align-items:center; text-align:center;line-height:100px ">
            <div
              id="resChart"
              style="margin: 0px;width: 500px;height: 500px;"
            />

          </div>
        </NewBsBox>
      </NewBsBox>

      <NewBsBox :bs="{ ml: 20, mr: 20, }">
        <NewBsBox :bs="{
          padding: 0,
          display: 'flex',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
        }">
          <NewBsBox :bs="{ mr: 10 }">
            <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-start', alignItems: 'flex-start' }">
              <BeerTitle :bs="{ mb: 16, mr: 10 }"> 【{{ state.id }}】执行日志详情 </BeerTitle>

              <Select
                v-if="isIndexOfArray(CvtaskType, String(state.taskType))"
                v-model="state.ResStatus"
                :options="cvstatusOptions"
                clearable
                :style="{ width: '200px' }"
              />
              <Select
                v-if="isIndexOfArray(AvtaskType, String(state.taskType))"
                v-model="state.ResStatus"
                :options="avstatusOptions"
                clearable
                :style="{ width: '200px' }"
              />
              <Button
                style="margin-left:10px"
                type="primary"
                :icon="{ icon: Search }"
                @click="filterRes()"
              >查询</Button>
            </NewBsBox>
          </NewBsBox>

          <NewBsBox :bs="{ mr: 30 }">
            <Button
              v-if="isIndexOfArray(CvtaskType, String(state.taskType)) && (String(state.taskType) === '3' || String(state.taskType) === '5' || String(state.taskType) === '6')"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white' }"
              @click="ViewResultImgs()"
            >批量可视化</Button>
            <Button
              v-if="isIndexOfArray(CvtaskType, String(state.taskType)) && (String(state.taskType) === '3' || String(state.taskType) === '5' || String(state.taskType) === '6')"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white', marginLeft: '10px' }"
              @click="DownViewResultImgs()"
            >批量下载</Button>
            <!-- <Button
              v-if="userName==='afei2'"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white',marginLeft:'10px' }"
              @click="View3dphototest(state.test)"
            >测试</Button> -->
            <Button
              v-if="isIndexOfArray(AvtaskType, String(state.taskType))"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white' }"
              @click="getQueryRes()"
            >更新</Button>
            <Button
              v-if="isIndexOfArray(AvtaskType, String(state.taskType))"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white', marginLeft: '10px' }"
              @click="showDrawer()"
            >收数配置</Button>
          </NewBsBox>
        </NewBsBox>
        <br>
        <BeerDivider />
        <br>
        <Table
          v-if="isIndexOfArray(AvtaskType, String(state.taskType))"
          :columns="taskResColumns"
          :data-source="state.taskResSource"
          :loading="loading"
          size="small"
          @selectedChange="handleSelectedChange"
          @selectedAll="handleSelectedAll"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
          <template #rescheckUrl="{ data }">
            <a
              style="font-size:14px"
              :href="data"
              target="_blank"
            >{{ data }}</a>
          </template>
          <template #setContent="{ data }">
            <a
              style="font-size:14px"
              :href="data"
              target="_blank"
            >{{ data }}</a>
          </template>

          <template #status="{ data }">
            <Tag
              v-if="data === 'RUNNING'"
              theme="solid"
              color="blue"
              size="small"
            >运行中</Tag>
            <Tag
              v-if="data === 'COMPLETED'"
              theme="solid"
              color="green"
              size="small"
            >成功</Tag>
            <Tag
              v-if="data === 'FAILED'"
              theme="solid"
              color="red"
              size="small"
            >失败</Tag>
            <Tag
              v-if="data === 'TIMED_OUT'"
              theme="solid"
              color="orange"
              size="small"
            >超时</Tag>
          </template>
        </Table>
        <Table
          v-if="isIndexOfArray(CvtaskType, String(state.taskType))"
          :columns="cvtaskResColumns"
          :data-source="state.taskResSource"
          :loading="loading"
          size="small"
          @selectedChange="handleSelectedChange"
          @selectedAll="handleSelectedAll"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
          <template #status="{ data }">
            <Tag
              v-if="data === '1'"
              theme="solid"
              color="green"
              size="small"
            >成功</Tag>
            <Tag
              v-if="data === '0'"
              theme="solid"
              color="red"
              size="small"
            >未知错误</Tag>
            <Tag
              v-if="data === '2'"
              theme="solid"
              color="red"
              size="small"
            >未检测到主体</Tag>
            <Tag
              v-if="data === '3'"
              theme="solid"
              color="red"
              size="small"
            >下载图片失败</Tag>
            <Tag
              v-if="data === '4'"
              theme="solid"
              color="red"
              size="small"
            >上传图片失败</Tag>
            <Tag
              v-if="data === '5'"
              theme="solid"
              color="red"
              size="small"
            >算法处理失败</Tag>
            <Tag
              v-if="data === '6'"
              theme="solid"
              color="red"
              size="small"
            >其他错误</Tag>
            <Tag
              v-if="data === '-1'"
              theme="solid"
              color="orange"
              size="small"
            >执行异常</Tag>
          </template>

          <template
            v-if="String(state.taskType) === '4' || String(state.taskType) === '5' || String(state.taskType) === '6'"
            #url="{ rowData }"
          >
            <span style="font-size:14px">{{
              JSON.parse(rowData.jsonResult).data.result[0].value.status === 1 ?
                JSON.parse(rowData.jsonResult).data.result[0].value.media.url : '--'
            }}</span>
          </template>

          <template
            v-if="String(state.taskType) === '5'"
            #int32_array="{ rowData }"
          >
            <span style="font-size:14px">{{ JSON.parse(rowData.jsonResult).data.result[0].value.feature.int32_array
              }}</span>
          </template>

          <template #jsonResult="{ data }">
            <span style="font-size:14px">{{ data }}</span>
          </template>
          <template #operation="{ rowData }">
            <!-- <Button
            size="small"
            :style="{ backgroundColor: '#3366FF', color: 'white' }"
            @click="jsonDetail(rowData)"
          >格式化</Button> -->
            <Button
              v-if="(String(state.taskType) === '3' || String(state.taskType) === '5' || String(state.taskType) === '6')"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white' }"
              @click="ViewResultImg(rowData)"
            >可视化</Button>
            <Button
              v-if="String(state.taskType) === '4'"
              size="small"
              :style="{ backgroundColor: '#2b85e4', color: 'white' }"
              @click="View3dphoto(rowData)"
            >可视化</Button>
            <Button
              size="small"
              :style="{ backgroundColor: '#999966', color: 'white' }"
              @click="ViewSrcImg(rowData)"
            >查看原图</Button>
          </template>

        </Table>
      </NewBsBox>
      <NewBsBox :bs="{ margin: 20, }">
        <Pagination
          v-model="state.pageNum"
          :total="state.dataLength"
          :page-size-options="pageSizeOptions"
          align="end"
          @update:modelValue="handlePagination"
          @update:pageSize="handlepageSize"
        />
      </NewBsBox>

    </div>
    <Modal
      v-model:visible="showjsonDataModal"
      :mask-closeable="logmaskCloseable"
      title="数据"
      :with-footer="false"
      :style="{ width: '800px', height: '800px' }"
    >
      <json-viewer
        :value="state.jsonData"
        copyable
      />
    </Modal>

    <ViewResultModal
      :is-show="modalVisible"
      :visible="modalVisible"
      :resdata="resdata"
      @handleVisible="handleVisible"
    />
    <View3dphotoModal
      :is-show="PhotomodalVisible"
      :visible="PhotomodalVisible"
      :photoresdata="photoresdata"
      @PhotohandleVisible="PhotohandleVisible"
    />

    <Modal
      v-model:visible="ImgVisible"
      style="width:700px;height:900px"
      title="可视化"
      :mask-closeable="ImgmaskCloseable"
    >
      <template #footer>

        <div style="text-align:center;">
          <Button
            style="margin:10px;"
            @click="preImg"
          >上一个</Button>
          <Button
            style="margin:10px;"
            type="primary"
            @click="nextImg"
          >下一个</Button>
        </div>
      </template>

      <div style="display:flex;justify-content: center; align-items:center; text-align:center;line-height:100px ">
        <div style="width:600px;height:100%">

          <img
            class="imgcss"
            :src="state.viewImg"
          >
        </div>
      </div>
    </Modal>

    <Drawer
      v-model:visible="paramDrawer.visible"
      size="70%"
      mask
      mask-closeable
      @close="closeDrawer()"
    >
      <template #title>
        <BeerTitle :bs="{ mb: 16 }"> {{ paramDrawer.title }} </BeerTitle>
      </template>
      <Spinner :spinning="paramDrawer.loading">
        <NewBsBox :bs="{ display: 'flex', flexWrap: 'wrap', gap: '10px' }">
          <!-- Left side - Input section -->
          <div :style="{ width: '45%' }">
            <Text
              bold
              style="font-size: 16px; margin-left:16px; margin-right:10px"
            >已添加的母板工作流:</Text>
            <!-- <BeerDivider :style="{ margin: '16px' }" /> -->
            <div
              class="scroll-container"
              :class="{ 'scroll-enabled': paramDrawer.workflowIds.length > 5 }"
            >
              <div
                v-for="workflowId in paramDrawer.workflowIds"
                :key="workflowId"
                class="workflow-item"
              >
                <Text style="font-size: 14px; margin-left:16px; margin-right:10px">
                  {{ workflowId }}
                </Text>
              </div>
            </div>
          </div>
          <div :style="{ float: 'right', width: '50%', marginBottom: '10px' }">
            <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <Text
                bold
                style="font-size: 16px; margin-left:16px; margin-right:10px"
              >母板工作流:</Text>
              <Input
                v-model="paramDrawer.workflowId"
                label="母板工作流"
                :style="{ width: '350px' }"
                clearable
                placeholder="请输入工作流Id，点击Enter添加"
                @enter="addWorkflowId()"
              />
            </NewBsBox>
            <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <Tooltip>
                <Button
                  style="margin-top:30px;margin-right:10px"
                  type="primary"
                  :icon="{ icon: Search }"
                  @click="queryTaskField()"
                >获取字段</Button>
                <template #content>
                  默认查询当前页面展示的工作流Id
                </template>
              </Tooltip>
              <Button
                style="margin-top:30px;margin-right:10px"
                type="secondary"
                :icon="{ icon: Redo }"
                @click="resetDrawer()"
              >重置</Button>
            </NewBsBox>
            <NewBsBox :bs="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <Text
                bold
                style="font-size: 14px; margin-top:30px; margin-right:10px"
              >收数数量:</Text>
              <InputNumber
                v-model="paramDrawer.limit"
                :style="{ width: '80px', marginTop: '30px', marginRight: '10px' }"
                :precision="0"
                placeholder="默认为1000"
              />
              <Button
                style="background-color: #11aa55; color: white; margin-top:30px;margin-right:10px"
                :icon="{ icon: EditOne }"
                @click="gatherResult()"
              >开始收数</Button>
            </NewBsBox>
          </div>
        </NewBsBox>
        <br>
        <div style="display: flex; max-height: 60%;">
          <!-- <div style="flex: 1;">
          <Text
            bold
            style="font-size: 16px; margin-left: 16px;"
          >
            工作流程图
          </Text>
          <BeerDivider :style="{ margin: '16px' }" />
          <WorkflowGraph
            :style="{ width: 'auto', height: '90%', margin: '16px' }"
            :cards="cards"
            :connections="connections"
            @card-click="cardClicked"
          />
        </div> -->
          <div style="flex: 1; ">
            <Text
              bold
              style="font-size: 16px; margin-left: 16px;"
            >
              任务详情
            </Text>
            <BeerDivider :style="{ margin: '16px' }" />
            <VirtualTree
              :key="treeKey"
              v-model:checked-keys="treeDataChecked"
              :style="{ height: '350px', width: '100%', margin: '16px' }"
              :tree-data="treeData"
              checkable
              expand-on-click-node
              @check="selectNode"
            />
          </div>
          <div style="flex: 1;">
            <Text
              bold
              style="font-size: 16px; margin-left: 16px;"
            >
              任务字段
            </Text>
            <BeerDivider :style="{ margin: '16px' }" />
            <TaskFieldEditor
              v-model:tasksJsonPath="tasksJsonPath"
              :height="350"
              :width="600"
              @field-removed="removeField"
            />
          </div>
        </div>
      </Spinner>
      <template #footer>
        <BeerDivider :style="{ margin: '16px' }" />
        <Button
          type="danger"
          @click="setDefaultTaskField"
        >设置默认字段</Button>
      </template>
    </Drawer>

  </Space>
</template>

<script setup lang="ts">
  import axios from 'axios'
  import {
    reactive, onMounted, ref, onBeforeUnmount,
  } from 'vue'
  // import { useStore } from 'vuex'
  import {
    Table, toast, Pagination, Tag, Button, Modal, viewImgs, Result, Input, InputNumber, Select, Text, Drawer, VirtualTree, Tooltip, Spinner,
    // Space, Text,Input,Icon,
  } from '@xhs/delight'
  import {
    // HandleRight, HandleLeft,Clear,
    Search, EditOne, Redo,
  } from '@xhs/delight/icons'
  import { NewBsBox, BeerTitle, BeerDivider } from '@xhs/yam-beer'
  import JsonViewer from 'vue-json-viewer'
  // import JSZip from 'jszip'
  // import FileSaver from 'file-saver'
  import { useRouter, useRoute } from 'vue-router'
  import * as echarts from 'echarts'
  import {
    getTaskReportList, setTaskReportDefaultTaskField, getTaskReportDefaultTaskField, getTaskReportGather, ResultUpdate, CVResultView, CVResultViewDown,
  } from '../../services/task'
  import {
    isIndexOfArray, AvtaskType, CvtaskType, avstatusOptions, cvstatusOptions, pageSizeOptions,
  } from '../../utils/common'
  import ViewResultModal from './components/ViewResultModal.vue'
  import View3dphotoModal from './components/View3dphotoModal.vue'
  // import WorkflowGraph from './components/WorkflowGraph.vue'
  import TaskFieldEditor from './components/TaskFieldEditor.vue'

  const router = useRouter()
  const route = useRoute()
  // const store = useStore()
  // const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

  const showjsonDataModal = ref(false)
  const logmaskCloseable = ref(false)
  const modalVisible = ref(false)
  const PhotomodalVisible = ref(false)
  const ImgVisible = ref(false)
  const ImgmaskCloseable = ref(false)

  const resdata = ref({})
  const photoresdata = ref({})

  const timer = ref({
    querytask: null,
  })

  const taskResColumns = [
    {
      title: 'workflowId',
      dataIndex: 'workflowID',
    },
    {
      title: 'correlationId',
      dataIndex: 'correlationID',
    },
    {
      title: 'status',
      dataIndex: 'status',
      minWidth: 65,
    },
    {
      title: 'fileKey',
      dataIndex: 'setContent',
    },
    {
      title: 'rescheckUrl',
      dataIndex: 'rescheckUrl',
    },
  ]
  const cvtaskResColumns = [
    {
      title: 'case_id',
      dataIndex: 'caseid',
    },
    {
      title: 'file_id',
      dataIndex: 'file_id',
    },
    {
      title: 'status',
      dataIndex: 'status',
    },
    {
      title: 'url',
      dataIndex: 'url',
    },
    {
      title: 'int32_array',
      dataIndex: 'int32_array',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
    },
  ]
  const loading = ref(false)

  // const cards = [
  //   {
  //     id: 'a',
  //     x: 140,
  //     y: 100,
  //     shape: 'rect',
  //     width: 100,
  //     height: 50,
  //     label: '开始',
  //     bgColor: '#e4ffec',
  //     borderColor: '#2ecc71',
  //     color: '#1b5833'
  //   },
  //   {
  //     id: 'b',
  //     x: 310,
  //     y: 180,
  //     shape: 'diamond',
  //     width: 80,
  //     height: 80,
  //     label: '判断',
  //     bgColor: '#fffbe4',
  //     borderColor: '#f1c40f',
  //     color: '#856120'
  //   },
  //   {
  //     id: 'c',
  //     x: 520,
  //     y: 100,
  //     shape: 'rect',
  //     width: 120,
  //     height: 60,
  //     label: '处理',
  //     bgColor: '#e4f5ff',
  //     borderColor: '#53a8fc',
  //     color: '#14507c'
  //   },
  //   {
  //     id: 'd',
  //     x: 350,
  //     y: 320,
  //     shape: 'circle',
  //     width: 70,
  //     height: 70,
  //     label: '结束',
  //     bgColor: '#fee',
  //     borderColor: '#e67e22',
  //     color: '#8d3204'
  //   }
  // ]

  // const connections = [
  //   {
  //     from: 'a',
  //     to: 'b',
  //     color: '#27ae60'
  //   },
  //   {
  //     from: 'b',
  //     to: 'c',
  //     color: '#d35400'
  //   }
  // ]

  const paramDrawer = ref({
    visible: false,
    loading: false,
    title: '',
    workflowId: '',
    workflowIds: [],
    tasksJsonPath: {},
    limit: 1000,
  })
  const treeKey = ref(0)
  const treeData = ref([])
  const treeDataChecked = ref([])
  const treeDataCheckedLast = ref([])
  const tasksJsonPath = ref({})
  const tasksJsonPathKeys = ref([])

  const reportdata = reactive({
    resultsumList: [] as any[],
    resultsum: {
      total: 0,
      success: 0,
    },
  })

  const state = reactive({
    test: {
      file_id: 'Afei2/baseset/commoncase/1.jpg', fileurl: 'https://image-url-2-feature-1251524319.cos.ap-shanghai.myqcloud.com/Afei2/baseset/commoncase/1.jpg', status: '-1', jsonResult: '{}',
    },
    ResStatus: '',
    id: '',
    taskName: '',
    userName: '',
    taskType: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskResSource: [],
    taskId: '',
    ReportSource: [],
    ReportdataLength: 0,
    ParamData: {},
    viewidList: [],
    viewImg: '',
    curIndex: -1,
    viewImgCheckids: [],
    viewImgList: [],
    srcimages: [],
    isEmpty: true,

    resOption: {
      title: {
        text: '执行情况',
        subtext: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'right',
      },
      series: [
        {
          name: '成功率',
          type: 'pie',
          radius: '50%',
          data: reportdata.resultsumList,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    },

  })

  // function cardClicked(card, i) {
  //   toast.success(`Card clicked: ${card} ${i}`)
  // }

  function generateTreeData(data, currentPath = '$') {
    const result = []
    if (data === null) {
      return undefined
    }
    Object.keys(data).forEach(key => {
      const value = data[key]
      if (typeof value === 'object') {
        if (/^[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?$/.test(key)) {
          result.push({
            key: `${currentPath}[${key}]`,
            title: `${key}`,
            children: generateTreeData(value, `${currentPath}[${key}]`),
          })
        } else {
          result.push({
            key: `${currentPath}.${key}`,
            title: `${key}`,
            children: generateTreeData(value, `${currentPath}.${key}`),
          })
        }
      } else if (typeof value !== 'object') {
        if (/^[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?$/.test(key)) {
          result.push({
            key: `${currentPath}[${key}]`,
            title: `${key}`,
            children: undefined,
          })
        } else {
          result.push({
            key: `${currentPath}.${key}`,
            title: `${key}`,
            children: undefined,
          })
        }
      }
    })
    return result
  }

  async function queryTaskField() {
    paramDrawer.value.loading = true
    if (!paramDrawer.value.workflowIds.length) {
      state.taskResSource.forEach(item => {
        paramDrawer.value.workflowIds.push(item.workflowID)
      })
    }
    try {
      const resData = await getTaskReportDefaultTaskField({ request: { execId: state.id } }).then(res => res.data)
      const resUrl = resData.url
      // Create array of promises for all API calls
      const promises = paramDrawer.value.workflowIds.map(key => {
        const redprocessUrl = `${resUrl}${key}`
        return axios.get(redprocessUrl)
      })

      // Wait for all API calls to complete
      const responses = await Promise.all(promises)

      // Create a new array for tree data
      const newTreeData = []
      const newTreeDataChecked = []

      // Process all responses
      responses.forEach(res => {
        Object.keys(res.data.tasks).forEach(key => {
          if (!newTreeData.map(item => item.title).includes(res.data.tasks[key].referenceTaskName)) {
            newTreeData.push({
              key: `$.tasks[?(@.referenceTaskName=="${res.data.tasks[key].referenceTaskName}")]`,
              title: res.data.tasks[key].referenceTaskName,
              children: generateTreeData(res.data.tasks[key], `$.tasks[?(@.referenceTaskName=="${res.data.tasks[key].referenceTaskName}")]`),
            })
          }
        })
      })

      // Replace the entire array at once to ensure reactivity
      // console.log('newTreeData', newTreeData)
      treeData.value = [...newTreeData]

      const newTasksJsonPath = resData.tasksJsonPath
      Object.keys(newTasksJsonPath).forEach(referenceTaskName => {
        if (treeData.value.map(item => item.title).includes(referenceTaskName)) {
          Object.values(newTasksJsonPath[referenceTaskName]).forEach(value => {
            newTreeDataChecked.push(value.split(',')[0])
          })
        }
      })
      paramDrawer.value.tasksJsonPath = { ...newTasksJsonPath }
      // console.log('paramDrawer.value.tasksJsonPath', paramDrawer.value.tasksJsonPath)
      treeDataChecked.value = [...newTreeDataChecked]
      // console.log('treeData', treeData.value)
    } catch (error) {
      console.error('Error loading workflow data:', error)
    } finally {
      paramDrawer.value.loading = false
    }
  }

  function isSubstring(sub: string, strList: string[]) {
    return strList.some(str => str !== sub && str.includes(sub))
  }

  function getTaskJsonPath(jsonPath: string, type: string) {
    if (type === 'add') {
      let isExist = false
      Object.keys(paramDrawer.value.tasksJsonPath).forEach(referenceTaskName => {
        Object.keys(paramDrawer.value.tasksJsonPath[referenceTaskName]).forEach(key => {
          if (paramDrawer.value.tasksJsonPath[referenceTaskName][key].split(',')[0] === jsonPath) {
            if (!Object.prototype.hasOwnProperty.call(tasksJsonPath.value, referenceTaskName)) {
              tasksJsonPath.value[referenceTaskName] = {}
            }
            tasksJsonPath.value[referenceTaskName][key] = paramDrawer.value.tasksJsonPath[referenceTaskName][key]
            tasksJsonPathKeys.value.push(key)
            isExist = true
          }
        })
      })
      if (isExist) {
        return
      }
      // console.log(jsonPath, 'is not in paramDrawer.value.tasksJsonPath')
      // 修复正则表达式 - 正确匹配 referenceTaskName=="xxx" 模式
      const match = jsonPath.match(/referenceTaskName=="([^"]*)"/)
      if (!match) {
        console.error(`Could not extract referenceTaskName from ${jsonPath}`)
        return // 跳过此项
      }
      const referenceTaskName = match[1]
      const key = jsonPath.split('.')[jsonPath.split('.').length - 1]
      if (!Object.prototype.hasOwnProperty.call(tasksJsonPath.value, referenceTaskName)) {
        tasksJsonPath.value[referenceTaskName] = {}
      }
      if (tasksJsonPathKeys.value.includes(key)) {
        let idx = 1
        let prefix = jsonPath.split('.')[jsonPath.split('.').length - 1 - idx]
        while (tasksJsonPathKeys.value.includes(`${prefix}.${key}`)) {
          idx += 1
          if (jsonPath.split('.').length - 1 - idx < 0) {
            break
          }
          prefix = jsonPath.split('.')[jsonPath.split('.').length - 1 - idx]
        }
        if (jsonPath.split('.').length - 1 - idx >= 0) {
          tasksJsonPath.value[referenceTaskName][`${prefix}.${key}`] = jsonPath
          tasksJsonPathKeys.value.push(`${prefix}.${key}`)
        }
      } else {
        tasksJsonPath.value[referenceTaskName][key] = jsonPath
        tasksJsonPathKeys.value.push(key)
      }
    } else if (type === 'remove') {
      Object.keys(tasksJsonPath.value).forEach(referenceTaskName => {
        Object.keys(tasksJsonPath.value[referenceTaskName]).forEach(key => {
          if (tasksJsonPath.value[referenceTaskName][key].split(',')[0] === jsonPath) {
            // 删除指定key
            delete tasksJsonPath.value[referenceTaskName][key]
            // 从key数组中移除
            tasksJsonPathKeys.value.splice(tasksJsonPathKeys.value.indexOf(key), 1)
            // 如果 referenceTaskName 里已经没有别的key了，把这个referenceTaskName也移除
            if (Object.keys(tasksJsonPath.value[referenceTaskName]).length === 0) {
              delete tasksJsonPath.value[referenceTaskName]
            }
          }
        })
      })
    }
  }

  function showDrawer() {
    paramDrawer.value.visible = true
    paramDrawer.value.title = '收数配置'
    queryTaskField()
  }

  function addWorkflowId() {
    if (paramDrawer.value.workflowId) {
      paramDrawer.value.workflowIds.push(paramDrawer.value.workflowId)
      paramDrawer.value.workflowId = ''
    }
  }

  function resetDrawer() {
    paramDrawer.value.workflowId = ''
    paramDrawer.value.workflowIds = []
    paramDrawer.value.limit = 1000
  }

  function closeDrawer() {
    paramDrawer.value.visible = false
    resetDrawer()
    console.log('closeDrawer')
  }

  function gatherResult() {
    paramDrawer.value.loading = true
    const payload = {
      request: {
        execId: state.id,
        limit: paramDrawer.value.limit,
        tasksJsonPath: tasksJsonPath.value,
      },
    }

    paramDrawer.value.loading = false
    getTaskReportGather(payload).then(res => {
      if (res.status !== 0) {
        toast.danger(res.message)
      } else {
        closeDrawer()
        toast.success('触发收数成功')
      }
    })
  }

  function selectNode() {
    const addList: string[] = []
    const removeList: string[] = []
    let tmp: string[] = []
    treeDataChecked.value.forEach(item => {
      if (!treeDataCheckedLast.value.includes(item)) {
        addList.push(item)
        tmp.push(item)
      }
    })
    tmp.forEach(item => {
      if (isSubstring(item, tmp)) {
        addList.splice(addList.indexOf(item), 1)
      }
    })
    tmp = []
    treeDataCheckedLast.value.forEach(item => {
      if (!treeDataChecked.value.includes(item)) {
        removeList.push(item)
        tmp.push(item)
      }
    })
    tmp.forEach(item => {
      if (isSubstring(item, tmp)) {
        removeList.splice(removeList.indexOf(item), 1)
      }
    })
    // console.log('addList', addList)
    // console.log('removeList', removeList)
    addList.forEach(item => {
      getTaskJsonPath(item, 'add')
    })
    removeList.forEach(item => {
      getTaskJsonPath(item, 'remove')
    })
    // console.log('tasksJsonPath', tasksJsonPath.value)
    // console.log('paramDrawer.value.tasksJsonPath', paramDrawer.value.tasksJsonPath)
    treeDataCheckedLast.value = treeDataChecked.value
  }

  function removeField(removedField: any) {
    const removedKey = removedField.path
    treeDataChecked.value = treeDataChecked.value.filter(k => k !== removedKey)
    treeKey.value += 1
  }

  function setDefaultTaskField() {
    paramDrawer.value.loading = true
    const payload = {
      request: {
        tasksJsonPath: tasksJsonPath.value,
      },
    }
    paramDrawer.value.loading = false
    setTaskReportDefaultTaskField(payload).then(res => {
      if (res.status !== 0) {
        toast.danger(res.message)
      } else {
        toast.success('设置默认任务字段成功')
      }
    })
  }

  function getTaskReport(params: any) {
    loading.value = true
    const query = {}
    // alert(state.ResStatus)

    if (state.ResStatus !== '' || typeof state.ResStatus === 'undefined') {
      query.status = state.ResStatus
    }

    const payload = {
      request: {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        executeId: params.id ? params.id : params.excuteid,
        csvResult: params.csvResult,
        query,
      },
    }

    state.ReportSource = []
    state.taskResSource = []
    state.viewImgCheckids = []

    getTaskReportList(payload)
      .then(res => {
        loading.value = false

        if (res.status === 0) {
          state.isEmpty = true
          state.dataLength = res.dataLength
          state.taskResSource = res.data
          reportdata.resultsum = res.resultsum
          reportdata.resultsumList = [
            { name: '成功', value: res.resultsum.success, itemStyle: { color: '#91CC75' } },
            { name: '失败', value: res.resultsum.failed, itemStyle: { color: '#EE6666' } },
            { name: '运行中', value: res.resultsum.running, itemStyle: { color: '#5470C6' } },
          ]
          state.resOption.series[0].data = reportdata.resultsumList
          state.resOption.title.subtext = `总计:${reportdata.resultsum.total} | 成功:${reportdata.resultsum.success} | 失败:${reportdata.resultsum.failed} | 运行中:${reportdata.resultsum.running}`

          const chartDom = document.getElementById('resChart')!
          const myChart = echarts.init(chartDom)
          myChart.clear()
          myChart.setOption(state.resOption)

          // 批量化id 成功状态下因为分页无法获取所有id,和无状态保持一致下载全部
          if (state.dataLength !== 0 && (state.ResStatus === '' || typeof state.ResStatus === 'undefined' || state.ResStatus === '1')) {
            for (let i = 0; i < state.dataLength; i++) {
              state.viewImgCheckids.push(i)
            }
          }
          // 页面数据渲染完成后，删除缓存聚合报告缓存
          localStorage.removeItem('subTaskDetail')
        } else {
          toast.danger(res.message)
          if (state.ResStatus === '') {
            state.isEmpty = false
          }
        }
      })
  }

  function handlePagination() {
    getTaskReport(state.ParamData)
  }

  // 过滤结果整不同状态
  function filterRes() {
    /// //
    state.pageNum = 1
    getTaskReport(state.ParamData)
  }

  function getUpdateRes() {
    // state.ParamData
    const payload = {
      request: {
        taskType: state.ParamData.taskType,
        executeId: state.ParamData.id,
        csvUrl: state.ParamData.csvUrl,
        env: state.ParamData.env,
        sceneType: state.ParamData.sceneType,
        paraJson: state.ParamData.paraJson,
        csvResult: state.ParamData.csvResult,
        clusterType: state.ParamData.clusterType,
      },
    }
    ResultUpdate(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        }
      })
  }

  function getQueryRes() {
    localStorage.setItem('execParamData', JSON.stringify(state.ParamData))
    getTaskReport(state.ParamData)
    getUpdateRes()
  }

  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getTaskReport(state.ParamData)
  }
  // 父组件函数
  const handleVisible = e => {
    modalVisible.value = e
  }

  function ViewSrcImg(rowData) {
    state.srcimages = []
    state.srcimages.push(rowData.fileurl)
    console.log(state.srcimages)
    viewImgs(state.srcimages)
  }

  const PhotohandleVisible = e => {
    PhotomodalVisible.value = e
  }

  // 3dphoto
  function View3dphoto(rowData) {
    PhotomodalVisible.value = true
    // data传给子组件
    photoresdata.value = rowData
    console.log('可视化3d', PhotomodalVisible.value, rowData)
  }

  // 可视化
  function ViewResultImg(rowData) {
    modalVisible.value = true
    // data传给子组件
    resdata.value = rowData
    console.log('可视化', modalVisible.value, rowData)
  }

  // imgview
  async function imgpoint(checkids) {
    state.viewImgList = []

    const payload = {
      request: {
        taskType: state.ParamData.taskType,
        checkid: checkids, // 图片的id
        csvResult: state.ParamData.csvResult,
        request: state.ParamData.paraJson.requestinner, /// ///////

      },
    }
    const res = await CVResultView(payload)
    if (res.status !== 0) {
      toast.danger(res.message)
    } else {
      state.viewImgList = res.data.imgurl
    }
  }

  // 批量可视化，公司组件
  function ViewResultImgs() {
    imgpoint(state.viewImgCheckids).then(res => {
      console.log(res)
      if (state.viewImgList.length === 0) {
        toast.danger({
          description: '无结果',
        })
      } else {
        // viewImgs(state.viewImgList)
        viewImgs(state.viewImgList, {
          closeOnMask: true,
          actionsLayout: [
            'step',
            'scale',
            [
              'rotate',
              'download',
            ],
          ],
        })
      }
    })
  }

  // // 批量下载
  // function imgpointdown(id) {
  //   const payload = {
  //     request: {
  //       execId: id, // 执行记录的id
  //     },
  //   }
  //   const res = await CVResultViewDown(payload)
  //   if (res.status !== 0) {
  //     if (res.status === -1) {
  //       toast.danger('正在打包中！请稍后！')
  //     } else if (res.status === -2) {
  //       toast.danger('打包失败！请重新执行任务！')
  //     } else {
  //       toast.danger(res.message)
  //     }
  //   } else {
  //     let strurl = ''
  //     if (res.data.zipUrl.indexOf('http:') !== -1) {
  //       strurl = res.data.zipUrl.replace(/http/, 'https')
  //     } else {
  //       strurl = res.data.zipUrl
  //     }

  //     const filename = strurl.split('/')[strurl.split('/').length - 1]
  //     const x = new XMLHttpRequest()
  //     x.open('GET', strurl, true)
  //     x.responseType = 'blob'
  //     x.onload = e => {
  //       console.log(e, e.target)
  //       // 会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
  //       const url = window.URL.createObjectURL(x.response)
  //       const a = document.createElement('a')
  //       a.href = url
  //       a.download = filename
  //       a.click()
  //       console.log('onload-----', e.target.status, x.status)
  //     }
  //     x.send()
  //     console.log('end-----', x.status)
  //     return x
  //   }
  // }
  function DownViewResultImgs() {
    if (state.viewImgCheckids.length === 0) {
      toast.danger({
        description: '无结果',
      })
      return
    }

    // imgpointdown(state.id).then(res => {
    //   console.log('return', res)
    // })
    const payload = {
      request: {
        execId: state.id, // 执行记录的id
      },
    }
    CVResultViewDown(payload).then(res => {
      if (res.status !== 0) {
        if (res.status === -1) {
          toast.danger('正在打包中！请稍后！')
        } else if (res.status === -2) {
          toast.danger('打包失败！请重新执行任务！')
        } else {
          toast.danger(res.message)
        }
      } else {
        toast.success({
          description: '开始下载图片！',
          strong: true,
        })
        let strurl = ''
        if (res.data.zipUrl.indexOf('http:') !== -1) {
          strurl = res.data.zipUrl.replace(/http/, 'https')
        } else {
          strurl = res.data.zipUrl
        }

        // const filename = strurl.split('/')[strurl.split('/').length - 1]
        // const x = new XMLHttpRequest()
        // x.open('GET', strurl, true)
        // x.responseType = 'blob'
        // x.onload = e => {
        //   console.log(e, e.target)
        //   // 会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
        //   const url = window.URL.createObjectURL(x.response)
        //   const a = document.createElement('a')
        //   a.href = url
        //   a.download = filename
        //   a.click()
        //   console.log('onload-----', e.target.status, x.status)
        // }
        // x.send()
        // console.log('end-----', x.status)

        // let strurl = ''
        // if (res.data.zipUrl.indexOf('http:') !== -1) {
        //   strurl = res.data.zipUrl.replace(/http/, 'https')
        // } else {
        //   strurl = res.data.zipUrl
        // }
        //         fetch(strurl).then(res => res.blob()).then(blob => { // 将链接地址字符内容转变成blob地址
        // 	  const a = document.createElement('a')
        // 	  a.href = URL.createObjectURL(blob)
        // 	  //测试链接console.log(a.href)
        // 	  a.download = 'fileName'  // 下载文件的名字
        // 	  document.body.appendChild(a)
        // 	  a.click()
        // })

        window.open(strurl)
      }
    })
  }

  onMounted(() => {
    // 优先使用 URL params.id
    const id = route.params.id

    // 1. 先用 subTaskDetail
    const subTaskDetail = localStorage.getItem('subTaskDetail')
    if (subTaskDetail) {
      state.ParamData = JSON.parse(subTaskDetail)
      state.id = state.ParamData.excuteid
      state.taskType = state.ParamData.taskType
      getUpdateRes()
      getTaskReport(state.ParamData)
      timer.value.querytask = setInterval(getQueryRes, 2 * 60 * 1000)
      return
    }

    // 2. 没有subTaskDetail时，尝试用execParamData
    const execParamData = localStorage.getItem('execParamData')
    if (!id || !execParamData) {
      // id 或 execParamData任意缺失，跳回列表
      router.push({ name: 'TaskRecord' })
      return
    }

    const localObj = JSON.parse(execParamData)
    if (String(id) !== localObj.id.toString()) {
      // URL id与localStorage id不一致，疑似参数错位，安全跳回列表
      router.push({ name: 'TaskRecord' })
      return
    }

    // 一切匹配，正常执行
    state.ParamData = localObj
    state.id = localObj.id
    state.taskType = localObj.taskType
    getUpdateRes()
    getTaskReport(state.ParamData)
    timer.value.querytask = setInterval(getQueryRes, 2 * 60 * 1000)

    // 用完清理，避免localStorage污染下一次显示
    localStorage.removeItem('execParamData')

    // -- 列展示动态调整部分可保留 --
    // 删除指定的元素（in-place），不改变引用
    if (String(state.taskType) === '3') {
      for (let i = cvtaskResColumns.length - 1; i >= 0; i--) {
        if (cvtaskResColumns[i].title === 'url' || cvtaskResColumns[i].title === 'int32_array') {
          cvtaskResColumns.splice(i, 1)
        }
      }
    }
    if (String(state.taskType) === '4' || String(state.taskType) === '6') {
      for (let i = cvtaskResColumns.length - 1; i >= 0; i--) {
        if (cvtaskResColumns[i].title === 'int32_array') {
          cvtaskResColumns.splice(i, 1)
        }
      }
    }
  })

  onBeforeUnmount(() => {
    if (timer.value.querytask) {
      clearInterval(timer.value.querytask)
    }
  })

</script>

<style lang="stylus" scoped>
.imgcss{
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
}

.scroll-container {
  /* Normal container styling */
  overflow-y: hidden;
  background-color: #f0f0f0;
  height: 75%; /* 设置一个你希望的高度 */
  margin-top: 16px;
  margin-left: 16px;
  margin-right: 16px;
  border-radius: 16px;
}

.scroll-container.scroll-enabled {
  /* Scrollable container styling */
  max-height: 125px; /* Adjust this value based on your needs */
  overflow-y: auto;
}

.workflow-item {
  padding: 2px 0; /* Add some vertical spacing between items */
}
</style>
