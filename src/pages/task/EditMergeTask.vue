<template>
  <Space
    direction="vertical"
    block
    style="padding: calc(var(--size-space-step-default) * 10)"
    align="start"
  >
    <BeerTitle
      divider
      :style="{width: '100%',marginTop:'-25px',marginBottom:'15px'}"
    >用例集编辑</BeerTitle>
    <!-- <div :style="{ width: '100%',display: 'flex',justifyContent: 'flex-start',alignItems: 'flex-start' }"> -->
    <div :style="{ flex:1}">
      <Text
        bold
        style="margin-right:15px"
      >用例集名称:</Text>
      <Input v-model="state.mergeTaskName" />
    </div>
    <div :style="{ flex:1}">
      <Text
        bold
        style="margin-right:15px"
      >用例集详情:</Text>
      <TextArea v-model="state.mergeTaskContent" />
    </div>
    <div><Text
      bold
      style=""
    >用例列表:</Text></div>
    <!-- </div> -->
    <Card :style="{ width: '100%'}">
      <NewBsBox :bs="{ display: 'flex',justifyContent: 'flex-start',alignItems: 'center',}">
        <Text
          bold
          style="margin-left:15px"
        >任务名称:</Text>
        <Input
          v-model="state.taskName"
          clearable
          :style="{ width: '120px' }"
        />
        <Text
          bold
          style="margin-left:10px"
        >创建人:</Text>
        <Input
          v-model="state.userName"
          :style="{ width: '120px' ,}"
        />
        <Button
          style="float:right;margin:10px"
          type="primary"
          :icon="{ icon: Search }"
          @click="getsubTask()"
        >查询</Button>
        <Button
          style="float:right;margin:10px"
          type="secondary"
          :icon="{ icon: Clear }"
          @click="clearParam"
        >重置</Button>

      </NewBsBox>

      <div :style="{ width: '100%',display: 'flex',justifyContent: 'flex-start',alignItems: 'flex-start' }">
        <div :style="{ flex:1}">
          <!-- <Text>当前选中左边：{{ state.selectedAllList }}</Text>  -->
          <Table
            v-model:selected="state.selectedList"
            :columns="taskColumns"
            :data-source="state.taskdataSource"
            :row-selection="rowSelection"
            size="small"
            @selectedChange="handleSelectedChange"
            @selectedAll="handleSelectedAll"
          />
          <br>
          <Pagination
            v-model="state.pageNum"
            :total="state.dataLength"
            align="end"
            @update:modelValue="handlePagination"
            @update:pageSize="handlepageSize"
          />
        </div>
        <div :style="{ flex:'0,0,2%'}">
          <div style="margin-top: 150px;">
            <Button
              style="margin: 10px"
              type="primary"
              :icon="RightTwo"
              :disabled="!state.flag"
              @click="addSelectedRow"
            />
            <br>
            <Button
              style="margin: 10px"
              type="primary"
              :icon="LeftTwo"
              :disabled="state.flag"
              @click="delSelectedRow"
            />
          </div>
        </div>
        <div :style="{ flex:1}">

          <!-- <Text>当前选中all：{{ state.selectedrightAllList }}</Text>  -->

          <Table
            v-model:selected="state.rightselectedList"
            :columns="rightTaskColumns"
            :data-source="state.currentPageData"
            :row-selection="rightrowSelection"
            size="small"
            @selectedChange="righthandleSelectedChange"
            @selectedAll="righthandleSelectedAll"
          />
          <br>
          <Pagination
            v-model="state.rightpageNum"
            :total="state.rightdataLength"
            align="end"
            @update:modelValue="righthandlePagination"
            @update:pageSize="righthandlepageSize"
          />
        </div>
      </div>
    </Card>
    <div :style="{ width: '100%',display: 'flex',justifyContent: 'center',alignItems: 'flex-start' }">
      <Button
        style="margin:10px;width:200px"
        type="primary"
        :icon="{ icon: Edit }"
        @click="editMergeTask()"
      >编辑</Button>
    </div>

    <!-- 版本修改模态框 -->
    <Modal
      v-model:visible="state.versionModalVisible"
      title="修改任务版本"
      :mask-closeable="false"
      :style="{ width: '400px' }"
    >
      <div style="padding: 20px 0;">
        <div style="margin-bottom: 16px;">
          <Text bold>任务名称：</Text>
          <Text>{{ state.currentEditTask?.taskName || '' }}</Text>
        </div>
        <div style="margin-bottom: 16px;">
          <Text bold>当前版本：</Text>
          <Text>{{ getCurrentVersion(state.currentEditTask) }}</Text>
        </div>
        <div style="margin-bottom: 16px;">
          <Text bold>新版本：</Text>
          <Input
            v-model="state.newVersion"
            placeholder="请输入新版本号"
            style="margin-top: 8px;"
          />
        </div>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 8px;">
          <Button @click="cancelVersionEdit">取消</Button>
          <Button type="primary" @click="confirmVersionEdit">确认修改</Button>
        </div>
      </template>
    </Modal>

  </Space>
</template>

<script setup lang="ts">
  import {
    reactive, onMounted, ref, h,
  } from 'vue'
  import {
    Space, Table, Text, Button, toast, Pagination, Input, Card, TextArea, Modal,
  } from '@xhs/delight'
  import {
    Search, Clear, LeftTwo, RightTwo, Edit,
  } from '@xhs/delight/icons'
  import { NewBsBox, BeerTitle } from '@xhs/yam-beer'

  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    getTaskList, editMergeTask as editMergeTaskAPI, getMergeTaskList,
  } from '../../services/task'
  import {} from './components/TaskExecuteModal.vue'

  const router = useRouter()
  const route = useRoute()

  const store = useStore()
  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

  const loading = ref(false)

  const state = reactive({
    taskMode: '0',
    taskName: '',
    userName: '',
    pageNum: 1,
    pageSize: 10,
    dataLength: 0,
    taskdataSource: [],

    selectedList: [],
    selectedAllList: [],
    lefttmpselected: [],
    editData: {},
    rightselectedList: [],
    righttaskdataSource: [],
    currentPageData: [],
    rightpageNum: 1,
    rightpageSize: 10,
    rightdataLength: 0,
    righttotal: 0,
    selectedrightAllList: [],
    flag: true,

    // 合并任务相关字段
    mergeTaskName: '',
    mergeTaskContent: '',

    // 版本修改相关字段
    versionModalVisible: false,
    currentEditTask: null,
    newVersion: '',

  })

  const taskColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '版本',
      dataIndex: 'version',
      width: 80,
      render: (td) => {
        // 从 paraJson.requestinner.version 中提取版本信息
        try {
          const row = td.rowData;
          let paraJson = row.paraJson;

          // 如果paraJson是字符串，尝试解析
          if (typeof paraJson === 'string') {
            paraJson = JSON.parse(paraJson);
          }

          const version = paraJson?.requestinner?.version;
          return version || '-';
        } catch (error) {
          console.error('Error extracting version:', error);
          return '-';
        }
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 160,
      render: (td) => {
        const row = td.rowData;
        return h('div', { style: 'display: flex; gap: 8px;' }, [
          h(Button, {
            size: 'small',
            style: 'background-color:#999966;color:white;padding-left:5px;padding-right:5px;',
            type: 'primary',
            onClick: () => editTask(row)
          }, '编辑'),
          h(Button, {
            size: 'small',
            style: 'background-color:#4CAF50;color:white;padding-left:5px;padding-right:5px;',
            type: 'primary',
            onClick: () => editVersion(row)
          }, '修改版本')
        ]);
      }
    },
  ]

  // 右侧表格列配置
  const rightTaskColumns = [
    {
      title: '任务id',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '版本',
      dataIndex: 'version',
      width: 80,
      render: (td) => {
        // 从 paraJson.requestinner.version 中提取版本信息
        try {
          const row = td.rowData;
          let paraJson = row.paraJson;

          // 如果paraJson是字符串，尝试解析
          if (typeof paraJson === 'string') {
            paraJson = JSON.parse(paraJson);
          }

          const version = paraJson?.requestinner?.version;
          return version || '-';
        } catch (error) {
          console.error('Error extracting version:', error);
          return '-';
        }
      }
    },
  ]

  const rowSelection = {
    getCheckboxProps: v => ({
      disabled: v.key === -1,
    }),
    onSelect: v => {
      const result = state.selectedAllList.some(item => {
        if (item.id === v.id) {
          return true
        }
        return false
      })
      if (!result) {
        state.selectedAllList.push({ id: v.id, taskName: v.taskName })
      }
    },
  }

  function handleSelectedChange(v, raw) {
    // console.log('handleSelectedChange', v, raw)
    state.selectedAllList = []
    if (raw.length !== 0) {
      state.flag = true
      raw.forEach((item:any) => {
        state.selectedAllList.push({
          key: item.id,
          id: item.id,
          taskName: item.taskName,
        })
      })
    } else {
      state.flag = false
    }
  }
  function handleSelectedAll(v) {
    if (v === true) {
      state.flag = true
    } else {
      state.flag = false
    }
  }

  const rightrowSelection = {
    getCheckboxProps: v => ({
      disabled: v.key === -1,
    }),
    onSelect: v => {
      const result = state.selectedrightAllList.some(item => {
        if (item.id === v.id) {
          return true
        }
        return false
      })
      if (!result) {
        state.selectedrightAllList.push({ id: v.id, taskName: v.taskName })
      }
    },
  }

  function righthandleSelectedChange(v) {
    const tmparr = []
    state.selectedrightAllList.forEach((item:any) => {
      //   console.log('item', item)
      if (v.indexOf(item.id) !== -1) {
        tmparr.push(item)
      }
    })
    state.selectedrightAllList = tmparr

    if (state.rightselectedList.length !== 0) {
      state.flag = false
    }
  }
  function righthandleSelectedAll(v) {
    if (v === true) {
      state.flag = false
    } else {
      state.flag = true
    }
  }

  function getsubTask() {
    loading.value = true
    const queryparam = {}
    const payload = {}
    if (state.taskName !== '') queryparam.taskName = state.taskName
    if (state.taskMode !== '') queryparam.taskMode = state.taskMode
    if (state.taskMode === '0') {
      Object.assign(payload, { request: { pageNum: state.pageNum, pageSize: state.pageSize, queryparam } })
    }

    state.taskdataSource = []

    getTaskList(payload)
      .then(res => {
        loading.value = false
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          state.dataLength = res.dataLength
          state.taskdataSource = res.data
          if (res.dataLength !== 0) {
            state.taskdataSource.map(item => {
              item.key = item.id
              return item
            })
          }
          // 在获取任务列表后，匹配右侧任务的详细信息
          matchTasksWithDetails()
        }
      })
  }

  function clearParam() {
    state.selectedAllList = []
    state.taskName = ''
    getsubTask()
  }

  // 匹配右侧任务与左侧完整任务信息，补充paraJson字段
  function matchTasksWithDetails() {
    if (state.righttaskdataSource.length > 0 && state.taskdataSource.length > 0) {
      state.righttaskdataSource.forEach((rightTask:any) => {
        const fullTask = state.taskdataSource.find((leftTask:any) => leftTask.id === rightTask.id)
        if (fullTask) {
          rightTask.paraJson = fullTask.paraJson
        }
      })
      getCurrentPageData() // 更新当前页面数据
    }
  }

  // 重新加载合并任务数据
  function reloadMergeTaskData() {
    const mergeTaskId = route.params.id
    if (mergeTaskId) {
      const payload = {
        request: {
          pageNum: 1,
          pageSize: 1000,
          queryparam: {}
        }
      }

      getMergeTaskList(payload)
        .then(res => {
          if (res.status === 0 && res.data) {
            const currentTask = res.data.find(task => task.id.toString() === mergeTaskId.toString())
            if (currentTask) {
              state.editData = currentTask
              state.mergeTaskName = currentTask.taskName
              state.mergeTaskContent = currentTask.content
              state.righttaskdataSource = []

              if (currentTask.subTaskDetailList) {
                state.rightdataLength = currentTask.subTaskDetailList.length
                // 先清空右侧数据
                state.righttaskdataSource = []

                // 获取完整的任务信息来显示版本
                const taskIds = currentTask.subTaskDetailList.map((item:any) => item.id)

                // 通过getTaskList获取这些任务的完整信息
                const queryparam = { taskMode: '0' } 
                const payload = {
                  request: {
                    pageNum: 1,
                    pageSize: 1000,
                    queryparam
                  }
                }

                getTaskList(payload).then(taskRes => {
                  if (taskRes.status === 0 && taskRes.data) {
                    // console.log('reloadMergeTaskData - getTaskList response:', taskRes.data.length, 'tasks')
                    // 筛选出右侧需要的任务
                    const rightTasks = taskRes.data.filter((task:any) => taskIds.includes(task.id))
                    // console.log('reloadMergeTaskData - filtered right tasks:', rightTasks.length, 'tasks')

                    rightTasks.forEach((task:any) => {
                      // console.log('reloadMergeTaskData - adding task:', task.id, 'paraJson:', task.paraJson)
                      state.righttaskdataSource.push({
                        id: task.id,
                        key: task.id,
                        taskName: task.taskName,
                        paraJson: task.paraJson
                      })
                    })

                    getCurrentPageData()
                    // console.log('reloadMergeTaskData - right tasks loaded, currentPageData:', state.currentPageData.length)
                  }
                }).catch(err => {
                  console.error('Error loading right tasks:', err)
                  // 如果获取失败，使用基本信息
                  currentTask.subTaskDetailList.forEach((item:any) => {
                    state.righttaskdataSource.push({
                      id: item.id,
                      key: item.id,
                      taskName: item.taskName,
                      paraJson: null
                    })
                  })
                  getCurrentPageData()
                })
              }
            }
          }
        })
        .catch(err => {
          console.error('重新加载合并任务数据失败:', err)
        })
    }
  }

  function handlePagination() {
    getsubTask()
  }
  function handlepageSize(e) {
    state.pageSize = e
    state.pageNum = 1
    getsubTask()
  }
  function getCurrentPageData() {
    const begin = (state.rightpageNum - 1) * state.rightpageSize
    const end = state.rightpageNum * state.rightpageSize
    state.currentPageData = state.righttaskdataSource.slice(
      begin,
      end,
    )
  }

  function righthandlePagination() {
    getCurrentPageData()
  }
  function righthandlepageSize(e) {
    state.rightpageSize = e
    state.rightpageNum = 1
  }

  function addSelectedRow() {
    const keys = state.righttaskdataSource.map(item => item.key)
    const res = state.selectedAllList.filter(item => {
      const newres = !(keys.indexOf(item.key) > -1) // 添加的数组对象id是否已经存在
      return newres // false表示不符合条件
    })

    if (res.length !== 0) {
      res.forEach((item:any) => {
        // 保存完整的任务信息
        state.righttaskdataSource.unshift({
          key: item.key,
          id: item.id,
          taskName: item.taskName,
          paraJson: item.paraJson,
        })
      })
    }
    // console.log('组合过滤后', res, state.righttaskdataSource)
    // 右侧请求当前页面
    state.rightpageNum = 1
    state.rightdataLength = state.righttaskdataSource.length
    getCurrentPageData()
    // 添加后选中列表置空
    state.selectedrightAllList = []
    state.selectedAllList = []
    state.selectedList = []
    state.rightselectedList = []
  }

  function delSelectedRow() {
    for (let i = 0; i < state.rightselectedList.length; i++) {
      for (let j = 0; j < state.righttaskdataSource.length; j++) {
        if (
          state.rightselectedList[i]
          === state.righttaskdataSource[j].key
        ) {
          state.righttaskdataSource.splice(j, 1)
        }
      }
    }

    state.rightpageNum = 1
    state.rightdataLength = state.righttaskdataSource.length
    getCurrentPageData()

    // console.log('删除后', state.righttaskdataSource.length)
    state.selectedrightAllList = []
    if (state.righttaskdataSource.length === 0) {
      state.flag = true
    }
  }

  // 编辑单个任务
  function editTask(data) {
    localStorage.setItem('editFlag', 'edit')
    localStorage.setItem('createTaskParamData', JSON.stringify(data))
    localStorage.setItem('returnToPage', 'EditMergeTask') // 设置返回页面标识
    localStorage.setItem('mergeTaskId', state.editData.id) // 存储合并任务的id

    router.push({
      name: 'TaskCreate',
      params: {
        taskId: data.id,
      },
    })
  }

  // 获取当前版本
  function getCurrentVersion(task) {
    if (!task) return '-'
    try {
      let paraJson = task.paraJson
      if (typeof paraJson === 'string') {
        paraJson = JSON.parse(paraJson)
      }
      return paraJson?.requestinner?.version || '-'
    } catch (error) {
      // console.error('获取版本信息失败:', error)
      return '-'
    }
  }

  // 修改版本
  function editVersion(data) {
    // console.log('修改版本按钮被点击-', data)
    // console.log('当前state.versionModalVisible:', state.versionModalVisible)
    state.currentEditTask = data
    state.newVersion = getCurrentVersion(data)
    state.versionModalVisible = true
    // console.log('设置后state.versionModalVisible:', state.versionModalVisible)
    // console.log('当前版本:', state.newVersion)
  }

  // 取消版本修改
  function cancelVersionEdit() {
    state.versionModalVisible = false
    state.currentEditTask = null
    state.newVersion = ''
  }

  // 确认版本修改
  function confirmVersionEdit() {
    if (!state.newVersion.trim()) {
      toast.warning('请输入新版本号')
      return
    }

    if (!state.currentEditTask) {
      toast.danger('任务信息丢失，请重试')
      return
    }

    // 更新任务的版本信息
    updateTaskVersion(state.currentEditTask, state.newVersion.trim())
  }

  // 更新任务版本
  async function updateTaskVersion(task, newVersion) {
    try {
      // 解析当前的paraJson
      let paraJson = task.paraJson
      if (typeof paraJson === 'string') {
        paraJson = JSON.parse(paraJson)
      }

      // 更新版本信息
      if (!paraJson.requestinner) {
        paraJson.requestinner = {}
      }
      paraJson.requestinner.version = newVersion

      // 构建更新请求
      const payload = {
        request: {
          userName,
          taskName: task.taskName,
          taskType: task.taskType,
          taskContent: task.taskContent,
          taskMode: task.taskMode,
          auto_enable: task.auto_enable,
          sceneType: task.sceneType,
          taskPara: {
            env: task.env,
            clusterType: task.clusterType,
            setName: task.setName,
            setId: task.setId,
            setIds: task.setIds,
            setType: task.setType,
            paraJson,
          },
          tasksJsonPath: task.tasksJsonPath || '{}',
          taskId: task.id, // 添加taskId用于更新
        },
      }

      console.log('更新版本请求:', payload)

      // 调用更新API
      const { updateTask } = await import('../../services/task')
      const res = await updateTask(payload)

      if (res.status !== 0) {
        toast.danger(`更新失败: ${res.message}`)
      } else {
        toast.success('版本更新成功')

        // 更新本地数据
        task.paraJson = paraJson

        // 关闭模态框
        cancelVersionEdit()

        // 刷新右侧表格数据
        getCurrentPageData()
      }
    } catch (error) {
      console.error('更新版本失败:', error)
      toast.danger('更新版本失败，请重试')
    }
  }

  // 编辑合并任务
  function editMergeTask() {
    const payload = {
      request: {
        id: state.editData.id,
        userName, // 写死，接登入后改
        taskName: state.mergeTaskName,
        content: state.mergeTaskContent,
      },
    }
    payload.request.subTaskDetailList = []
    payload.request.subTaskList = []
    state.righttaskdataSource.forEach((item:any) => {
      payload.request.subTaskList.push(item.id)
      payload.request.subTaskDetailList.push({ id: item.id, taskName: item.taskName })
    })

    editMergeTaskAPI(payload)
      .then(res => {
        if (res.status !== 0) {
          toast.danger(res.message)
        } else {
          toast.success(res.message)
          // 重新加载当前页面数据
          reloadMergeTaskData()
        }
      })
  }

  onMounted(() => {
    if (localStorage.getItem('editMergeTaskData')) {
      const editData = localStorage.getItem('editMergeTaskData')
      if (editData) {
        state.editData = JSON.parse(editData)
        state.mergeTaskName = state.editData.taskName
        state.mergeTaskContent = state.editData.content
        state.righttaskdataSource = []
        // 编辑前选中的list
        //   state.selectedList = state.editData.subTaskList
        state.rightdataLength = state.editData.subTaskList.length
        // 获取完整的任务信息来显示版本
        const taskIds = state.editData.subTaskDetailList.map((item:any) => item.id)
        // console.log('Loading localStorage tasks with IDs:', taskIds)

        // 通过getTaskList获取这些任务的完整信息
        const queryparam = { taskMode: '0' } 
        const payload = {
          request: {
            pageNum: 1,
            pageSize: 1000,
            queryparam
          }
        }

        getTaskList(payload).then(taskRes => {
          if (taskRes.status === 0 && taskRes.data) {
            console.log('localStorage - getTaskList response:', taskRes.data.length, 'tasks')
            // 筛选出右侧需要的任务
            const rightTasks = taskRes.data.filter((task:any) => taskIds.includes(task.id))
            console.log('localStorage - filtered right tasks:', rightTasks.length, 'tasks')

            rightTasks.forEach((task:any) => {
              console.log('localStorage - adding task:', task.id, 'paraJson:', task.paraJson)
              state.righttaskdataSource.push({
                id: task.id,
                key: task.id,
                taskName: task.taskName,
                paraJson: task.paraJson
              })
            })

            getCurrentPageData()
            console.log('localStorage - right tasks loaded, currentPageData:', state.currentPageData.length)
          }
        }).catch(err => {
          console.error('Error loading localStorage right tasks:', err)
          // 如果获取失败，使用基本信息
          state.editData.subTaskDetailList.forEach((item:any) => {
            state.righttaskdataSource.push({
              id: item.id,
              key: item.id,
              taskName: item.taskName,
              paraJson: null
            })
          })
          getCurrentPageData()
        })

        //   console.log('初始值', state.righttaskdataSource)
        // 前端分页，当前页面
        getCurrentPageData()

        localStorage.removeItem('editMergeTaskData')
      }
    } else {
      // 如果没有localStorage数据，通过路由参数加载
      reloadMergeTaskData()
    }
    getsubTask()
  })

</script>

<style lang="stylus" scoped>
.small-hand {
    cursor: pointer;
}
</style>
