import { http } from '@xhs/launcher'

export function TaskListDisplay(payload: {
}): Promise<any> {
  return http.post('TASKLISTDISPLAY', payload, {
    extractData: false,
  })
}
export function perfTestStart(payload: {
}): Promise<any> {
  return http.post('PERFTESTSTART', payload, {
    extractData: false,
  })
}
export function perfTestEnd(payload: {
}): Promise<any> {
  return http.post('PERFTESTEND', payload, {
    extractData: false,
  })
}
export function singleTaskDisplay(payload: {
}): Promise<any> {
  return http.post('SINGLETASKDISPLAY', payload, {
    extractData: false,
  })
}
export function getShishiResult(payload: {
}): Promise<any> {
  return http.post('GETSHISHIRESULT', payload, {
    extractData: false,
  })
}
