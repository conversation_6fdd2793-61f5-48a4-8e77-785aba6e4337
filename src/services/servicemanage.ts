import { http } from '@xhs/launcher'

export function serviceInfoList(payload: {
}): Promise<any> {
  return http.post('SERVICEINFOLIST', payload, {
    extractData: false,
  })
}

// rpc任务创建
export function sourceRegisterCreate(payload: {
}): Promise<any> {
  return http.post('RPCCREATE', payload, {
    extractData: false,
  })
}
// GetApiTemplate
export function GetApiTemplate(payload: {
}): Promise<any> {
  return http.post('SOURCEGETTEMPLATE', payload, {
    extractData: false,
  })
}
// deleteRpcTask
export function deleteRpcTask(payload: {
}): Promise<any> {
  return http.post('DELETERPC', payload, {
    extractData: false,
  })
}
export function updateRpcTask(payload: {
}): Promise<any> {
  return http.post('UPDATERPC', payload, {
    extractData: false,
  })
}

// RPCRUN
export function serviceRpcRun(payload: {
}): Promise<any> {
  return http.post('RPCRUN', payload, {
    extractData: false,
  })
}
