import { http } from '@xhs/launcher'

// uploadimg
export function UploadFile(payload: {
}): Promise<any> {
  return http.post('UPLOAD_FILE', payload, {
    extractData: false,
  })
}

// uploadimg
export function Modify(payload: {
}): Promise<any> {
  return http.post('MODIFY_IMG', payload, {
    extractData: false,
  })
}

// 查询转码测试记录
export function queryNoteMediaCheckRecord(payload: {
  request: {
    noteId?: string
    status?: string
    videoId?: string
    file_id?: string
    env?: string
    sceneType?: string
    cloudType?: string
    mediaType?: string
    videoType?: string
    page_num: number
    page_size: number
  }
}): Promise<any> {
  return http.post('api/testtask/avset/testtaskmaster/note/query', payload, {
    extractData: false,
  })
}
