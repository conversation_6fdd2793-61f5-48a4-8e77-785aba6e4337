import { http } from '@xhs/launcher'

// 获取cos key
export function getCosKey(payload: {
}): Promise<any> {
  return http.post('XHS_GET_COS_KEY', payload, {
    extractData: false,
  })
}

// // 上传视频
// export function uploadVideo(payload: {
//   file: File  // 视频文件
// }): Promise<any> {
//   const formData = new FormData()
//   // 添加文件到表单数据，确保字段名为'file'以匹配后端期望
//   formData.append('file', payload.file)
//   return http.post('XHS_UPLOAD_VIDEO', formData, {
//     extractData: false,
//     headers: {
//       'Content-Type': 'multipart/form-data'
//     }
//   })
// }

// 获取cos key
export function insertVideoInfoToVA(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_INSERT', payload, {
    extractData: false,
  })
}

// 生成任务素材
export function generateTaskMaterialDaily(payload: {
}): Promise<any> {
  return http.post('XHS_GENERATE_TASK_MATERIAL_DAILY', payload, {
    extractData: false,
  })
}

export function getAvsetinit(payload: {
}): Promise<any> {
  return http.post('AVSET_INIT', payload, {
    extractData: false,
  })
}

// 获取素材集列表数据
export function getAvsetData(payload: {
}): Promise<any> {
  return http.post('AVSET_DATA', payload, {
    extractData: false,
  })
}

export function getAvsetVideoQuery(payload: {
}): Promise<any> {
  return http.post('AVSET_VIDEO_QUERY', payload, {
    extractData: false,
  })
}

// 获取素材集任务列表
export function getTaskSet(payload: {
}): Promise<any> {
  return http.post('TASKSET_DATA', payload, {
    extractData: false,
  })
}

export function taskDownload(payload: {
}): Promise<any> {
  return http.post('TASK_DOWNLOAD', payload, {
    extractData: false,
  })
}
// CVTASK_DOWNLOAD
export function cvtaskDownload(payload: {
}): Promise<any> {
  return http.post('CVTASK_DOWNLOAD', payload, {
    extractData: false,
  })
}

export function avsetCreateTask(payload: {
}): Promise<any> {
  return http.post('AVSETTASK_CREATE', payload, {
    extractData: false,
  })
}

export function avsetInsertTask(payload: {
}): Promise<any> {
  return http.post('AVSETTASK_INSERT', payload, {
    extractData: false,
  })
}

export function insertMaterial(payload: {
}): Promise<any> {
  return http.post('AVSETMATERIAL_INSERT', payload, {
    extractData: false,
  })
}

export function deleteMaterial(payload: {
}): Promise<any> {
  return http.post('AVSETMATERIAL_DELETE', payload, {
    extractData: false,
  })
}
export function editMaterial(payload: {
}): Promise<any> {
  return http.post('AVSETMATERIAL_EDIT', payload, {
    extractData: false,
  })
}
export function getMaterialInfo(payload: {
}): Promise<any> {
  return http.post('FFPROBE', payload, {
    extractData: false,
  })
}
export function deleteTask(payload: {
}): Promise<any> {
  return http.post('AVTASK_DELETE', payload, {
    extractData: false,
  })
}

export function checkData(payload: {
}): Promise<any> {
  return http.post('TASKDETAIL_DATA', payload, {
    extractData: false,
  })
}

// cvcheckData
export function cvcheckData(payload: {
}): Promise<any> {
  return http.post('CVTASKDETAIL_DATA', payload, {
    extractData: false,
  })
}

// CV相关接口
// getCvsetinit
export function getCvsetinit(): Promise<any> {
  return http.get('CVSET_INIT', {
    extractData: false,
  })
}

// getCvsetData

export function getCvsetData(payload: {
}): Promise<any> {
  return http.post('CVSET_DATA', payload, {
    extractData: false,
  })
}
// cvsetCreateTask
export function cvsetCreateTask(payload: {
}): Promise<any> {
  return http.post('CVSETTASK_CREATE', payload, {
    extractData: false,
  })
}
// getCVTaskSet
export function getCVTaskSet(payload: {
}): Promise<any> {
  return http.post('CVTASKSET_DATA', payload, {
    extractData: false,
  })
}
