import { http } from '@xhs/launcher'

// dynamicSwitchConfig
export function dynamicSwitchConfig(params:unknown): Promise<any> {
  return http.get('DYNAMIC_SWITCH_CONFIG', {
    extractData: false,
  },
  { params })
}

// dynamicWarnInfo
export function dynamicWarnInfo(params:unknown): Promise<any> {
  return http.get('DYNAMIC_WARN_INFO', {
    extractData: false,
  },
  { params })
}

// distribute
export function distribute(params:unknown): Promise<any> {
  return http.get('DISTRIBUTE', {
    extractData: false,
  },
  { params })
}

// duration
export function duration(params:unknown): Promise<any> {
  return http.get('DURATION', {
    extractData: false,
  },
  { params })
}

// linkmicSwitch
export function linkmicSwitch(params:unknown): Promise<any> {
  return http.get('LINKMIC_SWITCH', {
    extractData: false,
  },
  { params })
}
