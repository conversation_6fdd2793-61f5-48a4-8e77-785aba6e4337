import { http } from '@xhs/launcher'

//  下面试档位配置接口
// 创建工作流码流配置信息
export function uploadWorkflowStreamtypeList(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/streamtype/upload', payload, {
    extractData: false,
  })
}

// 更新工作流码流配置信息
export function updateWorkflowStreamtypeList(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/streamtype/update', payload, {
    extractData: false,
  })
}

// 删除工作流码流配置信息
export function deleteWorkflowStreamtypeList(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/streamtype/delete', payload, {
    extractData: false,
  })
}

// 查询工作流码流配置信息
export function queryWorkflowStreamtypeList(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/streamtype/query', payload, {
    extractData: false,
  })
}

// 下面是媒体处理业务检查的执行记录接口
// 发布单个笔记
export function publishSingleNote(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/note/publish', payload, {
    extractData: false,
  })
}

// 检查note_media_check_record，检查笔记转码检测结果
export function checkNoteMediaCheckRecord(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/note/check', payload, {
    extractData: false,
  })
}

// 查询note_media_check_record，查询笔记转码检测结果
export function queryNoteMediaCheckRecord(payload: any): Promise<any> {
  return http.post('/api/testtask/avset/testtaskmaster/note/query', payload, {
    extractData: false,
  })
}

// 获取视频结果
export function getVideoResult(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_RESULT_SHOW', payload, {
    extractData: false,
  })
}

//  下面是图片检测的接口
// 获取图片分析统计
export function getVideoAnalysisStatistics(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_ANALYSIS_STATISTICS_GET', payload, {
    extractData: false,
  })
}
// 获取图片分析结果
export function getVideoAnalysisResult(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_ANALYSIS_RESULT_GET', payload, {
    extractData: false,
  })
}

// 更新图片分析结果
export function updateVideoAnalysisResult(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_ANALYSIS_RESULT_UPDATE', payload, {
    extractData: false,
  })
}

// 提交图片分析结果到 PingCode2
export function submitVideoAnalysisResultToPingCode2(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_ANALYSIS_SUBMIT_BUGS_TO_PINGCODE2', payload, {
    extractData: false,
  })
}

// 获取视频标注结果
export function getVideoDetectResult(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_DETECTION_RESULT_GET', payload, {
    extractData: false,
  })
}

// 获取视频标注统计
export function getVideoDetectStatistics(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_DETECTION_STATISTICS_GET', payload, {
    extractData: false,
  })
}

// 更新视频标注结果
export function updateVideoDetectResult(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_DETECTION_RESULT_UPDATE', payload, {
    extractData: false,
  })
}

// 提交视频标注结果到 PingCode2
export function submitVideoDetectResultToPingCode2(payload: {
}): Promise<any> {
  return http.post('XHS_VIDEO_DETECTION_SUBMIT_BUGS_TO_PINGCODE2', payload, {
    extractData: false,
  })
}
// 获取执行任务列表
export function getTaskList(payload: {
}): Promise<any> {
  return http.post('TASK_LIST', payload, {
    extractData: false,
  })
}
// 获取有序的执行任务列表
export function getTaskInOrderList(payload: {
}): Promise<any> {
  return http.post('TASKINORDER_LIST', payload, {
    extractData: false,
  })
}
// 获取执行任务记录列表
export function getTaskRecordList(payload: {
}): Promise<any> {
  return http.post('TASKRECORD_LIST', payload, {
    extractData: false,
  })
}
// 获取执行任务记录列表
export function getTaskRecordResult(payload: {
}): Promise<any> {
  return http.post('TASKRECORD_RESULTGET', payload, {
    extractData: false,
  })
}

// getTaskRecordListId
export function getTaskRecordListId(payload: {
}): Promise<any> {
  return http.post('TASKRECORD_LIST_ID', payload, {
    extractData: false,
  })
}

// 创建任务 createTask
export function createTask(payload: {
}): Promise<any> {
  return http.post('TASK_CREATE', payload, {
    extractData: false,
  })
}

// 删除任务 deleteTask
export function deleteTask(payload: {
}): Promise<any> {
  return http.post('TASK_DELETE', payload, {
    extractData: false,
  })
}

// 创建聚合任务 CreateMergeTask
export function CreateMergeTask(payload: {
}): Promise<any> {
  return http.post('MERGETASK_CREATE', payload, {
    extractData: false,
  })
}

// 获取聚合任务列表
export function getMergeTaskList(payload: {
}): Promise<any> {
  return http.post('MERGETASK_LIST', payload, {
    extractData: false,
  })
}

export function getTaskResultList(payload: {
}): Promise<any> {
  return http.post('TASKRESULT_LIST', payload, {
    extractData: false,
  })
}

// getTaskReportList
export function getTaskReportList(payload: {
}): Promise<any> {
  return http.post('TASKREPORT_LIST', payload, {
    extractData: false,
  })
}

// 设置默认任务字段
export function setTaskReportDefaultTaskField(payload: {
}): Promise<any> {
  return http.post('TASKREPORT_SETDEFAULTTASKFIELD', payload, {
    extractData: false,
  })
}

// 获取默认任务字段
export function getTaskReportDefaultTaskField(payload: {
}): Promise<any> {
  return http.post('TASKREPORT_DEFAULTTASKFIELD', payload, {
    extractData: false,
  })
}

// getTaskReportGather
export function getTaskReportGather(payload: {
}): Promise<any> {
  return http.post('TASKREPORT_GATHER', payload, {
    extractData: false,
  })
}

// executeTask
export function executeTask(payload: {
}): Promise<any> {
  return http.post('TASK_EXECUTE', payload, {
    extractData: false,
  })
}

// executeMergeTask
export function executeMergeTask(payload: {
}): Promise<any> {
  return http.post('MERGETASK_EXECUTE', payload, {
    extractData: false,
  })
}

// getMergeTaskRecordList
export function getMergeTaskRecordList(payload: {
}): Promise<any> {
  return http.post('MERGETASKREPORT_LIST', payload, {
    extractData: false,
  })
}

// templateMake
export function templateMake(payload: {
}): Promise<any> {
  return http.post('TEMPLATEMAKE', payload, {
    extractData: false,
  })
}

// MERGETASK_RESULTMAKE
export function getMergeTaskReportList(payload: {
}): Promise<any> {
  return http.post('MERGETASK_RESULTMAKE', payload, {
    extractData: false,
  })
}

// updateTask
export function updateTask(payload: {
}): Promise<any> {
  return http.post('TASK_UPDATE', payload, {
    extractData: false,
  })
}

// ResultUpdate
export function ResultUpdate(payload: {
}): Promise<any> {
  return http.post('RESULT_UPDATE', payload, {
    extractData: false,
  })
}

// ResultQuery
export function ResultQuery(payload: {
}): Promise<any> {
  return http.post('RESULT_QUERY', payload, {
    extractData: false,
  })
}

export function CVResultView(payload: {
}): Promise<any> {
  return http.post('CVRESULT_VIEW', payload, {
    extractData: false,
  })
}

export function CVResultViewDown(payload: {
}): Promise<any> {
  return http.post('CVRESULT_VIEWDOWN', payload, {
    extractData: false,
  })
}

// 编辑用例集
export function editMergeTask(payload: {
}): Promise<any> {
  return http.post('MERGETASK_EDIT', payload, {
    extractData: false,
  })
}
