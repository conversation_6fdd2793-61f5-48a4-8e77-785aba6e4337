import {
  CollectPicture, Tool, ApplicationTwo, VideoFile, ImageFiles, Drone, AdjacentItem, CategoryManagement, ListTwo, Log, Anchor,
  Analysis, ChartHistogramTwo, ActivitySource, DocDetail, AdProduct, Application, ApplicationOne, Pic,
  VideoOne,
} from '@xhs/delight/icons'
import { stream } from 'xlsx'

export default [
  // {
  //   description: '导航',
  // },
  {
    icon: Analysis,
    title: 'Dashboard',
    children: [
      {
        icon: ChartHistogramTwo,
        title: '首页',
        to: { name: 'index' },
      },
    ],
  },
  {
    icon: ApplicationTwo,
    title: '素材集',
    children: [
      {
        icon: VideoFile,
        title: '音视频',
        children: [
          {
            icon: Drone,
            title: '原子素材集',
            to: { name: 'AudioVideoAtom' },
          },
          {
            icon: AdjacentItem,
            title: '任务素材集',
            to: { name: 'AudioVideoTask' },
          },
        ],
        // to: { name: 'AudioVideo' },
      },

      // {
      //   icon: ImageFiles,
      //   title: '智能算法',
      //   // to: { name: 'CV' },
      //   children: [
      //     {
      //       icon: Drone,
      //       title: '原子素材集',
      //       to: { name: '<PERSON><PERSON><PERSON>' },
      //     },
      //     {
      //       icon: AdjacentItem,
      //       title: '任务素材集',
      //       to: { name: 'CVTask' },
      //     },
      //   ],
      // },
      // {
      //   icon: AdProduct,
      //   title: '竞品',
      //   to: { name: 'Competitor' },
      // },
      // {
      //   icon: Clipboard,
      //   title: '无参打分',
      //   to: { name: 'NoargumentsGrade' },
      // },
    ],
  },
  {
    icon: CategoryManagement,
    title: '用例管理',
    children: [
      {
        icon: ListTwo,
        title: '用例列表',
        to: { name: 'TaskList' },
      },
      {
        icon: Log,
        title: '用例运行记录',
        to: { name: 'TaskRecord' },
      },
    ],
  },
  {
    icon: AdjacentItem,
    title: '用例集管理',
    children: [
      {
        icon: ActivitySource,
        title: '用例集列表',
        to: { name: 'MergeTask' },
      },
      {
        icon: DocDetail,
        title: '用例集运行记录',
        to: { name: 'MergeTaskRecord' },
      },
    ],
  },
  // {
  //   icon: Application,
  //   title: '服务管理',
  //   children: [
  //     {
  //       icon: ApplicationOne,
  //       title: '服务列表',
  //       to: { name: 'ServiceList' },
  //     },
  //     {
  //       icon: Log,
  //       title: '服务详情',
  //       to: { name: 'RpcDetail' },
  //     },
  //   ],
  // },
  // {
  //   icon: AdProduct,
  //   title: 'APP性能工具',
  //   children: [
  //     {
  //       icon: ActivitySource,
  //       title: '任务列表',
  //       to: { name: 'AppPerfList' },
  //     },
  //   ],
  // },
  {
    icon: Tool,
    title: '测试工具',
    children: [
      {
        icon: Pic,
        title: '模型标注',
        to: { name: 'VideoMark' },
      },
      {
        icon: VideoOne,
        title: '视频标注',
        to: { name: 'VideoDetect' },
      },
      {
        icon: VideoFile,
        title: '视频查询',
        to: { name: 'VideoFetch' },
      },
      {
        icon: CollectPicture,
        title: '图片信息查看',
        to: { name: 'ImgInformation' },
      },
      {
        icon: Anchor,
        title: '图片尺寸修改',
        to: { name: 'ImgSizeModify' },
      },
      {
        icon: VideoFile,
        title: '转码测试',
        to: { name: 'TranscodeTest' },
      },
      {
        icon: VideoFile,
        title: '档位配置',
        to: { name: 'StreamConfig' },
      },
    ],
  },
  // {
  //   icon: Tool,
  //   title: '直播工具',
  //   children: [
  //     {
  //       icon: ListTwo,
  //       title: '直播动态开关',
  //       to: { name: 'DynamicSwitchConfig' },
  //     },
  //     {
  //       icon: ListTwo,
  //       title: '直播间违规',
  //       to: { name: 'DynamicWarnInfo' },
  //     },
  //     {
  //       icon: ListTwo,
  //       title: '直播可见范围',
  //       to: { name: 'Distribute' },
  //     },
  //     {
  //       icon: ListTwo,
  //       title: '直播时长',
  //       to: { name: 'Duration' },
  //     },
  //     {
  //       icon: ListTwo,
  //       title: '连麦开关',
  //       to: { name: 'LinkmicSwitch' },
  //     },
  //     {
  //       icon: ListTwo,
  //       title: '署币充值',
  //       to: { name: 'RedCoinTransaction' },
  //     },
  //     {
  //       icon: ListTwo,
  //       title: '直播声音',
  //       to: { name: 'ZhiBoVoice' },
  //     },
  //   ],
  // },
  // {
  //   icon: Application,
  //   title: '实验管理',
  //   children: [
  //     {
  //       icon: ApplicationOne,
  //       title: '实验列表',
  //       to: { name: 'Experiments' },
  //     },
  //   ],
  // },

  // {
  //   icon: ApplicationTwo,
  //   title: '通用',
  //   children: [
  //     {
  //       icon: Keyboard,
  //       title: '表单',
  //       to: { name: 'FormPage' },
  //     },
  //     {
  //       icon: InternalData,
  //       title: '表格',
  //       to: { name: 'TablePage' },
  //     },
  //   ],
  // },
  // {
  //   description: '其他',
  // },
  // {
  //   icon: Caution,
  //   title: '站点报错',
  //   to: { name: 'SiteError' },
  // },
]
