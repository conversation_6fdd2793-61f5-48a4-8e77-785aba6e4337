import { LauncherOptions } from '@xhs/launcher'
import AppLayout from '../layouts/AppLayout.vue'
import SiteError from '../pages/SiteError/SiteError.vue'
import Table from '../pages/list/Table.vue'
import Form from '../pages/form/Form.vue'
import AudioVideoAtom from '../pages/material/AudioVideoAtom.vue'
import AudioVideoTask from '../pages/material/AudioVideoTask.vue'
import TaskMaterialDetail from '../pages/material/TaskMaterialDetail.vue'
import NoargumentsGrade from '../pages/material/NoargumentsGrade.vue'
import Competitor from '../pages/material/Competitor.vue'
import <PERSON><PERSON>tom from '../pages/material/CVAtom.vue'
import CVTask from '../pages/material/CVTask.vue'
import TaskList from '../pages/task/TaskList.vue'
import MergeTask from '../pages/task/MergeTask.vue'
import MergeTaskRecord from '../pages/task/MergeTaskRecord.vue'
import MergeTaskReport from '../pages/task/MergeTaskReport.vue'
import EditMergeTask from '../pages/task/EditMergeTask.vue'
import TaskRecord from '../pages/task/TaskRecord.vue'
import TaskRecordID from '../pages/task/TaskRecordID.vue'
import TaskRecordDetail from '../pages/task/TaskRecordDetail.vue'
// import TaskReportDetail from '../pages/task/TaskReportDetail.vue'
import TaskCreate from '../pages/task/TaskCreate.vue'
import AppPerfList from '../pages/apptools/AppPerfList.vue'
import ServiceList from '../pages/servicemanage/ServiceList.vue'
import RpcDetail from '../pages/servicemanage/RpcDetail.vue'

import VideoFetch from '../pages/testtool/VideoFetch.vue'
import VideoMark from '../pages/testtool/VideoMark.vue'
import VideoDetect from '../pages/testtool/VideoDetect.vue'
import StreamConfig from '../pages/testtool/StreamConfig.vue'
import ImgInformation from '../pages/testtool/ImgInformation.vue'
import ImgSizeModify from '../pages/testtool/ImgSizeModify.vue'
import TranscodeTest from '../pages/testtool/TranscodeTest.vue'
import index from '../pages/dashboard/index.vue'
import DynamicSwitchConfig from '../pages/zhibo/DynamicSwitchConfig.vue'
import DynamicWarnInfo from '../pages/zhibo/DynamicWarnInfo.vue'
import Distribute from '../pages/zhibo/Distribute.vue'
import Duration from '../pages/zhibo/Duration.vue'
import LinkmicSwitch from '../pages/zhibo/LinkmicSwitch.vue'
import RedCoinTransaction from '../pages/zhibo/RedCoinTransaction.vue'
import ZhiBoVoice from '../pages/zhibo/ZhiBoVoice.vue'

import Experiments from '../pages/experiment/Experiments.vue'

const routes: LauncherOptions['routes'] = [
  {
    path: '',
    redirect: {
      name: 'index',
      // name: 'AudioVideoAtom',
    },
  },
  {
    path: '/site-error',
    name: 'SiteError',
    component: SiteError,
  },
  {
    name: 'AppLayout',
    path: '/',
    component: AppLayout,
    meta: {
      auth: {
        check: ['isLogin'],
      },
    },
    children: [
      {
        name: 'index',
        path: 'dashboard/index',
        meta: {
          title: 'Dashboard',
        },
        component: index,
      },
      {
        name: 'AudioVideoAtom',
        path: 'material/audiovideoatom',
        meta: {
          title: '原子素材集',
        },
        component: AudioVideoAtom,
      },
      {
        name: 'AudioVideoTask',
        path: 'material/audiovideotask',
        meta: {
          title: '任务素材集',
        },
        component: AudioVideoTask,
      },

      {
        name: 'TaskMaterialDetail',
        path: 'material/taskmaterialdetail/:id/:setType/:setName',
        // path: 'material/taskmaterialdetail?id=:id&setname=:setName',
        meta: {
          title: '任务素材详情',
        },
        component: TaskMaterialDetail,
      },
      {
        name: 'Competitor',
        path: 'material/competitor',
        meta: {
          title: '竞品',
        },
        component: Competitor,
      },
      {
        name: 'NoargumentsGrade',
        path: 'material/noargumentsgrade',
        meta: {
          title: '无参打分',
        },
        component: NoargumentsGrade,
      },
      {
        name: 'CVAtom',
        path: 'material/cvatom',
        meta: {
          title: '原子素材集',
        },
        component: CVAtom,
      },
      {
        name: 'CVTask',
        path: 'material/cvtask',
        meta: {
          title: '任务素材集',
        },
        component: CVTask,
      },
      {
        name: 'TaskList',
        path: 'task/tasklist',
        meta: {
          title: '用例列表',
        },
        component: TaskList,
      },
      {
        name: 'TaskRecord',
        path: 'task/taskrecord',
        meta: {
          title: '用例运行记录',
        },
        component: TaskRecord,
      },
      {
        name: 'TaskRecordID',
        path: 'task/taskrecord/:id',
        meta: {
          title: '用例运行记录',
        },
        component: TaskRecordID,
      },
      {
        name: 'TaskRecordDetail',
        path: 'task/taskrecorddetail/:id',
        meta: {
          title: '用例运行记录详情',
        },
        component: TaskRecordDetail,
        // props: ($route) => {
        //   return { nhjParamData: $route.params.ParamData, id: $route.params.id,taskType: $route.params.taskType};
        // },
      },
      {
        name: 'MergeTask',
        path: 'task/mergetask',
        meta: {
          title: '用例集列表',
        },
        component: MergeTask,
      },
      {
        name: 'MergeTaskRecord',
        path: 'task/mergetaskrecord',
        meta: {
          title: '用例集运行记录',
        },
        component: MergeTaskRecord,
      },
      {
        name: 'MergeTaskReport',
        path: 'task/mergetaskreport/:id',
        meta: {
          title: '用例集运行记录详情',
        },
        component: MergeTaskReport,
      },
      {
        name: 'EditMergeTask',
        path: 'task/editmergetask/:id',
        meta: {
          title: '编辑用例集',
        },
        component: EditMergeTask,
      },
      {
        name: 'TaskCreate',
        path: 'task/taskcreate',
        meta: {
          title: '任务创建',
        },
        component: TaskCreate,
      },
      {
        name: 'ServiceList',
        path: 'servicemanage/servicelist',
        meta: {
          title: '服务列表',
        },
        component: ServiceList,
      },
      {
        name: 'RpcDetail',
        path: 'servicemanage/rpcdetail',
        meta: {
          title: '服务详情',
        },
        component: RpcDetail,
      },
      {
        name: 'AppPerfList',
        path: 'apptools/appperflist',
        meta: {
          title: '任务列表',
        },
        component: AppPerfList,
      },
      {
        name: 'VideoFetch',
        path: 'testtool/videofetch',
        meta: {
          title: '视频查询',
        },
        component: VideoFetch,
      },
      {
        name: 'VideoMark',
        path: 'testtool/videomark',
        meta: {
          title: '视频标注',
        },
        component: VideoMark,
      },
      {
        name: 'VideoDetect',
        path: 'testtool/videodetect',
        meta: {
          title: '视频标注',
        },
        component: VideoDetect,
      },
      {
        name: 'StreamConfig',
        path: 'testtool/streamconfig',
        meta: {
          title: '档位配置',
        },
        component: StreamConfig,
      },
      {
        name: 'ImgInformation',
        path: 'testtool/imginformation',
        meta: {
          title: '图片信息查看',
        },
        component: ImgInformation,
      },
      {
        name: 'ImgSizeModify',
        path: 'testtool/imgsizemodify',
        meta: {
          title: '图片信息查看',
        },
        component: ImgSizeModify,
      },
      {
        name: 'TranscodeTest',
        path: 'testtool/transcodetest',
        meta: {
          title: '转码测试',
        },
        component: TranscodeTest,
      },
      {
        name: 'DynamicSwitchConfig',
        path: 'zhibo/dynamicswitchconfig',
        meta: {
          title: '直播动态开关',
        },
        component: DynamicSwitchConfig,
      },
      {
        name: 'DynamicWarnInfo',
        path: 'zhibo/dynamicwarninfo',
        meta: {
          title: '直播间违规',
        },
        component: DynamicWarnInfo,
      },
      {
        name: 'Distribute',
        path: 'zhibo/distribute',
        meta: {
          title: '直播可见范围',
        },
        component: Distribute,
      },
      {
        name: 'Duration',
        path: 'zhibo/duration',
        meta: {
          title: '直播时长',
        },
        component: Duration,
      },
      {
        name: 'LinkmicSwitch',
        path: 'zhibo/linkmicswitch',
        meta: {
          title: '连麦开关',
        },
        component: LinkmicSwitch,
      },
      {
        name: 'RedCoinTransaction',
        path: 'zhibo/redcointransaction',
        meta: {
          title: '署币充值',
        },
        component: RedCoinTransaction,
      },
      {
        name: 'ZhiBoVoice',
        path: 'zhibo/zhibovoice',
        meta: {
          title: '直播声音',
        },
        component: ZhiBoVoice,
      },
      {
        name: 'Experiments',
        path: 'experiment/experiments',
        meta: {
          title: '实验列表',
        },
        component: Experiments,
      },

      {
        name: 'FormPage',
        path: 'form',
        meta: {
          title: '表单',
        },
        component: Form,
      },
      {
        name: 'TablePage',
        path: 'table',
        meta: {
          title: '表格',
        },
        component: Table,
      },
    ],
  },
]

export default routes
