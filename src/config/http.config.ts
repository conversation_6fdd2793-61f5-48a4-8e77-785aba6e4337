import { HttpConfig } from '@xhs/launcher'

const httpConfig: HttpConfig = {
  BASE_URL: {
    // development: 'https://logan.devops.xiaohongshu.com/proxy/fevideo',
    // development: 'http://10.30.41.137:8585',
    // development: 'https://va.devops.sit.xiaohongshu.com',
    development: 'http://127.0.0.1:8585',
    // formula build -e test
    // test: 'https://10.31.175.116:8585',
    // development: 'http://192.168.31.189:8585',
    test: '',
    // formula build -e prerelease
    prerelease: '',
    // formula build
    production: '',
  },
  BASE_CONFIG: {
    defaults: {
      transform: false,
      timeout: 30000,
    },
    development: {
      withCredentials: true,
    },
    test: {
      withCredentials: true,
    },
  },
  API_LIST: {
    LOGIN_SSO: '/api/user/login',
    LOGOUT: '/api/user/logout',
    USER_INFO: '/api/user/info',

    TABLE: '/data/sns/template_table',

    XHS_GET_COS_KEY: '/api/xhsproject/tools/coskey/get',
    // XHS_UPLOAD_VIDEO: '/api/xhsproject/tools/video/upload',
    XHS_VIDEO_INSERT: '/api/xhsproject/tools/video/insert',
    XHS_VIDEO_RESULT_SHOW: '/api/xhsproject/tools/video/result/show',
    // 图片分析接口
    XHS_VIDEO_ANALYSIS_STATISTICS_GET: '/api/xhsproject/tools/video/analysis/statistics/get',
    XHS_VIDEO_ANALYSIS_RESULT_GET: '/api/xhsproject/tools/video/analysis/result/get',
    XHS_VIDEO_ANALYSIS_RESULT_UPDATE: '/api/xhsproject/tools/video/analysis/result/update',
    XHS_VIDEO_ANALYSIS_SUBMIT_BUGS_TO_PINGCODE2: '/api/xhsproject/tools/video/analysis/submitBugsToPingcode2',
    // 视频标注接口
    XHS_VIDEO_DETECTION_STATISTICS_GET: '/api/xhsproject/tools/video/detect/statistics/get',
    XHS_VIDEO_DETECTION_RESULT_GET: '/api/xhsproject/tools/video/detect/result/get',
    XHS_VIDEO_DETECTION_RESULT_UPDATE: '/api/xhsproject/tools/video/detect/result/update',
    XHS_VIDEO_DETECTION_SUBMIT_BUGS_TO_PINGCODE2: '/api/xhsproject/tools/video/detect/submitBugsToPingcode2',

    XHS_GENERATE_TASK_MATERIAL_DAILY: '/api/workflow/execute/generate_task_material_daily',

    // 素材集
    AVSET_INIT: '/api/material/avset/init',
    AVSET_DATA: '/api/material/avset/query',
    AVSET_VIDEO_QUERY: '/api/material/avset/video/query',
    TASKSET_DATA: '/api/material/avset/testtask/query',
    TASK_DOWNLOAD: '/api/material/avset/testtask/down',
    AVSETTASK_CREATE: '/api/material/avset/testtask/create',
    AVSETTASK_INSERT: '/api/material/avset/testtask/insert',
    TASKDETAIL_DATA: '/api/material/avset/testtask/check',
    AVSETMATERIAL_INSERT: '/api/material/avset/insert',
    AVSETMATERIAL_DELETE: '/api/material/avset/delete',
    AVSETMATERIAL_EDIT: '/api/material/avset/update',
    FFPROBE: '/api/material/avset/ffprobe',
    AVTASK_DELETE: '/api/material/avset/testtask/delete',

    // CV素材集合
    CVSET_INIT: '/api/material/cvset/init',
    CVSET_DATA: '/api/material/cvset/query',
    CVSETTASK_CREATE: '/api/material/cvset/testtask/create',
    CVTASKSET_DATA: '/api/material/cvset/testtask/query',
    CVTASKDETAIL_DATA: '/api/material/cvset/testtask/check',
    CVTASK_DOWNLOAD: '/api/material/cvset/testtask/down',

    // 任务
    TASK_LIST: '/api/testtask/avset/testtaskmaster/testtaskquery', /// //获取任务列表
    TASKINORDER_LIST: '/api/testtask/avset/testtaskmaster/testtaskqueryinorder', /// //获取有序的任务列表
    TASKRECORD_LIST: '/api/testtask/avset/testtaskmaster/executerecord/query', /// //获取任务记录列表
    TASKRECORD_RESULTGET: '/api/testtask/avset/testtaskmaster/executerecord/resultget', /// //获取任务记录列表
    TASKRECORD_LIST_ID: '/api/testtask/avset/testtaskmaster/executerecord/single/query',
    TASK_CREATE: '/api/testtask/avset/testtaskmaster/testtaskcreate',
    TASK_DELETE: '/api/testtask/avset/testtaskmaster/testtaskdelete',
    TASKRESULT_LIST: '/api/testtask/avset/testtaskmaster/execute/resultcheck',
    TASK_EXECUTE: '/api/testtask/avset/testtaskmaster/execute',
    TEMPLATEMAKE: '/api/testtask/avset/testtaskmaster/templatemake', // 获取简单模式JSON模板
    TASK_UPDATE: '/api/testtask/avset/testtaskmaster/taskeasyparaupdate',
    TASKREPORT_LIST: '/api/testtask/avset/testtaskmaster/resultmake', // 获取报告数据
    TASKREPORT_SETDEFAULTTASKFIELD: '/api/testtask/avset/testtaskmaster/setdefaulttaskfield',
    TASKREPORT_DEFAULTTASKFIELD: '/api/testtask/avset/testtaskmaster/querydefaulttaskfield',
    TASKREPORT_GATHER: '/api/testtask/avset/testtaskmaster/resultgather', // 获取报告数据
    RESULT_UPDATE: '/api/testtask/avset/testtaskmaster/resultupdate', // 获取报告过程数据
    RESULT_QUERY: '/api/testtask/avset/testtaskmaster/resultquery', // 查询报告过程数据
    CVRESULT_VIEW: '/api/testtask/avset/testtaskmaster/execute/imgpoint', // CV可视化
    CVRESULT_VIEWDOWN: '/api/testtask/avset/testtaskmaster/execute/imgpointdown', // CV可视化
    MERGETASK_CREATE: '/api/testtask/avset/testtaskmaster/juhetask/juhetaskcreate', // 创建聚合任务
    MERGETASK_LIST: '/api/testtask/avset/testtaskmaster/juhetask/juhetaskquery',
    MERGETASK_EXECUTE: '/api/testtask/avset/testtaskmaster/juhetask/juheteskexcute',
    MERGETASKREPORT_LIST: '/api/testtask/avset/testtaskmaster/juhetask/jhteskexcrecquery',
    MERGETASK_RESULTMAKE: '/api/testtask/avset/testtaskmaster/juhetask/juheteskresultmake',
    MERGETASK_EDIT: '/api/testtask/avset/testtaskmaster/juhetask/juhetaskupdate',

    // app相关
    TASKLISTDISPLAY: '/api/vatest/perftest/TaskListDisplay',
    PERFTESTSTART: '/api/vatest/perftest/perfTestStart',
    PERFTESTEND: '/api/vatest/perftest/perfTestEnd',
    SINGLETASKDISPLAY: '/api/vatest/perftest/singleTaskDisplay',
    GETSHISHIRESULT: '/api/vatest/perftest/getshishiresult',

    // 服务管理api/vqa/sourceRegister/create',views.sourceRegisterCreate
    SERVICEINFOLIST: '/api/vqa/sourceRegister/query',
    RPCCREATE: '/api/vqa/sourceRegister/create',
    SOURCEGETTEMPLATE: '/api/vqa/sourceRegister/getTemplate',
    DELETERPC: '/api/vqa/sourceRegister/delete',
    UPDATERPC: '/api/vqa/sourceRegister/update',
    RPCRUN: '/api/vqa/serviceRun/rpcRun',

    // 工具类
    UPLOAD_FILE: '/api/testtask/avset/imagetools/upload', // 上传文件
    MODIFY_IMG: '/api/testtask/avset/imagetools/imgabilitydeal',

    // Dashboard
    VATEST_STATCOUNTDATA: '/api/dashboard/getvacount', // 使用数据
    VATEST_STATINFODATA: '/api/dashboard/getvastatinfo', // 使用数据

    // 直播
    DYNAMIC_SWITCH_CONFIG: '/zhibo/getDynamicSwitchConfig',
    DYNAMIC_WARN_INFO: '/zhibo/getLiveDynamicWarnInfo',
    DISTRIBUTE: '/zhibo/getLiveInfo',
    DURATION: '/zhibo/getLiveInfo',
    LINKMIC_SWITCH: '/zhibo/getLinkmicSwitchByHost',
  },
}

export default httpConfig
