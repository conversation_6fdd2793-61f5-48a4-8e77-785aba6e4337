export const SSO_HOSTS: Record<string, string> = {
  development: 'http://login2.sit.xiaohongshu.com',
  test: 'http://login2.sit.xiaohongshu.com',
  prerelease: 'https://login2.xiaohongshu.com',
  production: 'https://login2.xiaohongshu.com',
}

const host = SSO_HOSTS[process.env.BUILD_ENV || 'development']

export function getPorchLoginUrl(): string {
  return `${host}?service=${window.location.origin}`
}

export function getPorchLogoutUrl(): string {
  return `${host}/logout?service=${window.location.origin}`
}
