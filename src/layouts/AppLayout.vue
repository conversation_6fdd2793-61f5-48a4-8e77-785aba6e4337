<template>
  <Layout>
    <template #top>
      <Topbar>
        <template #title>
          <Space size="small">
            <RedLogo />
            <Text
              type="h4"
              bold
            >
              音视频测试平台
            </Text>
          </Space>
        </template>
        <Space
          block
          justify="end"
        >
          <!-- <Menu
            mode="horizontal"
            :menu="menu"
          /> -->
          <div style="display:table">
            <div style="display:table-cell;vertical-align:middle; ">
              <Avatar src="http://fe-static.xhscdn.com/formula-static/ones/public/img/user.2a4c4ae.jpg" />
            </div>
            <div style="display:table-cell;vertical-align:middle; ">
              <Button
                type="light"
                style=""
              >
                {{ userName }}
              </Button> </div>
          </div>
        </Space>
      </Topbar>
    </template>
    <template #left>
      <Menu
        :style="{ height: '100%' }"
        :menu="menu"
      />
    </template>
    <router-view />
  </Layout>
</template>

<script setup lang="ts">
  import {
    Layout, Space, Topbar, <PERSON>u, Text, Button, Avatar,
  } from '@xhs/delight'
  import { Presets } from '@xhs/delight/icons'
  import { useStore } from 'vuex'
  import menu from '../config/menus.config'

  const { RedLogo } = Presets

  const store = useStore()

  const userName = store.state.Auth.userInfo.email ? store.state.Auth.userInfo.email.split('@')[0] : '路人'

</script>

<style lang="stylus">
body
  background-color var(--color-white) !important
#app
  height 100vh
</style>
